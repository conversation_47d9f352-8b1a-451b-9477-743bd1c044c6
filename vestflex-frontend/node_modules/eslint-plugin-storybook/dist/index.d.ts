import * as _typescript_eslint_utils_ts_eslint from '@typescript-eslint/utils/ts-eslint';

declare const _default: {
    configs: {
        csf: {
            plugins: string[];
            overrides: ({
                files: string[];
                rules: {
                    readonly 'react-hooks/rules-of-hooks': "off";
                    readonly 'import/no-anonymous-default-export': "off";
                    readonly 'storybook/csf-component': "warn";
                    readonly 'storybook/default-exports': "error";
                    readonly 'storybook/hierarchy-separator': "warn";
                    readonly 'storybook/no-redundant-story-name': "warn";
                    readonly 'storybook/story-exports': "error";
                    readonly 'storybook/no-uninstalled-addons'?: undefined;
                };
            } | {
                files: string[];
                rules: {
                    readonly 'storybook/no-uninstalled-addons': "error";
                    readonly 'react-hooks/rules-of-hooks'?: undefined;
                    readonly 'import/no-anonymous-default-export'?: undefined;
                    readonly 'storybook/csf-component'?: undefined;
                    readonly 'storybook/default-exports'?: undefined;
                    readonly 'storybook/hierarchy-separator'?: undefined;
                    readonly 'storybook/no-redundant-story-name'?: undefined;
                    readonly 'storybook/story-exports'?: undefined;
                };
            })[];
        };
        'csf-strict': {
            extends: string;
            rules: {
                readonly 'react-hooks/rules-of-hooks': "off";
                readonly 'import/no-anonymous-default-export': "off";
                readonly 'storybook/no-stories-of': "error";
                readonly 'storybook/no-title-property-in-meta': "error";
            };
        };
        'addon-interactions': {
            plugins: string[];
            overrides: ({
                files: string[];
                rules: {
                    readonly 'react-hooks/rules-of-hooks': "off";
                    readonly 'import/no-anonymous-default-export': "off";
                    readonly 'storybook/await-interactions': "error";
                    readonly 'storybook/context-in-play-function': "error";
                    readonly 'storybook/use-storybook-expect': "error";
                    readonly 'storybook/use-storybook-testing-library': "error";
                    readonly 'storybook/no-uninstalled-addons'?: undefined;
                };
            } | {
                files: string[];
                rules: {
                    readonly 'storybook/no-uninstalled-addons': "error";
                    readonly 'react-hooks/rules-of-hooks'?: undefined;
                    readonly 'import/no-anonymous-default-export'?: undefined;
                    readonly 'storybook/await-interactions'?: undefined;
                    readonly 'storybook/context-in-play-function'?: undefined;
                    readonly 'storybook/use-storybook-expect'?: undefined;
                    readonly 'storybook/use-storybook-testing-library'?: undefined;
                };
            })[];
        };
        recommended: {
            plugins: string[];
            overrides: ({
                files: string[];
                rules: {
                    readonly 'react-hooks/rules-of-hooks': "off";
                    readonly 'import/no-anonymous-default-export': "off";
                    readonly 'storybook/await-interactions': "error";
                    readonly 'storybook/context-in-play-function': "error";
                    readonly 'storybook/default-exports': "error";
                    readonly 'storybook/hierarchy-separator': "warn";
                    readonly 'storybook/no-redundant-story-name': "warn";
                    readonly 'storybook/no-renderer-packages': "error";
                    readonly 'storybook/prefer-pascal-case': "warn";
                    readonly 'storybook/story-exports': "error";
                    readonly 'storybook/use-storybook-expect': "error";
                    readonly 'storybook/use-storybook-testing-library': "error";
                    readonly 'storybook/no-uninstalled-addons'?: undefined;
                };
            } | {
                files: string[];
                rules: {
                    readonly 'storybook/no-uninstalled-addons': "error";
                    readonly 'react-hooks/rules-of-hooks'?: undefined;
                    readonly 'import/no-anonymous-default-export'?: undefined;
                    readonly 'storybook/await-interactions'?: undefined;
                    readonly 'storybook/context-in-play-function'?: undefined;
                    readonly 'storybook/default-exports'?: undefined;
                    readonly 'storybook/hierarchy-separator'?: undefined;
                    readonly 'storybook/no-redundant-story-name'?: undefined;
                    readonly 'storybook/no-renderer-packages'?: undefined;
                    readonly 'storybook/prefer-pascal-case'?: undefined;
                    readonly 'storybook/story-exports'?: undefined;
                    readonly 'storybook/use-storybook-expect'?: undefined;
                    readonly 'storybook/use-storybook-testing-library'?: undefined;
                };
            })[];
        };
        'flat/csf': ({
            name: string;
            plugins: {
                readonly storybook: any;
            };
            files?: undefined;
            rules?: undefined;
        } | {
            name: string;
            files: string[];
            rules: {
                readonly 'react-hooks/rules-of-hooks': "off";
                readonly 'import/no-anonymous-default-export': "off";
                readonly 'storybook/csf-component': "warn";
                readonly 'storybook/default-exports': "error";
                readonly 'storybook/hierarchy-separator': "warn";
                readonly 'storybook/no-redundant-story-name': "warn";
                readonly 'storybook/story-exports': "error";
                readonly 'storybook/no-uninstalled-addons'?: undefined;
            };
            plugins?: undefined;
        } | {
            name: string;
            files: string[];
            rules: {
                readonly 'storybook/no-uninstalled-addons': "error";
                readonly 'react-hooks/rules-of-hooks'?: undefined;
                readonly 'import/no-anonymous-default-export'?: undefined;
                readonly 'storybook/csf-component'?: undefined;
                readonly 'storybook/default-exports'?: undefined;
                readonly 'storybook/hierarchy-separator'?: undefined;
                readonly 'storybook/no-redundant-story-name'?: undefined;
                readonly 'storybook/story-exports'?: undefined;
            };
            plugins?: undefined;
        })[];
        'flat/csf-strict': ({
            name: string;
            plugins: {
                readonly storybook: any;
            };
            files?: undefined;
            rules?: undefined;
        } | {
            name: string;
            files: string[];
            rules: {
                readonly 'react-hooks/rules-of-hooks': "off";
                readonly 'import/no-anonymous-default-export': "off";
                readonly 'storybook/csf-component': "warn";
                readonly 'storybook/default-exports': "error";
                readonly 'storybook/hierarchy-separator': "warn";
                readonly 'storybook/no-redundant-story-name': "warn";
                readonly 'storybook/story-exports': "error";
                readonly 'storybook/no-uninstalled-addons'?: undefined;
            };
            plugins?: undefined;
        } | {
            name: string;
            files: string[];
            rules: {
                readonly 'storybook/no-uninstalled-addons': "error";
                readonly 'react-hooks/rules-of-hooks'?: undefined;
                readonly 'import/no-anonymous-default-export'?: undefined;
                readonly 'storybook/csf-component'?: undefined;
                readonly 'storybook/default-exports'?: undefined;
                readonly 'storybook/hierarchy-separator'?: undefined;
                readonly 'storybook/no-redundant-story-name'?: undefined;
                readonly 'storybook/story-exports'?: undefined;
            };
            plugins?: undefined;
        } | {
            name: string;
            rules: {
                readonly 'react-hooks/rules-of-hooks': "off";
                readonly 'import/no-anonymous-default-export': "off";
                readonly 'storybook/no-stories-of': "error";
                readonly 'storybook/no-title-property-in-meta': "error";
            };
        })[];
        'flat/addon-interactions': ({
            name: string;
            plugins: {
                readonly storybook: any;
            };
            files?: undefined;
            rules?: undefined;
        } | {
            name: string;
            files: string[];
            rules: {
                readonly 'react-hooks/rules-of-hooks': "off";
                readonly 'import/no-anonymous-default-export': "off";
                readonly 'storybook/await-interactions': "error";
                readonly 'storybook/context-in-play-function': "error";
                readonly 'storybook/use-storybook-expect': "error";
                readonly 'storybook/use-storybook-testing-library': "error";
                readonly 'storybook/no-uninstalled-addons'?: undefined;
            };
            plugins?: undefined;
        } | {
            name: string;
            files: string[];
            rules: {
                readonly 'storybook/no-uninstalled-addons': "error";
                readonly 'react-hooks/rules-of-hooks'?: undefined;
                readonly 'import/no-anonymous-default-export'?: undefined;
                readonly 'storybook/await-interactions'?: undefined;
                readonly 'storybook/context-in-play-function'?: undefined;
                readonly 'storybook/use-storybook-expect'?: undefined;
                readonly 'storybook/use-storybook-testing-library'?: undefined;
            };
            plugins?: undefined;
        })[];
        'flat/recommended': ({
            name: string;
            plugins: {
                readonly storybook: any;
            };
            files?: undefined;
            rules?: undefined;
        } | {
            name: string;
            files: string[];
            rules: {
                readonly 'react-hooks/rules-of-hooks': "off";
                readonly 'import/no-anonymous-default-export': "off";
                readonly 'storybook/await-interactions': "error";
                readonly 'storybook/context-in-play-function': "error";
                readonly 'storybook/default-exports': "error";
                readonly 'storybook/hierarchy-separator': "warn";
                readonly 'storybook/no-redundant-story-name': "warn";
                readonly 'storybook/no-renderer-packages': "error";
                readonly 'storybook/prefer-pascal-case': "warn";
                readonly 'storybook/story-exports': "error";
                readonly 'storybook/use-storybook-expect': "error";
                readonly 'storybook/use-storybook-testing-library': "error";
                readonly 'storybook/no-uninstalled-addons'?: undefined;
            };
            plugins?: undefined;
        } | {
            name: string;
            files: string[];
            rules: {
                readonly 'storybook/no-uninstalled-addons': "error";
                readonly 'react-hooks/rules-of-hooks'?: undefined;
                readonly 'import/no-anonymous-default-export'?: undefined;
                readonly 'storybook/await-interactions'?: undefined;
                readonly 'storybook/context-in-play-function'?: undefined;
                readonly 'storybook/default-exports'?: undefined;
                readonly 'storybook/hierarchy-separator'?: undefined;
                readonly 'storybook/no-redundant-story-name'?: undefined;
                readonly 'storybook/no-renderer-packages'?: undefined;
                readonly 'storybook/prefer-pascal-case'?: undefined;
                readonly 'storybook/story-exports'?: undefined;
                readonly 'storybook/use-storybook-expect'?: undefined;
                readonly 'storybook/use-storybook-testing-library'?: undefined;
            };
            plugins?: undefined;
        })[];
    };
    rules: {
        'await-interactions': _typescript_eslint_utils_ts_eslint.RuleModule<"interactionShouldBeAwaited" | "fixSuggestion", never[], unknown, _typescript_eslint_utils_ts_eslint.RuleListener>;
        'context-in-play-function': _typescript_eslint_utils_ts_eslint.RuleModule<"passContextToPlayFunction", never[], unknown, _typescript_eslint_utils_ts_eslint.RuleListener>;
        'csf-component': _typescript_eslint_utils_ts_eslint.RuleModule<"missingComponentProperty", never[], unknown, _typescript_eslint_utils_ts_eslint.RuleListener>;
        'default-exports': _typescript_eslint_utils_ts_eslint.RuleModule<"fixSuggestion" | "shouldHaveDefaultExport", never[], unknown, _typescript_eslint_utils_ts_eslint.RuleListener>;
        'hierarchy-separator': _typescript_eslint_utils_ts_eslint.RuleModule<"useCorrectSeparators" | "deprecatedHierarchySeparator", never[], unknown, _typescript_eslint_utils_ts_eslint.RuleListener>;
        'meta-inline-properties': _typescript_eslint_utils_ts_eslint.RuleModule<"metaShouldHaveInlineProperties", {
            csfVersion: number;
        }[], unknown, _typescript_eslint_utils_ts_eslint.RuleListener>;
        'meta-satisfies-type': _typescript_eslint_utils_ts_eslint.RuleModule<"metaShouldSatisfyType", never[], unknown, _typescript_eslint_utils_ts_eslint.RuleListener>;
        'no-redundant-story-name': _typescript_eslint_utils_ts_eslint.RuleModule<"removeRedundantName" | "storyNameIsRedundant", never[], unknown, _typescript_eslint_utils_ts_eslint.RuleListener>;
        'no-renderer-packages': _typescript_eslint_utils_ts_eslint.RuleModule<"noRendererPackages", readonly [], unknown, _typescript_eslint_utils_ts_eslint.RuleListener>;
        'no-stories-of': _typescript_eslint_utils_ts_eslint.RuleModule<"doNotUseStoriesOf", never[], unknown, _typescript_eslint_utils_ts_eslint.RuleListener>;
        'no-title-property-in-meta': _typescript_eslint_utils_ts_eslint.RuleModule<"removeTitleInMeta" | "noTitleInMeta", never[], unknown, _typescript_eslint_utils_ts_eslint.RuleListener>;
        'no-uninstalled-addons': _typescript_eslint_utils_ts_eslint.RuleModule<"addonIsNotInstalled", {
            packageJsonLocation: string;
            ignore: string[];
        }[], unknown, _typescript_eslint_utils_ts_eslint.RuleListener>;
        'prefer-pascal-case': _typescript_eslint_utils_ts_eslint.RuleModule<"convertToPascalCase" | "usePascalCase", never[], unknown, _typescript_eslint_utils_ts_eslint.RuleListener>;
        'story-exports': _typescript_eslint_utils_ts_eslint.RuleModule<"shouldHaveStoryExport" | "shouldHaveStoryExportWithFilters" | "addStoryExport", never[], unknown, _typescript_eslint_utils_ts_eslint.RuleListener>;
        'use-storybook-expect': _typescript_eslint_utils_ts_eslint.RuleModule<string, {
            storybookJestPath?: string;
        }[], unknown, _typescript_eslint_utils_ts_eslint.RuleListener>;
        'use-storybook-testing-library': _typescript_eslint_utils_ts_eslint.RuleModule<"updateImports" | "dontUseTestingLibraryDirectly", never[], unknown, _typescript_eslint_utils_ts_eslint.RuleListener>;
    };
};

export { _default as default };
