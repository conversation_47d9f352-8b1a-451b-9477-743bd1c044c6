declare const _default: ({
    name: string;
    plugins: {
        readonly storybook: any;
    };
    files?: undefined;
    rules?: undefined;
} | {
    name: string;
    files: string[];
    rules: {
        readonly 'react-hooks/rules-of-hooks': "off";
        readonly 'import/no-anonymous-default-export': "off";
        readonly 'storybook/await-interactions': "error";
        readonly 'storybook/context-in-play-function': "error";
        readonly 'storybook/default-exports': "error";
        readonly 'storybook/hierarchy-separator': "warn";
        readonly 'storybook/no-redundant-story-name': "warn";
        readonly 'storybook/no-renderer-packages': "error";
        readonly 'storybook/prefer-pascal-case': "warn";
        readonly 'storybook/story-exports': "error";
        readonly 'storybook/use-storybook-expect': "error";
        readonly 'storybook/use-storybook-testing-library': "error";
        readonly 'storybook/no-uninstalled-addons'?: undefined;
    };
    plugins?: undefined;
} | {
    name: string;
    files: string[];
    rules: {
        readonly 'storybook/no-uninstalled-addons': "error";
        readonly 'react-hooks/rules-of-hooks'?: undefined;
        readonly 'import/no-anonymous-default-export'?: undefined;
        readonly 'storybook/await-interactions'?: undefined;
        readonly 'storybook/context-in-play-function'?: undefined;
        readonly 'storybook/default-exports'?: undefined;
        readonly 'storybook/hierarchy-separator'?: undefined;
        readonly 'storybook/no-redundant-story-name'?: undefined;
        readonly 'storybook/no-renderer-packages'?: undefined;
        readonly 'storybook/prefer-pascal-case'?: undefined;
        readonly 'storybook/story-exports'?: undefined;
        readonly 'storybook/use-storybook-expect'?: undefined;
        readonly 'storybook/use-storybook-testing-library'?: undefined;
    };
    plugins?: undefined;
})[];

export { _default as default };
