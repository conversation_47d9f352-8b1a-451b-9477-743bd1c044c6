declare const _default: ({
    name: string;
    plugins: {
        readonly storybook: any;
    };
    files?: undefined;
    rules?: undefined;
} | {
    name: string;
    files: string[];
    rules: {
        readonly 'react-hooks/rules-of-hooks': "off";
        readonly 'import/no-anonymous-default-export': "off";
        readonly 'storybook/await-interactions': "error";
        readonly 'storybook/context-in-play-function': "error";
        readonly 'storybook/use-storybook-expect': "error";
        readonly 'storybook/use-storybook-testing-library': "error";
        readonly 'storybook/no-uninstalled-addons'?: undefined;
    };
    plugins?: undefined;
} | {
    name: string;
    files: string[];
    rules: {
        readonly 'storybook/no-uninstalled-addons': "error";
        readonly 'react-hooks/rules-of-hooks'?: undefined;
        readonly 'import/no-anonymous-default-export'?: undefined;
        readonly 'storybook/await-interactions'?: undefined;
        readonly 'storybook/context-in-play-function'?: undefined;
        readonly 'storybook/use-storybook-expect'?: undefined;
        readonly 'storybook/use-storybook-testing-library'?: undefined;
    };
    plugins?: undefined;
})[];

export { _default as default };
