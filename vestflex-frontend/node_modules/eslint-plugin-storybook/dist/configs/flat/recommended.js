"use strict";var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __esm=(fn,res)=>function(){return fn&&(res=(0,fn[__getOwnPropNames(fn)[0]])(fn=0)),res};var __commonJS=(cb,mod)=>function(){return mod||(0,cb[__getOwnPropNames(cb)[0]])((mod={exports:{}}).exports,mod),mod.exports};var __copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod));var require_addon_interactions=__commonJS({"src/configs/addon-interactions.ts"(exports2,module2){"use strict";module2.exports={plugins:["storybook"],overrides:[{files:["**/*.stories.@(ts|tsx|js|jsx|mjs|cjs)","**/*.story.@(ts|tsx|js|jsx|mjs|cjs)"],rules:{"react-hooks/rules-of-hooks":"off","import/no-anonymous-default-export":"off","storybook/await-interactions":"error","storybook/context-in-play-function":"error","storybook/use-storybook-expect":"error","storybook/use-storybook-testing-library":"error"}},{files:[".storybook/main.@(js|cjs|mjs|ts)"],rules:{"storybook/no-uninstalled-addons":"error"}}]}}});var require_csf=__commonJS({"src/configs/csf.ts"(exports2,module2){"use strict";module2.exports={plugins:["storybook"],overrides:[{files:["**/*.stories.@(ts|tsx|js|jsx|mjs|cjs)","**/*.story.@(ts|tsx|js|jsx|mjs|cjs)"],rules:{"react-hooks/rules-of-hooks":"off","import/no-anonymous-default-export":"off","storybook/csf-component":"warn","storybook/default-exports":"error","storybook/hierarchy-separator":"warn","storybook/no-redundant-story-name":"warn","storybook/story-exports":"error"}},{files:[".storybook/main.@(js|cjs|mjs|ts)"],rules:{"storybook/no-uninstalled-addons":"error"}}]}}});var require_csf_strict=__commonJS({"src/configs/csf-strict.ts"(exports2,module2){"use strict";module2.exports={extends:"./configs/csf",rules:{"react-hooks/rules-of-hooks":"off","import/no-anonymous-default-export":"off","storybook/no-stories-of":"error","storybook/no-title-property-in-meta":"error"}}}});var require_addon_interactions2=__commonJS({"src/configs/flat/addon-interactions.ts"(exports2,module2){"use strict";module2.exports=[{name:"storybook:addon-interactions:setup",plugins:{get storybook(){return require_index()}}},{name:"storybook:addon-interactions:stories-rules",files:["**/*.stories.@(ts|tsx|js|jsx|mjs|cjs)","**/*.story.@(ts|tsx|js|jsx|mjs|cjs)"],rules:{"react-hooks/rules-of-hooks":"off","import/no-anonymous-default-export":"off","storybook/await-interactions":"error","storybook/context-in-play-function":"error","storybook/use-storybook-expect":"error","storybook/use-storybook-testing-library":"error"}},{name:"storybook:addon-interactions:main-rules",files:[".storybook/main.@(js|cjs|mjs|ts)"],rules:{"storybook/no-uninstalled-addons":"error"}}]}});var require_csf2=__commonJS({"src/configs/flat/csf.ts"(exports2,module2){"use strict";module2.exports=[{name:"storybook:csf:setup",plugins:{get storybook(){return require_index()}}},{name:"storybook:csf:stories-rules",files:["**/*.stories.@(ts|tsx|js|jsx|mjs|cjs)","**/*.story.@(ts|tsx|js|jsx|mjs|cjs)"],rules:{"react-hooks/rules-of-hooks":"off","import/no-anonymous-default-export":"off","storybook/csf-component":"warn","storybook/default-exports":"error","storybook/hierarchy-separator":"warn","storybook/no-redundant-story-name":"warn","storybook/story-exports":"error"}},{name:"storybook:csf:main-rules",files:[".storybook/main.@(js|cjs|mjs|ts)"],rules:{"storybook/no-uninstalled-addons":"error"}}]}});var require_csf_strict2=__commonJS({"src/configs/flat/csf-strict.ts"(exports2,module2){"use strict";var import_csf2=__toESM(require_csf2());module2.exports=[...import_csf2.default,{name:"storybook:csf-strict:rules",rules:{"react-hooks/rules-of-hooks":"off","import/no-anonymous-default-export":"off","storybook/no-stories-of":"error","storybook/no-title-property-in-meta":"error"}}]}});var require_recommended=__commonJS({"src/configs/recommended.ts"(exports2,module2){"use strict";module2.exports={plugins:["storybook"],overrides:[{files:["**/*.stories.@(ts|tsx|js|jsx|mjs|cjs)","**/*.story.@(ts|tsx|js|jsx|mjs|cjs)"],rules:{"react-hooks/rules-of-hooks":"off","import/no-anonymous-default-export":"off","storybook/await-interactions":"error","storybook/context-in-play-function":"error","storybook/default-exports":"error","storybook/hierarchy-separator":"warn","storybook/no-redundant-story-name":"warn","storybook/no-renderer-packages":"error","storybook/prefer-pascal-case":"warn","storybook/story-exports":"error","storybook/use-storybook-expect":"error","storybook/use-storybook-testing-library":"error"}},{files:[".storybook/main.@(js|cjs|mjs|ts)"],rules:{"storybook/no-uninstalled-addons":"error"}}]}}});var import_utils,import_utils2,isNodeOfType,isAwaitExpression,isIdentifier,isVariableDeclarator,isArrayExpression,isArrowFunctionExpression,isBlockStatement,isCallExpression,isExpressionStatement,isVariableDeclaration,isAssignmentExpression,isSequenceExpression,isImportDeclaration,isImportDefaultSpecifier,isImportNamespaceSpecifier,isImportSpecifier,isJSXAttribute,isLiteral,isMemberExpression,isNewExpression,isObjectExpression,isObjectPattern,isProperty,isSpreadElement,isRestElement,isReturnStatement,isFunctionDeclaration,isFunctionExpression,isProgram,isTSTypeAliasDeclaration,isTSInterfaceDeclaration,isTSAsExpression,isTSSatisfiesExpression,isTSNonNullExpression,isMetaProperty,init_ast=__esm({"src/utils/ast.ts"(){"use strict";import_utils=require("@typescript-eslint/utils"),import_utils2=require("@typescript-eslint/utils"),isNodeOfType=nodeType=>node=>node?.type===nodeType,isAwaitExpression=isNodeOfType(import_utils.AST_NODE_TYPES.AwaitExpression),isIdentifier=isNodeOfType(import_utils.AST_NODE_TYPES.Identifier),isVariableDeclarator=isNodeOfType(import_utils.AST_NODE_TYPES.VariableDeclarator),isArrayExpression=isNodeOfType(import_utils.AST_NODE_TYPES.ArrayExpression),isArrowFunctionExpression=isNodeOfType(import_utils.AST_NODE_TYPES.ArrowFunctionExpression),isBlockStatement=isNodeOfType(import_utils.AST_NODE_TYPES.BlockStatement),isCallExpression=isNodeOfType(import_utils.AST_NODE_TYPES.CallExpression),isExpressionStatement=isNodeOfType(import_utils.AST_NODE_TYPES.ExpressionStatement),isVariableDeclaration=isNodeOfType(import_utils.AST_NODE_TYPES.VariableDeclaration),isAssignmentExpression=isNodeOfType(import_utils.AST_NODE_TYPES.AssignmentExpression),isSequenceExpression=isNodeOfType(import_utils.AST_NODE_TYPES.SequenceExpression),isImportDeclaration=isNodeOfType(import_utils.AST_NODE_TYPES.ImportDeclaration),isImportDefaultSpecifier=isNodeOfType(import_utils.AST_NODE_TYPES.ImportDefaultSpecifier),isImportNamespaceSpecifier=isNodeOfType(import_utils.AST_NODE_TYPES.ImportNamespaceSpecifier),isImportSpecifier=isNodeOfType(import_utils.AST_NODE_TYPES.ImportSpecifier),isJSXAttribute=isNodeOfType(import_utils.AST_NODE_TYPES.JSXAttribute),isLiteral=isNodeOfType(import_utils.AST_NODE_TYPES.Literal),isMemberExpression=isNodeOfType(import_utils.AST_NODE_TYPES.MemberExpression),isNewExpression=isNodeOfType(import_utils.AST_NODE_TYPES.NewExpression),isObjectExpression=isNodeOfType(import_utils.AST_NODE_TYPES.ObjectExpression),isObjectPattern=isNodeOfType(import_utils.AST_NODE_TYPES.ObjectPattern),isProperty=isNodeOfType(import_utils.AST_NODE_TYPES.Property),isSpreadElement=isNodeOfType(import_utils.AST_NODE_TYPES.SpreadElement),isRestElement=isNodeOfType(import_utils.AST_NODE_TYPES.RestElement),isReturnStatement=isNodeOfType(import_utils.AST_NODE_TYPES.ReturnStatement),isFunctionDeclaration=isNodeOfType(import_utils.AST_NODE_TYPES.FunctionDeclaration),isFunctionExpression=isNodeOfType(import_utils.AST_NODE_TYPES.FunctionExpression),isProgram=isNodeOfType(import_utils.AST_NODE_TYPES.Program),isTSTypeAliasDeclaration=isNodeOfType(import_utils.AST_NODE_TYPES.TSTypeAliasDeclaration),isTSInterfaceDeclaration=isNodeOfType(import_utils.AST_NODE_TYPES.TSInterfaceDeclaration),isTSAsExpression=isNodeOfType(import_utils.AST_NODE_TYPES.TSAsExpression),isTSSatisfiesExpression=isNodeOfType(import_utils.AST_NODE_TYPES.TSSatisfiesExpression),isTSNonNullExpression=isNodeOfType(import_utils.AST_NODE_TYPES.TSNonNullExpression),isMetaProperty=isNodeOfType(import_utils.AST_NODE_TYPES.MetaProperty)}});var init_constants=__esm({"src/utils/constants.ts"(){"use strict"}});var import_csf,import_utils3,docsUrl,getMetaObjectExpression,getDescriptor,isValidStoryExport,getAllNamedExports,init_utils=__esm({"src/utils/index.ts"(){"use strict";import_csf=require("storybook/internal/csf"),import_utils3=require("@typescript-eslint/utils");init_ast();docsUrl=ruleName=>`https://github.com/storybookjs/storybook/blob/next/code/lib/eslint-plugin/docs/rules/${ruleName}.md`,getMetaObjectExpression=(node,context)=>{let meta=node.declaration,{sourceCode}=context;if(isIdentifier(meta)){let scope=sourceCode.getScope?sourceCode.getScope(node):context.getScope(),variable=import_utils3.ASTUtils.findVariable(scope,meta.name),decl=variable&&variable.defs.find(def=>isVariableDeclarator(def.node));decl&&isVariableDeclarator(decl.node)&&(meta=decl.node.init)}return(isTSAsExpression(meta)||isTSSatisfiesExpression(meta))&&(meta=meta.expression),isObjectExpression(meta)?meta:null},getDescriptor=(metaDeclaration,propertyName)=>{let property=metaDeclaration&&metaDeclaration.properties.find(p=>"key"in p&&"name"in p.key&&p.key.name===propertyName);if(!property||isSpreadElement(property))return;let{type}=property.value;switch(type){case"ArrayExpression":return property.value.elements.map(t=>{if(t===null)throw new Error("Unexpected descriptor element: null");if(!["StringLiteral","Literal"].includes(t.type))throw new Error(`Unexpected descriptor element: ${t.type}`);return t.value});case"Literal":case"RegExpLiteral":return property.value.value;default:throw new Error(`Unexpected descriptor: ${type}`)}},isValidStoryExport=(node,nonStoryExportsConfig)=>(0,import_csf.isExportStory)(node.name,nonStoryExportsConfig)&&node.name!=="__namedExportsOrder",getAllNamedExports=node=>{if(!node.declaration&&node.specifiers)return node.specifiers.reduce((acc,specifier)=>(isIdentifier(specifier.exported)&&acc.push(specifier.exported),acc),[]);let decl=node.declaration;if(isVariableDeclaration(decl)){let declaration=decl.declarations[0];if(declaration){let{id}=declaration;if(isIdentifier(id))return[id]}}return isFunctionDeclaration(decl)&&isIdentifier(decl.id)?[decl.id]:[]}}});function createStorybookRule({create,meta,...remainingConfig}){return import_utils4.ESLintUtils.RuleCreator(docsUrl)({...remainingConfig,create,meta:{...meta,docs:{...meta.docs},defaultOptions:remainingConfig.defaultOptions}})}var import_utils4,init_create_storybook_rule=__esm({"src/utils/create-storybook-rule.ts"(){"use strict";import_utils4=require("@typescript-eslint/utils");init_utils()}});var require_await_interactions=__commonJS({"src/rules/await-interactions.ts"(exports2,module2){"use strict";init_ast();init_constants();init_create_storybook_rule();module2.exports=createStorybookRule({name:"await-interactions",defaultOptions:[],meta:{severity:"error",docs:{description:"Interactions should be awaited",categories:["addon-interactions","recommended"]},messages:{interactionShouldBeAwaited:"Interaction should be awaited: {{method}}",fixSuggestion:"Add `await` to method"},type:"problem",fixable:"code",hasSuggestions:!0,schema:[]},create(context){let FUNCTIONS_TO_BE_AWAITED=["waitFor","waitForElementToBeRemoved","wait","waitForElement","waitForDomChange","userEvent","play"],getMethodThatShouldBeAwaited=expr=>{let shouldAwait=name=>FUNCTIONS_TO_BE_AWAITED.includes(name)||name.startsWith("findBy");return isArrowFunctionExpression(expr.parent)||isReturnStatement(expr.parent)?null:isMemberExpression(expr.callee)&&isIdentifier(expr.callee.object)&&shouldAwait(expr.callee.object.name)?expr.callee.object:isTSNonNullExpression(expr.callee)&&isMemberExpression(expr.callee.expression)&&isIdentifier(expr.callee.expression.property)&&shouldAwait(expr.callee.expression.property.name)?expr.callee.expression.property:isMemberExpression(expr.callee)&&isIdentifier(expr.callee.property)&&shouldAwait(expr.callee.property.name)||isMemberExpression(expr.callee)&&isCallExpression(expr.callee.object)&&isIdentifier(expr.callee.object.callee)&&isIdentifier(expr.callee.property)&&expr.callee.object.callee.name==="expect"?expr.callee.property:isIdentifier(expr.callee)&&shouldAwait(expr.callee.name)?expr.callee:null},getClosestFunctionAncestor=node=>{let parent=node.parent;if(!(!parent||isProgram(parent)))return isArrowFunctionExpression(parent)||isFunctionExpression(parent)||isFunctionDeclaration(parent)?node.parent:getClosestFunctionAncestor(parent)},isUserEventFromStorybookImported=node=>(node.source.value==="@storybook/testing-library"||node.source.value==="@storybook/test")&&node.specifiers.find(spec=>isImportSpecifier(spec)&&"name"in spec.imported&&spec.imported.name==="userEvent"&&spec.local.name==="userEvent")!==void 0,isExpectFromStorybookImported=node=>(node.source.value==="@storybook/jest"||node.source.value==="@storybook/test")&&node.specifiers.find(spec=>isImportSpecifier(spec)&&"name"in spec.imported&&spec.imported.name==="expect")!==void 0,isImportedFromStorybook=!0,invocationsThatShouldBeAwaited=[];return{ImportDeclaration(node){isImportedFromStorybook=isUserEventFromStorybookImported(node)||isExpectFromStorybookImported(node)},VariableDeclarator(node){isImportedFromStorybook=isImportedFromStorybook&&isIdentifier(node.id)&&node.id.name!=="userEvent"},CallExpression(node){let method=getMethodThatShouldBeAwaited(node);method&&!isAwaitExpression(node.parent)&&!isAwaitExpression(node.parent?.parent)&&invocationsThatShouldBeAwaited.push({node,method})},"Program:exit":function(){isImportedFromStorybook&&invocationsThatShouldBeAwaited.length&&invocationsThatShouldBeAwaited.forEach(({node,method})=>{let parentFnNode=getClosestFunctionAncestor(node),parentFnNeedsAsync=parentFnNode&&!("async"in parentFnNode&&parentFnNode.async),fixFn=fixer=>{let fixerResult=[fixer.insertTextBefore(node,"await ")];return parentFnNeedsAsync&&fixerResult.push(fixer.insertTextBefore(parentFnNode,"async ")),fixerResult};context.report({node,messageId:"interactionShouldBeAwaited",data:{method:method.name},fix:fixFn,suggest:[{messageId:"fixSuggestion",fix:fixFn}]})})}}}})}});var require_context_in_play_function=__commonJS({"src/rules/context-in-play-function.ts"(exports2,module2){"use strict";init_ast();init_constants();init_create_storybook_rule();module2.exports=createStorybookRule({name:"context-in-play-function",defaultOptions:[],meta:{type:"problem",severity:"error",docs:{description:"Pass a context when invoking play function of another story",categories:["recommended","addon-interactions"]},messages:{passContextToPlayFunction:"Pass a context when invoking play function of another story"},fixable:void 0,schema:[]},create(context){let isPlayFunctionFromAnotherStory=expr=>!!(isTSNonNullExpression(expr.callee)&&isMemberExpression(expr.callee.expression)&&isIdentifier(expr.callee.expression.property)&&expr.callee.expression.property.name==="play"||isMemberExpression(expr.callee)&&isIdentifier(expr.callee.property)&&expr.callee.property.name==="play"),getParentParameterName=node=>{if(!isArrowFunctionExpression(node))return node.parent?getParentParameterName(node.parent):void 0;if(node.params.length!==0&&node.params.length>=1){let param=node.params[0];if(isIdentifier(param))return param.name;if(isObjectPattern(param)){if(param.properties.find(prop=>prop.type==="Property"&&prop.key.type==="Identifier"&&prop.key.name==="context"))return"context";let restElement=param.properties.find(isRestElement);return!restElement||!isIdentifier(restElement.argument)?void 0:restElement.argument.name}}},isNotPassingContextCorrectly=expr=>{let firstExpressionArgument=expr.arguments[0];if(!firstExpressionArgument)return!0;let contextVariableName=getParentParameterName(expr);return contextVariableName?!(expr.arguments.length===1&&isIdentifier(firstExpressionArgument)&&firstExpressionArgument.name===contextVariableName||isObjectExpression(firstExpressionArgument)&&firstExpressionArgument.properties.some(prop=>isSpreadElement(prop)&&isIdentifier(prop.argument)&&prop.argument.name===contextVariableName)):!0},invocationsWithoutProperContext=[];return{CallExpression(node){isPlayFunctionFromAnotherStory(node)&&isNotPassingContextCorrectly(node)&&invocationsWithoutProperContext.push(node)},"Program:exit":function(){invocationsWithoutProperContext.forEach(node=>{context.report({node,messageId:"passContextToPlayFunction"})})}}}})}});var require_csf_component=__commonJS({"src/rules/csf-component.ts"(exports2,module2){"use strict";init_utils();init_ast();init_constants();init_create_storybook_rule();module2.exports=createStorybookRule({name:"csf-component",defaultOptions:[],meta:{type:"suggestion",severity:"warn",docs:{description:"The component property should be set",categories:["csf"]},messages:{missingComponentProperty:"Missing component property."},schema:[]},create(context){return{ExportDefaultDeclaration(node){let meta=getMetaObjectExpression(node,context);if(!meta)return null;meta.properties.find(property=>!isSpreadElement(property)&&"name"in property.key&&property.key.name==="component")||context.report({node,messageId:"missingComponentProperty"})}}}})}});var require_default_exports=__commonJS({"src/rules/default-exports.ts"(exports2,module2){"use strict";var import_path=__toESM(require("path"));init_ast();init_constants();init_create_storybook_rule();module2.exports=createStorybookRule({name:"default-exports",defaultOptions:[],meta:{type:"problem",severity:"error",docs:{description:"Story files should have a default export",categories:["csf","recommended"]},messages:{shouldHaveDefaultExport:"The file should have a default export.",fixSuggestion:"Add default export"},fixable:"code",hasSuggestions:!0,schema:[]},create(context){let getComponentName=(node,filePath)=>{let name=import_path.default.basename(filePath).split(".")[0];return node.body.find(stmt=>{if(isImportDeclaration(stmt)&&isLiteral(stmt.source)&&stmt.source.value.startsWith(`./${name}`))return!!stmt.specifiers.find(spec=>isIdentifier(spec.local)&&spec.local.name===name)})?name:null},hasDefaultExport=!1,isCsf4Style=!1,hasStoriesOfImport=!1;return{ImportSpecifier(node){"name"in node.imported&&node.imported.name==="storiesOf"&&(hasStoriesOfImport=!0)},VariableDeclaration(node){node.parent.type==="Program"&&node.declarations.forEach(declaration=>{let init=declaration.init;if(init&&init.type==="CallExpression"){let callee=init.callee;callee.type==="MemberExpression"&&callee.property.type==="Identifier"&&callee.property.name==="meta"&&(isCsf4Style=!0)}})},ExportDefaultSpecifier:function(){hasDefaultExport=!0},ExportDefaultDeclaration:function(){hasDefaultExport=!0},"Program:exit":function(program){if(!isCsf4Style&&!hasDefaultExport&&!hasStoriesOfImport){let componentName=getComponentName(program,context.getFilename()),node=program.body.find(n=>!isImportDeclaration(n))||program.body[0]||program,report={node,messageId:"shouldHaveDefaultExport"},fix=fixer=>{let metaDeclaration=componentName?`export default { component: ${componentName} }
`:`export default {}
`;return fixer.insertTextBefore(node,metaDeclaration)};context.report({...report,fix,suggest:[{messageId:"fixSuggestion",fix}]})}}}}})}});var require_hierarchy_separator=__commonJS({"src/rules/hierarchy-separator.ts"(exports2,module2){"use strict";init_utils();init_ast();init_constants();init_create_storybook_rule();module2.exports=createStorybookRule({name:"hierarchy-separator",defaultOptions:[],meta:{type:"problem",fixable:"code",hasSuggestions:!0,severity:"warn",docs:{description:"Deprecated hierarchy separator in title property",categories:["csf","recommended"]},messages:{useCorrectSeparators:"Use correct separators",deprecatedHierarchySeparator:"Deprecated hierarchy separator in title property: {{metaTitle}}."},schema:[]},create:function(context){return{ExportDefaultDeclaration:function(node){let meta=getMetaObjectExpression(node,context);if(!meta)return null;let titleNode=meta.properties.find(prop=>!isSpreadElement(prop)&&"name"in prop.key&&prop.key?.name==="title");if(!titleNode||!isLiteral(titleNode.value))return;let metaTitle=titleNode.value.raw||"";metaTitle.includes("|")&&context.report({node:titleNode,messageId:"deprecatedHierarchySeparator",data:{metaTitle},fix:function(fixer){return fixer.replaceTextRange(titleNode.value.range,metaTitle.replace(/\|/g,"/"))},suggest:[{messageId:"useCorrectSeparators",fix:function(fixer){return fixer.replaceTextRange(titleNode.value.range,metaTitle.replace(/\|/g,"/"))}}]})}}}})}});var require_meta_inline_properties=__commonJS({"src/rules/meta-inline-properties.ts"(exports2,module2){"use strict";init_utils();init_constants();init_create_storybook_rule();module2.exports=createStorybookRule({name:"meta-inline-properties",defaultOptions:[{csfVersion:3}],meta:{type:"problem",severity:"error",docs:{description:"Meta should only have inline properties",categories:["csf","recommended"],excludeFromConfig:!0},messages:{metaShouldHaveInlineProperties:"Meta should only have inline properties: {{property}}"},schema:[{type:"object",properties:{csfVersion:{type:"number"}},additionalProperties:!1}]},create(context){let isInline=node=>node&&typeof node=="object"&&"value"in node?node.value.type==="ObjectExpression"||node.value.type==="Literal"||node.value.type==="ArrayExpression":!1;return{ExportDefaultDeclaration(node){let meta=getMetaObjectExpression(node,context);if(!meta)return null;let ruleProperties=["title","args"],dynamicProperties=[];meta.properties.filter(prop=>"key"in prop&&"name"in prop.key&&ruleProperties.includes(prop.key.name)).forEach(metaNode=>{isInline(metaNode)||dynamicProperties.push(metaNode)}),dynamicProperties.length>0&&dynamicProperties.forEach(propertyNode=>{context.report({node:propertyNode,messageId:"metaShouldHaveInlineProperties",data:{property:propertyNode.key?.name}})})}}}})}});var require_meta_satisfies_type=__commonJS({"src/rules/meta-satisfies-type.ts"(exports2,module2){"use strict";var import_utils5=require("@typescript-eslint/utils");init_utils();init_ast();init_create_storybook_rule();module2.exports=createStorybookRule({name:"meta-satisfies-type",defaultOptions:[],meta:{type:"problem",fixable:"code",severity:"error",docs:{description:"Meta should use `satisfies Meta`",categories:[],excludeFromConfig:!0},messages:{metaShouldSatisfyType:"CSF Meta should use `satisfies` for type safety"},schema:[]},create(context){let sourceCode=context.getSourceCode(),getTextWithParentheses=node=>{let beforeCount=0,afterCount=0;if(import_utils5.ASTUtils.isParenthesized(node,sourceCode)){let bodyOpeningParen=sourceCode.getTokenBefore(node,import_utils5.ASTUtils.isOpeningParenToken),bodyClosingParen=sourceCode.getTokenAfter(node,import_utils5.ASTUtils.isClosingParenToken);bodyOpeningParen&&bodyClosingParen&&(beforeCount=node.range[0]-bodyOpeningParen.range[0],afterCount=bodyClosingParen.range[1]-node.range[1])}return sourceCode.getText(node,beforeCount,afterCount)},getFixer=meta=>{let{parent}=meta;if(parent)switch(parent.type){case import_utils5.AST_NODE_TYPES.TSAsExpression:return fixer=>[fixer.replaceText(parent,getTextWithParentheses(meta)),fixer.insertTextAfter(parent,` satisfies ${getTextWithParentheses(parent.typeAnnotation)}`)];case import_utils5.AST_NODE_TYPES.VariableDeclarator:{let{typeAnnotation}=parent.id;return typeAnnotation?fixer=>[fixer.remove(typeAnnotation),fixer.insertTextAfter(meta,` satisfies ${getTextWithParentheses(typeAnnotation.typeAnnotation)}`)]:void 0}default:return}};return{ExportDefaultDeclaration(node){let meta=getMetaObjectExpression(node,context);if(!meta)return null;(!meta.parent||!isTSSatisfiesExpression(meta.parent))&&context.report({node:meta,messageId:"metaShouldSatisfyType",fix:getFixer(meta)})}}}})}});var require_no_redundant_story_name=__commonJS({"src/rules/no-redundant-story-name.ts"(exports2,module2){"use strict";var import_csf2=require("storybook/internal/csf");init_ast();init_constants();init_create_storybook_rule();module2.exports=createStorybookRule({name:"no-redundant-story-name",defaultOptions:[],meta:{type:"suggestion",fixable:"code",hasSuggestions:!0,severity:"warn",docs:{description:"A story should not have a redundant name property",categories:["csf","recommended"]},messages:{removeRedundantName:"Remove redundant name",storyNameIsRedundant:"Named exports should not use the name annotation if it is redundant to the name that would be generated by the export name"},schema:[]},create(context){return{ExportNamedDeclaration:function(node){if(!node.declaration)return;let decl=node.declaration;if(isVariableDeclaration(decl)){let declaration=decl.declarations[0];if(declaration==null)return;let{id,init}=declaration;if(isIdentifier(id)&&isObjectExpression(init)){let storyNameNode=init.properties.find(prop=>isProperty(prop)&&isIdentifier(prop.key)&&(prop.key?.name==="name"||prop.key?.name==="storyName"));if(!storyNameNode)return;let{name}=id,resolvedStoryName=(0,import_csf2.storyNameFromExport)(name);!isSpreadElement(storyNameNode)&&isLiteral(storyNameNode.value)&&storyNameNode.value.value===resolvedStoryName&&context.report({node:storyNameNode,messageId:"storyNameIsRedundant",suggest:[{messageId:"removeRedundantName",fix:function(fixer){return fixer.remove(storyNameNode)}}]})}}},AssignmentExpression:function(node){if(!isExpressionStatement(node.parent))return;let{left,right}=node;if("property"in left&&isIdentifier(left.property)&&!isMetaProperty(left)&&left.property.name==="storyName"){if(!("name"in left.object&&"value"in right))return;let propertyName=left.object.name,propertyValue=right.value,resolvedStoryName=(0,import_csf2.storyNameFromExport)(propertyName);propertyValue===resolvedStoryName&&context.report({node,messageId:"storyNameIsRedundant",suggest:[{messageId:"removeRedundantName",fix:function(fixer){return fixer.remove(node)}}]})}}}}})}});var require_no_renderer_packages=__commonJS({"src/rules/no-renderer-packages.ts"(exports2,module2){"use strict";init_constants();init_create_storybook_rule();var rendererToFrameworks={"@storybook/html":["@storybook/html-vite","@storybook/html-webpack5"],"@storybook/preact":["@storybook/preact-vite","@storybook/preact-webpack5"],"@storybook/react":["@storybook/nextjs","@storybook/react-vite","@storybook/react-webpack5","@storybook/react-native-web-vite","@storybook/experimental-nextjs-vite"],"@storybook/server":["@storybook/server-webpack5"],"@storybook/svelte":["@storybook/svelte-vite","@storybook/svelte-webpack5","@storybook/sveltekit"],"@storybook/vue3":["@storybook/vue3-vite","@storybook/vue3-webpack5"],"@storybook/web-components":["@storybook/web-components-vite","@storybook/web-components-webpack5"]};module2.exports=createStorybookRule({name:"no-renderer-packages",defaultOptions:[],meta:{type:"problem",severity:"error",docs:{description:"Do not import renderer packages directly in stories",categories:["recommended"]},schema:[],messages:{noRendererPackages:'Do not import renderer package "{{rendererPackage}}" directly. Use a framework package instead (e.g. {{suggestions}}).'}},create(context){return{ImportDeclaration(node){let packageName=node.source.value;if(typeof packageName=="string"&&packageName in rendererToFrameworks){let suggestions=rendererToFrameworks[packageName];context.report({node,messageId:"noRendererPackages",data:{rendererPackage:packageName,suggestions:suggestions.join(", ")}})}}}}})}});var require_no_stories_of=__commonJS({"src/rules/no-stories-of.ts"(exports2,module2){"use strict";init_constants();init_create_storybook_rule();module2.exports=createStorybookRule({name:"no-stories-of",defaultOptions:[],meta:{type:"problem",severity:"error",docs:{description:"storiesOf is deprecated and should not be used",categories:["csf-strict"]},messages:{doNotUseStoriesOf:"storiesOf is deprecated and should not be used"},schema:[]},create(context){return{ImportSpecifier(node){"name"in node.imported&&node.imported.name==="storiesOf"&&context.report({node,messageId:"doNotUseStoriesOf"})}}}})}});var require_no_title_property_in_meta=__commonJS({"src/rules/no-title-property-in-meta.ts"(exports2,module2){"use strict";init_utils();init_ast();init_constants();init_create_storybook_rule();module2.exports=createStorybookRule({name:"no-title-property-in-meta",defaultOptions:[],meta:{type:"problem",fixable:"code",hasSuggestions:!0,severity:"error",docs:{description:"Do not define a title in meta",categories:["csf-strict"]},messages:{removeTitleInMeta:"Remove title property from meta",noTitleInMeta:"CSF3 does not need a title in meta"},schema:[]},create:function(context){return{ExportDefaultDeclaration:function(node){let meta=getMetaObjectExpression(node,context);if(!meta)return null;let titleNode=meta.properties.find(prop=>!isSpreadElement(prop)&&"name"in prop.key&&prop.key?.name==="title");titleNode&&context.report({node:titleNode,messageId:"noTitleInMeta",suggest:[{messageId:"removeTitleInMeta",fix(fixer){let hasComma=context.getSourceCode().text.slice(titleNode.range[0],titleNode.range[1]+1).slice(-1)===",",propertyRange=[titleNode.range[0],hasComma?titleNode.range[1]+1:titleNode.range[1]];return fixer.removeRange(propertyRange)}}]})}}}})}});function dedent(templ){for(var values=[],_i=1;_i<arguments.length;_i++)values[_i-1]=arguments[_i];var strings=Array.from(typeof templ=="string"?[templ]:templ);strings[strings.length-1]=strings[strings.length-1].replace(/\r?\n([\t ]*)$/,"");var indentLengths=strings.reduce(function(arr,str){var matches=str.match(/\n([\t ]+|(?!\s).)/g);return matches?arr.concat(matches.map(function(match){var _a,_b;return(_b=(_a=match.match(/[\t ]/g))===null||_a===void 0?void 0:_a.length)!==null&&_b!==void 0?_b:0})):arr},[]);if(indentLengths.length){var pattern_1=new RegExp(`
[	 ]{`+Math.min.apply(Math,indentLengths)+"}","g");strings=strings.map(function(str){return str.replace(pattern_1,`
`)})}strings[0]=strings[0].replace(/^\r?\n/,"");var string=strings[0];return values.forEach(function(value,i){var endentations=string.match(/(?:^|\n)( *)$/),endentation=endentations?endentations[1]:"",indentedValue=value;typeof value=="string"&&value.includes(`
`)&&(indentedValue=String(value).split(`
`).map(function(str,i2){return i2===0?str:""+endentation+str}).join(`
`)),string+=indentedValue+strings[i+1]}),string}var init_esm=__esm({"../../node_modules/ts-dedent/esm/index.js"(){"use strict"}});var require_no_uninstalled_addons=__commonJS({"src/rules/no-uninstalled-addons.ts"(exports2,module2){"use strict";var import_fs=require("fs"),import_path=require("path");init_esm();init_utils();init_ast();init_constants();init_create_storybook_rule();module2.exports=createStorybookRule({name:"no-uninstalled-addons",defaultOptions:[{packageJsonLocation:"",ignore:[]}],meta:{type:"problem",severity:"error",docs:{description:"This rule identifies storybook addons that are invalid because they are either not installed or contain a typo in their name.",categories:["recommended"]},messages:{addonIsNotInstalled:"The {{ addonName }} is not installed in {{packageJsonPath}}. Did you forget to install it or is your package.json in a different location?"},schema:[{type:"object",properties:{packageJsonLocation:{type:"string"},ignore:{type:"array",items:{type:"string"}}}}]},create(context){let{packageJsonLocation,ignore}=context.options.reduce((acc,val)=>({packageJsonLocation:val.packageJsonLocation||acc.packageJsonLocation,ignore:val.ignore||acc.ignore}),{packageJsonLocation:"",ignore:[]});function excludeNullable(item){return!!item}let mergeDepsWithDevDeps=packageJson=>{let deps=Object.keys(packageJson.dependencies||{}),devDeps=Object.keys(packageJson.devDependencies||{});return[...deps,...devDeps]},isAddonInstalled=(addon,installedAddons)=>{let addonName=addon.replace(/\.[mc]?js$/,"").replace(/\/register$/,"").replace(/\/preset$/,"");return installedAddons.includes(addonName)},filterLocalAddons=addon=>!(addonName=>addonName.startsWith(".")||addonName.startsWith("/")||/\w:.*/.test(addonName)||addonName.startsWith("\\"))(addon),areThereAddonsNotInstalled=(addons,installedSbAddons)=>{let result=addons.filter(filterLocalAddons).filter(addon=>!isAddonInstalled(addon,installedSbAddons)&&!ignore.includes(addon)).map(addon=>({name:addon}));return result.length?result:!1},getPackageJson=path=>{let packageJson={devDependencies:{},dependencies:{}};try{let file=(0,import_fs.readFileSync)(path,"utf8"),parsedFile=JSON.parse(file);packageJson.dependencies=parsedFile.dependencies||{},packageJson.devDependencies=parsedFile.devDependencies||{}}catch{throw new Error(dedent`The provided path in your eslintrc.json - ${path} is not a valid path to a package.json file or your package.json file is not in the same folder as ESLint is running from.

          Read more at: https://github.com/storybookjs/storybook/blob/next/code/lib/eslint-plugin/docs/rules/no-uninstalled-addons.md
          `)}return packageJson},extractAllAddonsFromTheStorybookConfig=addonsExpression=>{if(addonsExpression?.elements){let nodesWithAddons=addonsExpression.elements.map(elem=>isLiteral(elem)?{value:elem.value,node:elem}:void 0).filter(excludeNullable),listOfAddonsInString=nodesWithAddons.map(elem=>elem.value),nodesWithAddonsInObj=addonsExpression.elements.map(elem=>isObjectExpression(elem)?elem:{properties:[]}).map(elem=>{let property=elem.properties.find(prop=>isProperty(prop)&&isIdentifier(prop.key)&&prop.key.name==="name");return isLiteral(property?.value)?{value:property.value.value,node:property.value}:void 0}).filter(excludeNullable),listOfAddonsInObj=nodesWithAddonsInObj.map(elem=>elem.value),listOfAddons=[...listOfAddonsInString,...listOfAddonsInObj],listOfAddonElements=[...nodesWithAddons,...nodesWithAddonsInObj];return{listOfAddons,listOfAddonElements}}return{listOfAddons:[],listOfAddonElements:[]}};function reportUninstalledAddons(addonsProp){let packageJsonPath=(0,import_path.resolve)(packageJsonLocation||"./package.json"),packageJsonObject;try{packageJsonObject=getPackageJson(packageJsonPath)}catch(e){throw new Error(e)}let depsAndDevDeps=mergeDepsWithDevDeps(packageJsonObject),{listOfAddons,listOfAddonElements}=extractAllAddonsFromTheStorybookConfig(addonsProp),result=areThereAddonsNotInstalled(listOfAddons,depsAndDevDeps);if(result){let elemsWithErrors=listOfAddonElements.filter(elem=>!!result.find(addon=>addon.name===elem.value)),currentPackageJsonPath=`${process.cwd().split(import_path.sep).pop()}${import_path.sep}${(0,import_path.relative)(process.cwd(),packageJsonLocation)}`;elemsWithErrors.forEach(elem=>{context.report({node:elem.node,messageId:"addonIsNotInstalled",data:{addonName:elem.value,packageJsonPath:currentPackageJsonPath}})})}}function findAddonsPropAndReport(node){let addonsProp=node.properties.find(prop=>isProperty(prop)&&isIdentifier(prop.key)&&prop.key.name==="addons");addonsProp?.value&&isArrayExpression(addonsProp.value)&&reportUninstalledAddons(addonsProp.value)}return{AssignmentExpression:function(node){isObjectExpression(node.right)&&findAddonsPropAndReport(node.right)},ExportDefaultDeclaration:function(node){let meta=getMetaObjectExpression(node,context);if(!meta)return null;findAddonsPropAndReport(meta)},ExportNamedDeclaration:function(node){let addonsProp=isVariableDeclaration(node.declaration)&&node.declaration.declarations.find(decl=>isVariableDeclarator(decl)&&isIdentifier(decl.id)&&decl.id.name==="addons");addonsProp&&isArrayExpression(addonsProp.init)&&reportUninstalledAddons(addonsProp.init)}}}})}});var require_prefer_pascal_case=__commonJS({"src/rules/prefer-pascal-case.ts"(exports2,module2){"use strict";var import_csf2=require("storybook/internal/csf"),import_utils5=require("@typescript-eslint/utils");init_utils();init_ast();init_constants();init_create_storybook_rule();module2.exports=createStorybookRule({name:"prefer-pascal-case",defaultOptions:[],meta:{type:"suggestion",fixable:"code",hasSuggestions:!0,severity:"warn",docs:{description:"Stories should use PascalCase",categories:["recommended"]},messages:{convertToPascalCase:"Use pascal case",usePascalCase:"The story should use PascalCase notation: {{name}}"},schema:[]},create(context){let isPascalCase=str=>/^[A-Z]+([a-z0-9]?)+/.test(str),toPascalCase=str=>str.replace(new RegExp(/[-_]+/,"g")," ").replace(new RegExp(/[^\w\s]/,"g"),"").replace(new RegExp(/\s+(.)(\w+)/,"g"),(_,$2,$3)=>`${$2.toUpperCase()+$3.toLowerCase()}`).replace(new RegExp(/\s/,"g"),"").replace(new RegExp(/\w/),s=>s.toUpperCase()),getModuleScope=()=>{let{sourceCode}=context;return sourceCode.getScope?sourceCode.scopeManager?.scopes?.find(scope=>scope.type==="module"):context.getScope().childScopes.find(scope=>scope.type==="module")},checkAndReportError=(id,nonStoryExportsConfig2={})=>{let{name}=id;if(!(0,import_csf2.isExportStory)(name,nonStoryExportsConfig2)||name==="__namedExportsOrder")return null;!name.startsWith("_")&&!isPascalCase(name)&&context.report({node:id,messageId:"usePascalCase",data:{name},suggest:[{messageId:"convertToPascalCase",*fix(fixer){let suffix=context.getSourceCode().text.slice(id.range[0],id.range[1]).substring(name.length),pascal=toPascalCase(name);yield fixer.replaceTextRange(id.range,pascal+suffix);let scope=getModuleScope();if(scope){let variable=import_utils5.ASTUtils.findVariable(scope,name),referenceCount=variable?.references?.length||0;for(let i=0;i<referenceCount;i++){let ref=variable?.references[i];ref&&!ref.init&&(yield fixer.replaceTextRange(ref.identifier.range,pascal))}}}}]})},meta,nonStoryExportsConfig,namedExports=[],hasStoriesOfImport=!1;return{ImportSpecifier(node){"name"in node.imported&&node.imported.name==="storiesOf"&&(hasStoriesOfImport=!0)},ExportDefaultDeclaration:function(node){if(meta=getMetaObjectExpression(node,context),meta)try{nonStoryExportsConfig={excludeStories:getDescriptor(meta,"excludeStories"),includeStories:getDescriptor(meta,"includeStories")}}catch{}},ExportNamedDeclaration:function(node){if(!node.declaration)return;let decl=node.declaration;if(isVariableDeclaration(decl)){let declaration=decl.declarations[0];if(declaration==null)return;let{id}=declaration;isIdentifier(id)&&namedExports.push(id)}},"Program:exit":function(){namedExports.length&&!hasStoriesOfImport&&namedExports.forEach(n=>checkAndReportError(n,nonStoryExportsConfig))}}}})}});var require_story_exports=__commonJS({"src/rules/story-exports.ts"(exports2,module2){"use strict";init_utils();init_ast();init_constants();init_create_storybook_rule();module2.exports=createStorybookRule({name:"story-exports",defaultOptions:[],meta:{type:"problem",severity:"error",docs:{description:"A story file must contain at least one story export",categories:["recommended","csf"]},messages:{shouldHaveStoryExport:"The file should have at least one story export",shouldHaveStoryExportWithFilters:"The file should have at least one story export. Make sure the includeStories/excludeStories you defined are correct, otherwise Storybook will not use any stories for this file.",addStoryExport:"Add a story export"},fixable:void 0,schema:[]},create(context){let hasStoriesOfImport=!1,nonStoryExportsConfig={},meta,namedExports=[];return{ImportSpecifier(node){"name"in node.imported&&node.imported.name==="storiesOf"&&(hasStoriesOfImport=!0)},ExportDefaultDeclaration:function(node){if(meta=getMetaObjectExpression(node,context),meta)try{nonStoryExportsConfig={excludeStories:getDescriptor(meta,"excludeStories"),includeStories:getDescriptor(meta,"includeStories")}}catch{}},ExportNamedDeclaration:function(node){namedExports.push(...getAllNamedExports(node))},"Program:exit":function(program){if(hasStoriesOfImport||!meta||namedExports.filter(exp=>isValidStoryExport(exp,nonStoryExportsConfig)).length)return;let node=program.body.find(n=>!isImportDeclaration(n))||program.body[0]||program,hasFilter=nonStoryExportsConfig.includeStories||nonStoryExportsConfig.excludeStories,report={node,messageId:hasFilter?"shouldHaveStoryExportWithFilters":"shouldHaveStoryExport"};context.report(report)}}}})}});var require_use_storybook_expect=__commonJS({"src/rules/use-storybook-expect.ts"(exports2,module2){"use strict";init_ast();init_constants();init_create_storybook_rule();module2.exports=createStorybookRule({name:"use-storybook-expect",defaultOptions:[],meta:{type:"suggestion",fixable:"code",schema:[],severity:"error",docs:{description:"Use expect from `@storybook/test`, `storybook/test` or `@storybook/jest`",categories:["addon-interactions","recommended"]},messages:{useExpectFromStorybook:"Do not use global expect directly in the story. You should import it from `@storybook/test` (preferrably) or `@storybook/jest` instead."}},create(context){let isExpectFromStorybookImported=node=>{let{value:packageName}=node.source;return(packageName==="@storybook/jest"||packageName==="@storybook/test"||packageName==="storybook/test")&&node.specifiers.find(spec=>isImportSpecifier(spec)&&"name"in spec.imported&&spec.imported.name==="expect")},isImportingFromStorybookExpect=!1,expectInvocations=[];return{ImportDeclaration(node){isExpectFromStorybookImported(node)&&(isImportingFromStorybookExpect=!0)},CallExpression(node){if(!isIdentifier(node.callee))return null;node.callee.name==="expect"&&expectInvocations.push(node.callee)},"Program:exit":function(){!isImportingFromStorybookExpect&&expectInvocations.length&&expectInvocations.forEach(node=>{context.report({node,messageId:"useExpectFromStorybook"})})}}}})}});var require_use_storybook_testing_library=__commonJS({"src/rules/use-storybook-testing-library.ts"(exports2,module2){"use strict";init_ast();init_constants();init_create_storybook_rule();module2.exports=createStorybookRule({name:"use-storybook-testing-library",defaultOptions:[],meta:{type:"suggestion",fixable:"code",hasSuggestions:!0,severity:"error",docs:{description:"Do not use testing-library directly on stories",categories:["addon-interactions","recommended"]},schema:[],messages:{updateImports:"Update imports",dontUseTestingLibraryDirectly:"Do not use `{{library}}` directly in the story. You should import the functions from `@storybook/test` (preferrably) or `@storybook/testing-library` instead."}},create(context){let getRangeWithoutQuotes=source=>[source.range[0]+1,source.range[1]-1],hasDefaultImport=specifiers=>specifiers.find(s=>isImportDefaultSpecifier(s)),getSpecifiers=node=>{let{specifiers}=node;if(!specifiers[0])return null;let start=specifiers[0].range[0],previousSpecifier=specifiers[specifiers.length-1];if(!previousSpecifier)return null;let end=previousSpecifier.range[1],fullText=context.getSourceCode().text,importEnd=node.range[1],closingBrace=fullText.indexOf("}",end-1);closingBrace>-1&&closingBrace<=importEnd&&(end=closingBrace+1);let text=fullText.substring(start,end);return{range:[start,end],text}},fixSpecifiers=specifiersText=>`{ ${specifiersText.replace("{","").replace("}","").replace(/\s\s+/g," ").trim()} }`;return{ImportDeclaration(node){node.source.value.includes("@testing-library")&&context.report({node,messageId:"dontUseTestingLibraryDirectly",data:{library:node.source.value},*fix(fixer){if(yield fixer.replaceTextRange(getRangeWithoutQuotes(node.source),"@storybook/testing-library"),hasDefaultImport(node.specifiers)){let specifiers=getSpecifiers(node);if(specifiers){let{range,text}=specifiers;yield fixer.replaceTextRange(range,fixSpecifiers(text))}}},suggest:[{messageId:"updateImports",*fix(fixer){if(yield fixer.replaceTextRange(getRangeWithoutQuotes(node.source),"@storybook/testing-library"),hasDefaultImport(node.specifiers)){let specifiers=getSpecifiers(node);if(specifiers){let{range,text}=specifiers;yield fixer.replaceTextRange(range,fixSpecifiers(text))}}}}]})}}}})}});var require_index=__commonJS({"src/index.ts"(exports2,module2){"use strict";var import_addon_interactions=__toESM(require_addon_interactions()),import_csf2=__toESM(require_csf()),import_csf_strict=__toESM(require_csf_strict()),import_addon_interactions2=__toESM(require_addon_interactions2()),import_csf3=__toESM(require_csf2()),import_csf_strict2=__toESM(require_csf_strict2()),import_recommended=__toESM(require_recommended2()),import_recommended2=__toESM(require_recommended()),import_await_interactions=__toESM(require_await_interactions()),import_context_in_play_function=__toESM(require_context_in_play_function()),import_csf_component=__toESM(require_csf_component()),import_default_exports=__toESM(require_default_exports()),import_hierarchy_separator=__toESM(require_hierarchy_separator()),import_meta_inline_properties=__toESM(require_meta_inline_properties()),import_meta_satisfies_type=__toESM(require_meta_satisfies_type()),import_no_redundant_story_name=__toESM(require_no_redundant_story_name()),import_no_renderer_packages=__toESM(require_no_renderer_packages()),import_no_stories_of=__toESM(require_no_stories_of()),import_no_title_property_in_meta=__toESM(require_no_title_property_in_meta()),import_no_uninstalled_addons=__toESM(require_no_uninstalled_addons()),import_prefer_pascal_case=__toESM(require_prefer_pascal_case()),import_story_exports=__toESM(require_story_exports()),import_use_storybook_expect=__toESM(require_use_storybook_expect()),import_use_storybook_testing_library=__toESM(require_use_storybook_testing_library());module2.exports={configs:{csf:import_csf2.default,"csf-strict":import_csf_strict.default,"addon-interactions":import_addon_interactions.default,recommended:import_recommended2.default,"flat/csf":import_csf3.default,"flat/csf-strict":import_csf_strict2.default,"flat/addon-interactions":import_addon_interactions2.default,"flat/recommended":import_recommended.default},rules:{"await-interactions":import_await_interactions.default,"context-in-play-function":import_context_in_play_function.default,"csf-component":import_csf_component.default,"default-exports":import_default_exports.default,"hierarchy-separator":import_hierarchy_separator.default,"meta-inline-properties":import_meta_inline_properties.default,"meta-satisfies-type":import_meta_satisfies_type.default,"no-redundant-story-name":import_no_redundant_story_name.default,"no-renderer-packages":import_no_renderer_packages.default,"no-stories-of":import_no_stories_of.default,"no-title-property-in-meta":import_no_title_property_in_meta.default,"no-uninstalled-addons":import_no_uninstalled_addons.default,"prefer-pascal-case":import_prefer_pascal_case.default,"story-exports":import_story_exports.default,"use-storybook-expect":import_use_storybook_expect.default,"use-storybook-testing-library":import_use_storybook_testing_library.default}}}});var require_recommended2=__commonJS({"src/configs/flat/recommended.ts"(exports2,module2){module2.exports=[{name:"storybook:recommended:setup",plugins:{get storybook(){return require_index()}}},{name:"storybook:recommended:stories-rules",files:["**/*.stories.@(ts|tsx|js|jsx|mjs|cjs)","**/*.story.@(ts|tsx|js|jsx|mjs|cjs)"],rules:{"react-hooks/rules-of-hooks":"off","import/no-anonymous-default-export":"off","storybook/await-interactions":"error","storybook/context-in-play-function":"error","storybook/default-exports":"error","storybook/hierarchy-separator":"warn","storybook/no-redundant-story-name":"warn","storybook/no-renderer-packages":"error","storybook/prefer-pascal-case":"warn","storybook/story-exports":"error","storybook/use-storybook-expect":"error","storybook/use-storybook-testing-library":"error"}},{name:"storybook:recommended:main-rules",files:[".storybook/main.@(js|cjs|mjs|ts)"],rules:{"storybook/no-uninstalled-addons":"error"}}]}});module.exports=require_recommended2();
