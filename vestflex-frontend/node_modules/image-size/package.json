{"name": "image-size", "version": "2.0.2", "description": "get dimensions of any image file", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}, "./fromFile": {"import": {"types": "./dist/fromFile.d.ts", "default": "./dist/fromFile.mjs"}, "require": {"types": "./dist/fromFile.d.ts", "default": "./dist/fromFile.cjs"}}, "./types/*": {"import": {"types": "./dist/types/*.d.ts", "default": "./dist/types/*.mjs"}, "require": {"types": "./dist/types/*.d.ts", "default": "./dist/types/*.cjs"}}}, "files": ["dist", "bin/image-size.js"], "engines": {"node": ">=16.x"}, "packageManager": "yarn@4.0.2", "bin": "bin/image-size.js", "scripts": {"lint": "biome check lib specs", "format": "biome format --write lib specs", "test": "TS_NODE_PROJECT=tsconfig.test.json c8 --reporter=text --reporter=lcov node --require ts-node/register --test --test-reporter=dot specs/*.spec.ts", "test:watch": "TS_NODE_PROJECT=tsconfig.test.json node --require ts-node/register --test --watch specs/*.spec.ts", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsup", "prepack": "yarn clean && yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "avif", "bmp", "cur", "gif", "heic", "heif", "icns", "ico", "jpeg", "jxl", "png", "psd", "svg", "tga", "tiff", "webp"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": "netroy <<EMAIL>> (http://netroy.in/)", "license": "MIT", "devDependencies": {"@biomejs/biome": "1.9.4", "@types/glob": "8.1.0", "@types/node": "18.19.39", "c8": "10.1.3", "glob": "10.4.2", "ts-node": "10.9.2", "tsup": "8.3.5", "typedoc": "0.25.13", "typescript": "5.4.5"}, "nyc": {"include": "lib", "exclude": "specs/*.spec.ts"}}