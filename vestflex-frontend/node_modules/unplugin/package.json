{"name": "unplugin", "type": "commonjs", "version": "1.16.1", "packageManager": "pnpm@8.15.9", "description": "Unified plugin system for build tools", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/unjs/unplugin.git"}, "sideEffects": false, "exports": {".": {"types": {"import": "./dist/index.d.mts", "require": "./dist/index.d.ts"}, "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./dist/webpack/loaders/*": "./dist/webpack/loaders/*.js", "./dist/rspack/loaders/*": "./dist/rspack/loaders/*.js"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "engines": {"node": ">=14.0.0"}, "scripts": {"build": "tsup", "dev": "tsup --watch src", "lint": "eslint --cache .", "lint:fix": "nr lint --fix", "typecheck": "tsc --noEmit", "docs:dev": "pnpm -C docs run dev", "docs:build": "pnpm -C docs run build", "docs:gen-files": "pnpm -C docs run gen-files", "prepublishOnly": "nr build", "release": "bumpp --all -x 'npx conventional-changelog -p angular -i CHANGELOG.md -s' && npm publish", "test": "nr test:build && vitest run --pool=forks", "test:build": "jiti scripts/buildFixtures.ts"}, "dependencies": {"acorn": "^8.14.0", "webpack-virtual-modules": "^0.6.2"}, "devDependencies": {"@ampproject/remapping": "^2.3.0", "@antfu/eslint-config": "^3.8.0", "@antfu/ni": "^0.23.0", "@farmfe/cli": "1.0.3", "@farmfe/core": "1.3.12", "@rspack/cli": "^1.0.14", "@rspack/core": "^1.0.14", "@types/fs-extra": "^11.0.4", "@types/node": "^22.8.1", "@types/webpack-sources": "^3.2.3", "bumpp": "^9.7.1", "conventional-changelog-cli": "^5.0.0", "esbuild": "^0.24.0", "esbuild-plugin-copy": "^2.1.1", "eslint": "^9.13.0", "fast-glob": "^3.3.2", "fs-extra": "^11.2.0", "jiti": "^2.3.3", "lint-staged": "^15.2.10", "magic-string": "^0.30.12", "picocolors": "^1.1.1", "rolldown": "^0.13.2", "rollup": "^4.24.2", "simple-git-hooks": "^2.11.1", "tsup": "^8.3.5", "typescript": "^5.6.3", "unplugin": "workspace:*", "unplugin-unused": "^0.2.3", "vite": "^5.4.10", "vitest": "^2.1.4", "webpack": "^5.95.0", "webpack-cli": "4.10.0"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}