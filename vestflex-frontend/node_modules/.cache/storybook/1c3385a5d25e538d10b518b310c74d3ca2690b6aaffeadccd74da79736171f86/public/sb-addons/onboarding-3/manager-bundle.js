try{
(() => {
  var __defProp = Object.defineProperty;
  var __getOwnPropNames = Object.getOwnPropertyNames;
  var __esm = (fn, res) => function() {
    return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;
  };
  var __export = (target, all) => {
    for (var name in all)
      __defProp(target, name, { get: all[name], enumerable: !0 });
  };

  // <define:module>
  var init_define_module = __esm({
    "<define:module>"() {
    }
  });

  // <define:process.env>
  var init_define_process_env = __esm({
    "<define:process.env>"() {
    }
  });

  // <define:process.env.NODE_PATH>
  var init_define_process_env_NODE_PATH = __esm({
    "<define:process.env.NODE_PATH>"() {
    }
  });

  // global-externals:react
  var react_default, Children, Component, Fragment, Profiler, PureComponent, StrictMode, Suspense, __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED, act, cloneElement, createContext, createElement, createFactory, createRef, forwardRef, isValidElement, lazy, memo, startTransition, unstable_act, useCallback, useContext, useDebugValue, useDeferredValue, useEffect, useId, useImperativeHandle, useInsertionEffect, useLayoutEffect, useMemo, useReducer, useRef, useState, useSyncExternalStore, useTransition, version, init_react = __esm({
    "global-externals:react"() {
      init_define_module();
      init_define_process_env();
      init_define_process_env_NODE_PATH();
      react_default = __REACT__, { Children, Component, Fragment, Profiler, PureComponent, StrictMode, Suspense, __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED, act, cloneElement, createContext, createElement, createFactory, createRef, forwardRef, isValidElement, lazy, memo, startTransition, unstable_act, useCallback, useContext, useDebugValue, useDeferredValue, useEffect, useId, useImperativeHandle, useInsertionEffect, useLayoutEffect, useMemo, useReducer, useRef, useState, useSyncExternalStore, useTransition, version } = __REACT__;
    }
  });

  // global-externals:react-dom
  var react_dom_default, __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED2, createPortal, createRoot, findDOMNode, flushSync, hydrate, hydrateRoot, render, unmountComponentAtNode, unstable_batchedUpdates, unstable_renderSubtreeIntoContainer, version2, init_react_dom = __esm({
    "global-externals:react-dom"() {
      init_define_module();
      init_define_process_env();
      init_define_process_env_NODE_PATH();
      react_dom_default = __REACT_DOM__, { __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED2, createPortal, createRoot, findDOMNode, flushSync, hydrate, hydrateRoot, render, unmountComponentAtNode, unstable_batchedUpdates, unstable_renderSubtreeIntoContainer, version: version2 } = __REACT_DOM__;
    }
  });

  // node_modules/storybook/dist/controls/index.js
  var o, init_controls = __esm({
    "node_modules/storybook/dist/controls/index.js"() {
      init_define_module();
      init_define_process_env();
      init_define_process_env_NODE_PATH();
      o = "addon-controls";
    }
  });

  // global-externals:storybook/internal/core-events
  var core_events_default, ARGTYPES_INFO_REQUEST, ARGTYPES_INFO_RESPONSE, CHANNEL_CREATED, CHANNEL_WS_DISCONNECT, CONFIG_ERROR, CREATE_NEW_STORYFILE_REQUEST, CREATE_NEW_STORYFILE_RESPONSE, CURRENT_STORY_WAS_SET, DOCS_PREPARED, DOCS_RENDERED, FILE_COMPONENT_SEARCH_REQUEST, FILE_COMPONENT_SEARCH_RESPONSE, FORCE_REMOUNT, FORCE_RE_RENDER, GLOBALS_UPDATED, NAVIGATE_URL, PLAY_FUNCTION_THREW_EXCEPTION, PRELOAD_ENTRIES, PREVIEW_BUILDER_PROGRESS, PREVIEW_KEYDOWN, REGISTER_SUBSCRIPTION, REQUEST_WHATS_NEW_DATA, RESET_STORY_ARGS, RESULT_WHATS_NEW_DATA, SAVE_STORY_REQUEST, SAVE_STORY_RESPONSE, SELECT_STORY, SET_CONFIG, SET_CURRENT_STORY, SET_FILTER, SET_GLOBALS, SET_INDEX, SET_STORIES, SET_WHATS_NEW_CACHE, SHARED_STATE_CHANGED, SHARED_STATE_SET, STORIES_COLLAPSE_ALL, STORIES_EXPAND_ALL, STORY_ARGS_UPDATED, STORY_CHANGED, STORY_ERRORED, STORY_FINISHED, STORY_HOT_UPDATED, STORY_INDEX_INVALIDATED, STORY_MISSING, STORY_PREPARED, STORY_RENDERED, STORY_RENDER_PHASE_CHANGED, STORY_SPECIFIED, STORY_THREW_EXCEPTION, STORY_UNCHANGED, TELEMETRY_ERROR, TOGGLE_WHATS_NEW_NOTIFICATIONS, UNHANDLED_ERRORS_WHILE_PLAYING, UPDATE_GLOBALS, UPDATE_QUERY_PARAMS, UPDATE_STORY_ARGS, init_core_events = __esm({
    "global-externals:storybook/internal/core-events"() {
      init_define_module();
      init_define_process_env();
      init_define_process_env_NODE_PATH();
      core_events_default = __STORYBOOK_CORE_EVENTS__, { ARGTYPES_INFO_REQUEST, ARGTYPES_INFO_RESPONSE, CHANNEL_CREATED, CHANNEL_WS_DISCONNECT, CONFIG_ERROR, CREATE_NEW_STORYFILE_REQUEST, CREATE_NEW_STORYFILE_RESPONSE, CURRENT_STORY_WAS_SET, DOCS_PREPARED, DOCS_RENDERED, FILE_COMPONENT_SEARCH_REQUEST, FILE_COMPONENT_SEARCH_RESPONSE, FORCE_REMOUNT, FORCE_RE_RENDER, GLOBALS_UPDATED, NAVIGATE_URL, PLAY_FUNCTION_THREW_EXCEPTION, PRELOAD_ENTRIES, PREVIEW_BUILDER_PROGRESS, PREVIEW_KEYDOWN, REGISTER_SUBSCRIPTION, REQUEST_WHATS_NEW_DATA, RESET_STORY_ARGS, RESULT_WHATS_NEW_DATA, SAVE_STORY_REQUEST, SAVE_STORY_RESPONSE, SELECT_STORY, SET_CONFIG, SET_CURRENT_STORY, SET_FILTER, SET_GLOBALS, SET_INDEX, SET_STORIES, SET_WHATS_NEW_CACHE, SHARED_STATE_CHANGED, SHARED_STATE_SET, STORIES_COLLAPSE_ALL, STORIES_EXPAND_ALL, STORY_ARGS_UPDATED, STORY_CHANGED, STORY_ERRORED, STORY_FINISHED, STORY_HOT_UPDATED, STORY_INDEX_INVALIDATED, STORY_MISSING, STORY_PREPARED, STORY_RENDERED, STORY_RENDER_PHASE_CHANGED, STORY_SPECIFIED, STORY_THREW_EXCEPTION, STORY_UNCHANGED, TELEMETRY_ERROR, TOGGLE_WHATS_NEW_NOTIFICATIONS, UNHANDLED_ERRORS_WHILE_PLAYING, UPDATE_GLOBALS, UPDATE_QUERY_PARAMS, UPDATE_STORY_ARGS } = __STORYBOOK_CORE_EVENTS__;
    }
  });

  // global-externals:storybook/internal/components
  var components_default, A, ActionBar, AddonPanel, Badge, Bar, Blockquote, Button, Checkbox, ClipboardCode, Code, DL, Div, DocumentWrapper, EmptyTabContent, ErrorFormatter, FlexBar, Form, H1, H2, H3, H4, H5, H6, HR, IconButton, Img, LI, Link, ListItem, Loader, Modal, OL, P, Placeholder, Pre, ProgressSpinner, ResetWrapper, ScrollArea, Separator, Spaced, Span, StorybookIcon, StorybookLogo, SyntaxHighlighter, TT, TabBar, TabButton, TabWrapper, Table, Tabs, TabsState, TooltipLinkList, TooltipMessage, TooltipNote, UL, WithTooltip, WithTooltipPure, Zoom, codeCommon, components, createCopyToClipboardFunction, getStoryHref, interleaveSeparators, nameSpaceClassNames, resetComponents, withReset, init_components = __esm({
    "global-externals:storybook/internal/components"() {
      init_define_module();
      init_define_process_env();
      init_define_process_env_NODE_PATH();
      components_default = __STORYBOOK_COMPONENTS__, { A, ActionBar, AddonPanel, Badge, Bar, Blockquote, Button, Checkbox, ClipboardCode, Code, DL, Div, DocumentWrapper, EmptyTabContent, ErrorFormatter, FlexBar, Form, H1, H2, H3, H4, H5, H6, HR, IconButton, Img, LI, Link, ListItem, Loader, Modal, OL, P, Placeholder, Pre, ProgressSpinner, ResetWrapper, ScrollArea, Separator, Spaced, Span, StorybookIcon, StorybookLogo, SyntaxHighlighter, TT, TabBar, TabButton, TabWrapper, Table, Tabs, TabsState, TooltipLinkList, TooltipMessage, TooltipNote, UL, WithTooltip, WithTooltipPure, Zoom, codeCommon, components, createCopyToClipboardFunction, getStoryHref, interleaveSeparators, nameSpaceClassNames, resetComponents, withReset } = __STORYBOOK_COMPONENTS__;
    }
  });

  // global-externals:storybook/theming
  var theming_default, CacheProvider, ClassNames, Global, ThemeProvider, background, color, convert, create, createCache, createGlobal, createReset, css, darken, ensure, ignoreSsrWarning, isPropValid, jsx, keyframes, lighten, styled, themes, typography, useTheme, withTheme, init_theming = __esm({
    "global-externals:storybook/theming"() {
      init_define_module();
      init_define_process_env();
      init_define_process_env_NODE_PATH();
      theming_default = __STORYBOOK_THEMING__, { CacheProvider, ClassNames, Global, ThemeProvider, background, color, convert, create, createCache, createGlobal, createReset, css, darken, ensure, ignoreSsrWarning, isPropValid, jsx, keyframes, lighten, styled, themes, typography, useTheme, withTheme } = __STORYBOOK_THEMING__;
    }
  });

  // global-externals:@storybook/icons
  var icons_default, AccessibilityAltIcon, AccessibilityIcon, AccessibilityIgnoredIcon, AddIcon, AdminIcon, AlertAltIcon, AlertIcon, AlignLeftIcon, AlignRightIcon, AppleIcon, ArrowBottomLeftIcon, ArrowBottomRightIcon, ArrowDownIcon, ArrowLeftIcon, ArrowRightIcon, ArrowSolidDownIcon, ArrowSolidLeftIcon, ArrowSolidRightIcon, ArrowSolidUpIcon, ArrowTopLeftIcon, ArrowTopRightIcon, ArrowUpIcon, AzureDevOpsIcon, BackIcon, BasketIcon, BatchAcceptIcon, BatchDenyIcon, BeakerIcon, BellIcon, BitbucketIcon, BoldIcon, BookIcon, BookmarkHollowIcon, BookmarkIcon, BottomBarIcon, BottomBarToggleIcon, BoxIcon, BranchIcon, BrowserIcon, ButtonIcon, CPUIcon, CalendarIcon, CameraIcon, CameraStabilizeIcon, CategoryIcon, CertificateIcon, ChangedIcon, ChatIcon, CheckIcon, ChevronDownIcon, ChevronLeftIcon, ChevronRightIcon, ChevronSmallDownIcon, ChevronSmallLeftIcon, ChevronSmallRightIcon, ChevronSmallUpIcon, ChevronUpIcon, ChromaticIcon, ChromeIcon, CircleHollowIcon, CircleIcon, ClearIcon, CloseAltIcon, CloseIcon, CloudHollowIcon, CloudIcon, CogIcon, CollapseIcon, CommandIcon, CommentAddIcon, CommentIcon, CommentsIcon, CommitIcon, CompassIcon, ComponentDrivenIcon, ComponentIcon, ContrastIcon, ContrastIgnoredIcon, ControlsIcon, CopyIcon, CreditIcon, CrossIcon, DashboardIcon, DatabaseIcon, DeleteIcon, DiamondIcon, DirectionIcon, DiscordIcon, DocChartIcon, DocListIcon, DocumentIcon, DownloadIcon, DragIcon, EditIcon, EllipsisIcon, EmailIcon, ExpandAltIcon, ExpandIcon, EyeCloseIcon, EyeIcon, FaceHappyIcon, FaceNeutralIcon, FaceSadIcon, FacebookIcon, FailedIcon, FastForwardIcon, FigmaIcon, FilterIcon, FlagIcon, FolderIcon, FormIcon, GDriveIcon, GithubIcon, GitlabIcon, GlobeIcon, GoogleIcon, GraphBarIcon, GraphLineIcon, GraphqlIcon, GridAltIcon, GridIcon, GrowIcon, HeartHollowIcon, HeartIcon, HomeIcon, HourglassIcon, InfoIcon, ItalicIcon, JumpToIcon, KeyIcon, LightningIcon, LightningOffIcon, LinkBrokenIcon, LinkIcon, LinkedinIcon, LinuxIcon, ListOrderedIcon, ListUnorderedIcon, LocationIcon, LockIcon, MarkdownIcon, MarkupIcon, MediumIcon, MemoryIcon, MenuIcon, MergeIcon, MirrorIcon, MobileIcon, MoonIcon, NutIcon, OutboxIcon, OutlineIcon, PaintBrushIcon, PaperClipIcon, ParagraphIcon, PassedIcon, PhoneIcon, PhotoDragIcon, PhotoIcon, PhotoStabilizeIcon, PinAltIcon, PinIcon, PlayAllHollowIcon, PlayBackIcon, PlayHollowIcon, PlayIcon, PlayNextIcon, PlusIcon, PointerDefaultIcon, PointerHandIcon, PowerIcon, PrintIcon, ProceedIcon, ProfileIcon, PullRequestIcon, QuestionIcon, RSSIcon, RedirectIcon, ReduxIcon, RefreshIcon, ReplyIcon, RepoIcon, RequestChangeIcon, RewindIcon, RulerIcon, SaveIcon, SearchIcon, ShareAltIcon, ShareIcon, ShieldIcon, SideBySideIcon, SidebarAltIcon, SidebarAltToggleIcon, SidebarIcon, SidebarToggleIcon, SpeakerIcon, StackedIcon, StarHollowIcon, StarIcon, StatusFailIcon, StatusIcon, StatusPassIcon, StatusWarnIcon, StickerIcon, StopAltHollowIcon, StopAltIcon, StopIcon, StorybookIcon2, StructureIcon, SubtractIcon, SunIcon, SupportIcon, SweepIcon, SwitchAltIcon, SyncIcon, TabletIcon, ThumbsUpIcon, TimeIcon, TimerIcon, TransferIcon, TrashIcon, TwitterIcon, TypeIcon, UbuntuIcon, UndoIcon, UnfoldIcon, UnlockIcon, UnpinIcon, UploadIcon, UserAddIcon, UserAltIcon, UserIcon, UsersIcon, VSCodeIcon, VerifiedIcon, VideoIcon, WandIcon, WatchIcon, WindowsIcon, WrenchIcon, XIcon, YoutubeIcon, ZoomIcon, ZoomOutIcon, ZoomResetIcon, iconList, init_icons = __esm({
    "global-externals:@storybook/icons"() {
      init_define_module();
      init_define_process_env();
      init_define_process_env_NODE_PATH();
      icons_default = __STORYBOOK_ICONS__, { AccessibilityAltIcon, AccessibilityIcon, AccessibilityIgnoredIcon, AddIcon, AdminIcon, AlertAltIcon, AlertIcon, AlignLeftIcon, AlignRightIcon, AppleIcon, ArrowBottomLeftIcon, ArrowBottomRightIcon, ArrowDownIcon, ArrowLeftIcon, ArrowRightIcon, ArrowSolidDownIcon, ArrowSolidLeftIcon, ArrowSolidRightIcon, ArrowSolidUpIcon, ArrowTopLeftIcon, ArrowTopRightIcon, ArrowUpIcon, AzureDevOpsIcon, BackIcon, BasketIcon, BatchAcceptIcon, BatchDenyIcon, BeakerIcon, BellIcon, BitbucketIcon, BoldIcon, BookIcon, BookmarkHollowIcon, BookmarkIcon, BottomBarIcon, BottomBarToggleIcon, BoxIcon, BranchIcon, BrowserIcon, ButtonIcon, CPUIcon, CalendarIcon, CameraIcon, CameraStabilizeIcon, CategoryIcon, CertificateIcon, ChangedIcon, ChatIcon, CheckIcon, ChevronDownIcon, ChevronLeftIcon, ChevronRightIcon, ChevronSmallDownIcon, ChevronSmallLeftIcon, ChevronSmallRightIcon, ChevronSmallUpIcon, ChevronUpIcon, ChromaticIcon, ChromeIcon, CircleHollowIcon, CircleIcon, ClearIcon, CloseAltIcon, CloseIcon, CloudHollowIcon, CloudIcon, CogIcon, CollapseIcon, CommandIcon, CommentAddIcon, CommentIcon, CommentsIcon, CommitIcon, CompassIcon, ComponentDrivenIcon, ComponentIcon, ContrastIcon, ContrastIgnoredIcon, ControlsIcon, CopyIcon, CreditIcon, CrossIcon, DashboardIcon, DatabaseIcon, DeleteIcon, DiamondIcon, DirectionIcon, DiscordIcon, DocChartIcon, DocListIcon, DocumentIcon, DownloadIcon, DragIcon, EditIcon, EllipsisIcon, EmailIcon, ExpandAltIcon, ExpandIcon, EyeCloseIcon, EyeIcon, FaceHappyIcon, FaceNeutralIcon, FaceSadIcon, FacebookIcon, FailedIcon, FastForwardIcon, FigmaIcon, FilterIcon, FlagIcon, FolderIcon, FormIcon, GDriveIcon, GithubIcon, GitlabIcon, GlobeIcon, GoogleIcon, GraphBarIcon, GraphLineIcon, GraphqlIcon, GridAltIcon, GridIcon, GrowIcon, HeartHollowIcon, HeartIcon, HomeIcon, HourglassIcon, InfoIcon, ItalicIcon, JumpToIcon, KeyIcon, LightningIcon, LightningOffIcon, LinkBrokenIcon, LinkIcon, LinkedinIcon, LinuxIcon, ListOrderedIcon, ListUnorderedIcon, LocationIcon, LockIcon, MarkdownIcon, MarkupIcon, MediumIcon, MemoryIcon, MenuIcon, MergeIcon, MirrorIcon, MobileIcon, MoonIcon, NutIcon, OutboxIcon, OutlineIcon, PaintBrushIcon, PaperClipIcon, ParagraphIcon, PassedIcon, PhoneIcon, PhotoDragIcon, PhotoIcon, PhotoStabilizeIcon, PinAltIcon, PinIcon, PlayAllHollowIcon, PlayBackIcon, PlayHollowIcon, PlayIcon, PlayNextIcon, PlusIcon, PointerDefaultIcon, PointerHandIcon, PowerIcon, PrintIcon, ProceedIcon, ProfileIcon, PullRequestIcon, QuestionIcon, RSSIcon, RedirectIcon, ReduxIcon, RefreshIcon, ReplyIcon, RepoIcon, RequestChangeIcon, RewindIcon, RulerIcon, SaveIcon, SearchIcon, ShareAltIcon, ShareIcon, ShieldIcon, SideBySideIcon, SidebarAltIcon, SidebarAltToggleIcon, SidebarIcon, SidebarToggleIcon, SpeakerIcon, StackedIcon, StarHollowIcon, StarIcon, StatusFailIcon, StatusIcon, StatusPassIcon, StatusWarnIcon, StickerIcon, StopAltHollowIcon, StopAltIcon, StopIcon, StorybookIcon: StorybookIcon2, StructureIcon, SubtractIcon, SunIcon, SupportIcon, SweepIcon, SwitchAltIcon, SyncIcon, TabletIcon, ThumbsUpIcon, TimeIcon, TimerIcon, TransferIcon, TrashIcon, TwitterIcon, TypeIcon, UbuntuIcon, UndoIcon, UnfoldIcon, UnlockIcon, UnpinIcon, UploadIcon, UserAddIcon, UserAltIcon, UserIcon, UsersIcon, VSCodeIcon, VerifiedIcon, VideoIcon, WandIcon, WatchIcon, WindowsIcon, WrenchIcon, XIcon, YoutubeIcon, ZoomIcon, ZoomOutIcon, ZoomResetIcon, iconList } = __STORYBOOK_ICONS__;
    }
  });

  // node_modules/@storybook/addon-onboarding/dist/Onboarding-C2PY5T7U.js
  var Onboarding_C2PY5T7U_exports = {};
  __export(Onboarding_C2PY5T7U_exports, {
    default: () => Onboarding
  });
  function y(y2, z2 = {}) {
    let { colors: A2 = a, duration: H22 = o2, force: F2 = n, particleCount: O = i, particleShape: j = c, particleSize: E = s, particleClass: $ = l, destroyAfterDone: q = d, stageHeight: D = p, stageWidth: J = u } = z2;
    (function(e2) {
      if (document.querySelector("style[data-neoconfetti]")) return;
      let t2 = W("style");
      t2.dataset.neoconfetti = "", t2.textContent = e2, _(document.head, t2);
    })(e), y2.classList.add(r), y2.style.setProperty("--sh", D + "px");
    let I = [], G = [], K = () => P2(b() * (N - 1)), Q = (e2, t2) => j !== "rectangles" && (e2 === "circles" || k(t2));
    function R(e2, t2) {
      let r2 = K(), a2 = Q(j, r2), o22 = (t3, r3) => e2.style.setProperty(t3, r3 + "");
      o22("--xlp", C(x(L(t2, 90) - 180), 0, 180, -J / 2, J / 2) + "px"), o22("--dc", H22 - P2(1e3 * b()) + "ms");
      let n2 = b() < m ? w(b() * h, 2) : 0;
      o22("--x1", n2), o22("--x2", -1 * n2), o22("--x3", n2), o22("--x4", w(x(C(x(L(t2, 90) - 180), 0, 180, -1, 1)), 4)), o22("--y1", w(b() * v, 4)), o22("--y2", w(b() * F2 * (M() ? 1 : -1), 4)), o22("--y3", v), o22("--y4", w(B(C(x(t2 - 180), 0, 180, F2, -F2), 0), 4)), o22("--w", (a2 ? E : P2(4 * b()) + E / 2) + "px"), o22("--h", (a2 ? E : P2(2 * b()) + E) + "px");
      let i2 = r2.toString(2).padStart(3, "0").split("");
      o22("--hr", i2.map((e3) => +e3 / 2 + "").join(" ")), o22("--r", i2.join(" ")), o22("--rd", w(b() * (g - f) + f) + "ms"), o22("--br", a2 ? "50%" : 0);
    }
    let U;
    function V() {
      y2.innerHTML = "", clearTimeout(U), I = S(O, A2), G = function(e2, r2 = [], a2) {
        let o22 = [];
        for (let { color: n2 } of r2) {
          let r3 = W("div");
          r3.className = `${t} ${a2}`, r3.style.setProperty("--bgc", n2);
          let i2 = W("div");
          _(r3, i2), _(e2, r3), o22.push(r3);
        }
        return o22;
      }(y2, I, $);
      for (let [e2, t2] of T(G)) R(t2, I[+e2].degree);
      U = setTimeout(() => {
        q && (y2.innerHTML = "");
      }, H22);
    }
    return V(), { update(e2) {
      let r2 = e2.particleCount ?? i, f2 = e2.particleShape ?? c, g2 = e2.particleSize ?? s, m2 = e2.particleClass ?? l, h2 = e2.colors ?? a, v2 = e2.stageHeight ?? p, x2 = e2.duration ?? o2, b2 = e2.force ?? n, P22 = e2.stageWidth ?? u, B2 = e2.destroyAfterDone ?? d;
      I = S(r2, h2);
      let W2 = !1;
      if (r2 === O) {
        G = Array.from(y2.querySelectorAll(`.${t}`));
        for (let [e3, { color: t2 }] of T(I)) {
          let r3 = G[+e3];
          JSON.stringify(A2) !== JSON.stringify(h2) && r3.style.setProperty("--bgc", t2), f2 !== j && r3.style.setProperty("--br", Q(f2, K()) ? "50%" : "0"), m2 !== $ && ($ && r3.classList.remove($), m2 && r3.classList.add(m2));
        }
      } else W2 = !0;
      q && !B2 && clearTimeout(U), y2.style.setProperty("--sh", v2 + "px"), H22 = x2, A2 = h2, F2 = b2, O = r2, j = f2, E = g2, $ = m2, q = B2, D = v2, J = P22, W2 && V();
    }, destroy() {
      y2.innerHTML = "", clearTimeout(U);
    } };
  }
  function F({ class: e2, ...t2 }) {
    let r2 = useRef(null), a2 = useRef();
    return useEffect(() => {
      if (typeof window < "u" && r2.current) {
        if (a2.current) return a2.current.update(t2), a2.current.destroy;
        a2.current = y(r2.current, t2);
      }
    }, [t2]), createElement("div", { ref: r2, className: e2 });
  }
  function HighlightElement({ targetSelector, pulsating = !1 }) {
    return useEffect(() => {
      let element = document.querySelector(targetSelector);
      if (element) if (pulsating) {
        element.style.animation = "pulsate 3s infinite", element.style.transformOrigin = "center", element.style.animationTimingFunction = "ease-in-out";
        let keyframes2 = `
        @keyframes pulsate {
          0% {
            box-shadow: rgba(2,156,253,1) 0 0 2px 1px, 0 0 0 0 rgba(2, 156, 253, 0.7), 0 0 0 0 rgba(2, 156, 253, 0.4);
          }
          50% {
            box-shadow: rgba(2,156,253,1) 0 0 2px 1px, 0 0 0 20px rgba(2, 156, 253, 0), 0 0 0 40px rgba(2, 156, 253, 0);
          }
          100% {
            box-shadow: rgba(2,156,253,1) 0 0 2px 1px, 0 0 0 0 rgba(2, 156, 253, 0), 0 0 0 0 rgba(2, 156, 253, 0);
          }
        }
      `, style = document.createElement("style");
        style.id = "sb-onboarding-pulsating-effect", style.innerHTML = keyframes2, document.head.appendChild(style);
      } else element.style.boxShadow = "rgba(2,156,253,1) 0 0 2px 1px";
      return () => {
        let styleElement = document.querySelector("#sb-onboarding-pulsating-effect");
        styleElement && styleElement.remove(), element && (element.style.animation = "", element.style.boxShadow = "");
      };
    }, [targetSelector, pulsating]), null;
  }
  function isOfType(type) {
    return (value) => typeof value === type;
  }
  function equalArray(left, right) {
    let { length } = left;
    if (length !== right.length) return !1;
    for (let index = length; index-- !== 0; ) if (!equal(left[index], right[index])) return !1;
    return !0;
  }
  function equalArrayBuffer(left, right) {
    if (left.byteLength !== right.byteLength) return !1;
    let view1 = new DataView(left.buffer), view2 = new DataView(right.buffer), index = left.byteLength;
    for (; index--; ) if (view1.getUint8(index) !== view2.getUint8(index)) return !1;
    return !0;
  }
  function equalMap(left, right) {
    if (left.size !== right.size) return !1;
    for (let index of left.entries()) if (!right.has(index[0])) return !1;
    for (let index of left.entries()) if (!equal(index[1], right.get(index[0]))) return !1;
    return !0;
  }
  function equalSet(left, right) {
    if (left.size !== right.size) return !1;
    for (let index of left.entries()) if (!right.has(index[0])) return !1;
    return !0;
  }
  function equal(left, right) {
    if (left === right) return !0;
    if (left && isObject(left) && right && isObject(right)) {
      if (left.constructor !== right.constructor) return !1;
      if (Array.isArray(left) && Array.isArray(right)) return equalArray(left, right);
      if (left instanceof Map && right instanceof Map) return equalMap(left, right);
      if (left instanceof Set && right instanceof Set) return equalSet(left, right);
      if (ArrayBuffer.isView(left) && ArrayBuffer.isView(right)) return equalArrayBuffer(left, right);
      if (isRegex(left) && isRegex(right)) return left.source === right.source && left.flags === right.flags;
      if (left.valueOf !== Object.prototype.valueOf) return left.valueOf() === right.valueOf();
      if (left.toString !== Object.prototype.toString) return left.toString() === right.toString();
      let leftKeys = Object.keys(left), rightKeys = Object.keys(right);
      if (leftKeys.length !== rightKeys.length) return !1;
      for (let index = leftKeys.length; index-- !== 0; ) if (!Object.prototype.hasOwnProperty.call(right, leftKeys[index])) return !1;
      for (let index = leftKeys.length; index-- !== 0; ) {
        let key = leftKeys[index];
        if (!(key === "_owner" && left.$$typeof) && !equal(left[key], right[key])) return !1;
      }
      return !0;
    }
    return Number.isNaN(left) && Number.isNaN(right) ? !0 : left === right;
  }
  function getObjectType(value) {
    let objectTypeName = Object.prototype.toString.call(value).slice(8, -1);
    if (/HTML\w+Element/.test(objectTypeName)) return "HTMLElement";
    if (isObjectType(objectTypeName)) return objectTypeName;
  }
  function isObjectOfType(type) {
    return (value) => getObjectType(value) === type;
  }
  function isObjectType(name) {
    return objectTypes.includes(name);
  }
  function isOfType2(type) {
    return (value) => typeof value === type;
  }
  function isPrimitiveType(name) {
    return primitiveTypes.includes(name);
  }
  function is(value) {
    if (value === null) return "null";
    switch (typeof value) {
      case "bigint":
        return "bigint";
      case "boolean":
        return "boolean";
      case "number":
        return "number";
      case "string":
        return "string";
      case "symbol":
        return "symbol";
      case "undefined":
        return "undefined";
    }
    return is.array(value) ? "Array" : is.plainFunction(value) ? "Function" : getObjectType(value) || "Object";
  }
  function canHaveLength(...arguments_) {
    return arguments_.every((d2) => src_default.string(d2) || src_default.array(d2) || src_default.plainObject(d2));
  }
  function checkEquality(left, right, value) {
    return isSameType(left, right) ? [left, right].every(src_default.array) ? !left.some(hasValue(value)) && right.some(hasValue(value)) : [left, right].every(src_default.plainObject) ? !Object.entries(left).some(hasEntry(value)) && Object.entries(right).some(hasEntry(value)) : right === value : !1;
  }
  function compareNumbers(previousData, data, options) {
    let { actual, key, previous, type } = options, left = nested(previousData, key), right = nested(data, key), changed = [left, right].every(src_default.number) && (type === "increased" ? left < right : left > right);
    return src_default.undefined(actual) || (changed = changed && right === actual), src_default.undefined(previous) || (changed = changed && left === previous), changed;
  }
  function compareValues(previousData, data, options) {
    let { key, type, value } = options, left = nested(previousData, key), right = nested(data, key), primary = type === "added" ? left : right, secondary = type === "added" ? right : left;
    if (!src_default.nullOrUndefined(value)) {
      if (src_default.defined(primary)) {
        if (src_default.array(primary) || src_default.plainObject(primary)) return checkEquality(primary, secondary, value);
      } else return equal(secondary, value);
      return !1;
    }
    return [left, right].every(src_default.array) ? !secondary.every(isEqualPredicate(primary)) : [left, right].every(src_default.plainObject) ? hasExtraKeys(Object.keys(primary), Object.keys(secondary)) : ![left, right].every((d2) => src_default.primitive(d2) && src_default.defined(d2)) && (type === "added" ? !src_default.defined(left) && src_default.defined(right) : src_default.defined(left) && !src_default.defined(right));
  }
  function getIterables(previousData, data, { key } = {}) {
    let left = nested(previousData, key), right = nested(data, key);
    if (!isSameType(left, right)) throw new TypeError("Inputs have different types");
    if (!canHaveLength(left, right)) throw new TypeError("Inputs don't have length");
    return [left, right].every(src_default.plainObject) && (left = Object.keys(left), right = Object.keys(right)), [left, right];
  }
  function hasEntry(input) {
    return ([key, value]) => src_default.array(input) ? equal(input, value) || input.some((d2) => equal(d2, value) || src_default.array(value) && isEqualPredicate(value)(d2)) : src_default.plainObject(input) && input[key] ? !!input[key] && equal(input[key], value) : equal(input, value);
  }
  function hasExtraKeys(left, right) {
    return right.some((d2) => !left.includes(d2));
  }
  function hasValue(input) {
    return (value) => src_default.array(input) ? input.some((d2) => equal(d2, value) || src_default.array(value) && isEqualPredicate(value)(d2)) : equal(input, value);
  }
  function includesOrEqualsTo(previousValue, value) {
    return src_default.array(previousValue) ? previousValue.some((d2) => equal(d2, value)) : equal(previousValue, value);
  }
  function isEqualPredicate(data) {
    return (value) => data.some((d2) => equal(d2, value));
  }
  function isSameType(...arguments_) {
    return arguments_.every(src_default.array) || arguments_.every(src_default.number) || arguments_.every(src_default.plainObject) || arguments_.every(src_default.string);
  }
  function nested(data, property) {
    return src_default.plainObject(data) || src_default.array(data) ? src_default.string(property) ? property.split(".").reduce((acc, d2) => acc && acc[d2], data) : src_default.number(property) ? data[property] : data : data;
  }
  function treeChanges(previousData, data) {
    if ([previousData, data].some(src_default.nullOrUndefined)) throw new Error("Missing required parameters");
    if (![previousData, data].every((d2) => src_default.plainObject(d2) || src_default.array(d2))) throw new Error("Expected plain objects or array");
    return { added: (key, value) => {
      try {
        return compareValues(previousData, data, { key, type: "added", value });
      } catch {
        return !1;
      }
    }, changed: (key, actual, previous) => {
      try {
        let left = nested(previousData, key), right = nested(data, key), hasActual = src_default.defined(actual), hasPrevious = src_default.defined(previous);
        if (hasActual || hasPrevious) {
          let leftComparator = hasPrevious ? includesOrEqualsTo(previous, left) : !includesOrEqualsTo(actual, left), rightComparator = includesOrEqualsTo(actual, right);
          return leftComparator && rightComparator;
        }
        return [left, right].every(src_default.array) || [left, right].every(src_default.plainObject) ? !equal(left, right) : left !== right;
      } catch {
        return !1;
      }
    }, changedFrom: (key, previous, actual) => {
      if (!src_default.defined(key)) return !1;
      try {
        let left = nested(previousData, key), right = nested(data, key), hasActual = src_default.defined(actual);
        return includesOrEqualsTo(previous, left) && (hasActual ? includesOrEqualsTo(actual, right) : !hasActual);
      } catch {
        return !1;
      }
    }, decreased: (key, actual, previous) => {
      if (!src_default.defined(key)) return !1;
      try {
        return compareNumbers(previousData, data, { key, actual, previous, type: "decreased" });
      } catch {
        return !1;
      }
    }, emptied: (key) => {
      try {
        let [left, right] = getIterables(previousData, data, { key });
        return !!left.length && !right.length;
      } catch {
        return !1;
      }
    }, filled: (key) => {
      try {
        let [left, right] = getIterables(previousData, data, { key });
        return !left.length && !!right.length;
      } catch {
        return !1;
      }
    }, increased: (key, actual, previous) => {
      if (!src_default.defined(key)) return !1;
      try {
        return compareNumbers(previousData, data, { key, actual, previous, type: "increased" });
      } catch {
        return !1;
      }
    }, removed: (key, value) => {
      try {
        return compareValues(previousData, data, { key, type: "removed", value });
      } catch {
        return !1;
      }
    } };
  }
  function microtaskDebounce(fn) {
    var called = !1;
    return function() {
      called || (called = !0, window.Promise.resolve().then(function() {
        called = !1, fn();
      }));
    };
  }
  function taskDebounce(fn) {
    var scheduled = !1;
    return function() {
      scheduled || (scheduled = !0, setTimeout(function() {
        scheduled = !1, fn();
      }, timeoutDuration));
    };
  }
  function isFunction2(functionToCheck) {
    var getType = {};
    return functionToCheck && getType.toString.call(functionToCheck) === "[object Function]";
  }
  function getStyleComputedProperty(element, property) {
    if (element.nodeType !== 1) return [];
    var window2 = element.ownerDocument.defaultView, css2 = window2.getComputedStyle(element, null);
    return property ? css2[property] : css2;
  }
  function getParentNode(element) {
    return element.nodeName === "HTML" ? element : element.parentNode || element.host;
  }
  function getScrollParent(element) {
    if (!element) return document.body;
    switch (element.nodeName) {
      case "HTML":
      case "BODY":
        return element.ownerDocument.body;
      case "#document":
        return element.body;
    }
    var _getStyleComputedProp = getStyleComputedProperty(element), overflow = _getStyleComputedProp.overflow, overflowX = _getStyleComputedProp.overflowX, overflowY = _getStyleComputedProp.overflowY;
    return /(auto|scroll|overlay)/.test(overflow + overflowY + overflowX) ? element : getScrollParent(getParentNode(element));
  }
  function getReferenceNode(reference) {
    return reference && reference.referenceNode ? reference.referenceNode : reference;
  }
  function isIE(version3) {
    return version3 === 11 ? isIE11 : version3 === 10 ? isIE10 : isIE11 || isIE10;
  }
  function getOffsetParent(element) {
    if (!element) return document.documentElement;
    for (var noOffsetParent = isIE(10) ? document.body : null, offsetParent = element.offsetParent || null; offsetParent === noOffsetParent && element.nextElementSibling; ) offsetParent = (element = element.nextElementSibling).offsetParent;
    var nodeName = offsetParent && offsetParent.nodeName;
    return !nodeName || nodeName === "BODY" || nodeName === "HTML" ? element ? element.ownerDocument.documentElement : document.documentElement : ["TH", "TD", "TABLE"].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, "position") === "static" ? getOffsetParent(offsetParent) : offsetParent;
  }
  function isOffsetContainer(element) {
    var nodeName = element.nodeName;
    return nodeName === "BODY" ? !1 : nodeName === "HTML" || getOffsetParent(element.firstElementChild) === element;
  }
  function getRoot(node) {
    return node.parentNode !== null ? getRoot(node.parentNode) : node;
  }
  function findCommonOffsetParent(element1, element2) {
    if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) return document.documentElement;
    var order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING, start = order ? element1 : element2, end = order ? element2 : element1, range = document.createRange();
    range.setStart(start, 0), range.setEnd(end, 0);
    var commonAncestorContainer = range.commonAncestorContainer;
    if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) return isOffsetContainer(commonAncestorContainer) ? commonAncestorContainer : getOffsetParent(commonAncestorContainer);
    var element1root = getRoot(element1);
    return element1root.host ? findCommonOffsetParent(element1root.host, element2) : findCommonOffsetParent(element1, getRoot(element2).host);
  }
  function getScroll(element) {
    var side = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "top", upperSide = side === "top" ? "scrollTop" : "scrollLeft", nodeName = element.nodeName;
    if (nodeName === "BODY" || nodeName === "HTML") {
      var html = element.ownerDocument.documentElement, scrollingElement = element.ownerDocument.scrollingElement || html;
      return scrollingElement[upperSide];
    }
    return element[upperSide];
  }
  function includeScroll(rect, element) {
    var subtract = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : !1, scrollTop = getScroll(element, "top"), scrollLeft = getScroll(element, "left"), modifier = subtract ? -1 : 1;
    return rect.top += scrollTop * modifier, rect.bottom += scrollTop * modifier, rect.left += scrollLeft * modifier, rect.right += scrollLeft * modifier, rect;
  }
  function getBordersSize(styles, axis) {
    var sideA = axis === "x" ? "Left" : "Top", sideB = sideA === "Left" ? "Right" : "Bottom";
    return parseFloat(styles["border" + sideA + "Width"]) + parseFloat(styles["border" + sideB + "Width"]);
  }
  function getSize(axis, body, html, computedStyle) {
    return Math.max(body["offset" + axis], body["scroll" + axis], html["client" + axis], html["offset" + axis], html["scroll" + axis], isIE(10) ? parseInt(html["offset" + axis]) + parseInt(computedStyle["margin" + (axis === "Height" ? "Top" : "Left")]) + parseInt(computedStyle["margin" + (axis === "Height" ? "Bottom" : "Right")]) : 0);
  }
  function getWindowSizes(document2) {
    var body = document2.body, html = document2.documentElement, computedStyle = isIE(10) && getComputedStyle(html);
    return { height: getSize("Height", body, html, computedStyle), width: getSize("Width", body, html, computedStyle) };
  }
  function getClientRect(offsets) {
    return _extends({}, offsets, { right: offsets.left + offsets.width, bottom: offsets.top + offsets.height });
  }
  function getBoundingClientRect(element) {
    var rect = {};
    try {
      if (isIE(10)) {
        rect = element.getBoundingClientRect();
        var scrollTop = getScroll(element, "top"), scrollLeft = getScroll(element, "left");
        rect.top += scrollTop, rect.left += scrollLeft, rect.bottom += scrollTop, rect.right += scrollLeft;
      } else rect = element.getBoundingClientRect();
    } catch {
    }
    var result = { left: rect.left, top: rect.top, width: rect.right - rect.left, height: rect.bottom - rect.top }, sizes = element.nodeName === "HTML" ? getWindowSizes(element.ownerDocument) : {}, width = sizes.width || element.clientWidth || result.width, height = sizes.height || element.clientHeight || result.height, horizScrollbar = element.offsetWidth - width, vertScrollbar = element.offsetHeight - height;
    if (horizScrollbar || vertScrollbar) {
      var styles = getStyleComputedProperty(element);
      horizScrollbar -= getBordersSize(styles, "x"), vertScrollbar -= getBordersSize(styles, "y"), result.width -= horizScrollbar, result.height -= vertScrollbar;
    }
    return getClientRect(result);
  }
  function getOffsetRectRelativeToArbitraryNode(children, parent) {
    var fixedPosition = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : !1, isIE102 = isIE(10), isHTML = parent.nodeName === "HTML", childrenRect = getBoundingClientRect(children), parentRect = getBoundingClientRect(parent), scrollParent2 = getScrollParent(children), styles = getStyleComputedProperty(parent), borderTopWidth = parseFloat(styles.borderTopWidth), borderLeftWidth = parseFloat(styles.borderLeftWidth);
    fixedPosition && isHTML && (parentRect.top = Math.max(parentRect.top, 0), parentRect.left = Math.max(parentRect.left, 0));
    var offsets = getClientRect({ top: childrenRect.top - parentRect.top - borderTopWidth, left: childrenRect.left - parentRect.left - borderLeftWidth, width: childrenRect.width, height: childrenRect.height });
    if (offsets.marginTop = 0, offsets.marginLeft = 0, !isIE102 && isHTML) {
      var marginTop = parseFloat(styles.marginTop), marginLeft = parseFloat(styles.marginLeft);
      offsets.top -= borderTopWidth - marginTop, offsets.bottom -= borderTopWidth - marginTop, offsets.left -= borderLeftWidth - marginLeft, offsets.right -= borderLeftWidth - marginLeft, offsets.marginTop = marginTop, offsets.marginLeft = marginLeft;
    }
    return (isIE102 && !fixedPosition ? parent.contains(scrollParent2) : parent === scrollParent2 && scrollParent2.nodeName !== "BODY") && (offsets = includeScroll(offsets, parent)), offsets;
  }
  function getViewportOffsetRectRelativeToArtbitraryNode(element) {
    var excludeScroll = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : !1, html = element.ownerDocument.documentElement, relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html), width = Math.max(html.clientWidth, window.innerWidth || 0), height = Math.max(html.clientHeight, window.innerHeight || 0), scrollTop = excludeScroll ? 0 : getScroll(html), scrollLeft = excludeScroll ? 0 : getScroll(html, "left"), offset2 = { top: scrollTop - relativeOffset.top + relativeOffset.marginTop, left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft, width, height };
    return getClientRect(offset2);
  }
  function isFixed(element) {
    var nodeName = element.nodeName;
    if (nodeName === "BODY" || nodeName === "HTML") return !1;
    if (getStyleComputedProperty(element, "position") === "fixed") return !0;
    var parentNode = getParentNode(element);
    return parentNode ? isFixed(parentNode) : !1;
  }
  function getFixedPositionOffsetParent(element) {
    if (!element || !element.parentElement || isIE()) return document.documentElement;
    for (var el = element.parentElement; el && getStyleComputedProperty(el, "transform") === "none"; ) el = el.parentElement;
    return el || document.documentElement;
  }
  function getBoundaries(popper, reference, padding, boundariesElement) {
    var fixedPosition = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : !1, boundaries = { top: 0, left: 0 }, offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));
    if (boundariesElement === "viewport") boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);
    else {
      var boundariesNode = void 0;
      boundariesElement === "scrollParent" ? (boundariesNode = getScrollParent(getParentNode(reference)), boundariesNode.nodeName === "BODY" && (boundariesNode = popper.ownerDocument.documentElement)) : boundariesElement === "window" ? boundariesNode = popper.ownerDocument.documentElement : boundariesNode = boundariesElement;
      var offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent, fixedPosition);
      if (boundariesNode.nodeName === "HTML" && !isFixed(offsetParent)) {
        var _getWindowSizes = getWindowSizes(popper.ownerDocument), height = _getWindowSizes.height, width = _getWindowSizes.width;
        boundaries.top += offsets.top - offsets.marginTop, boundaries.bottom = height + offsets.top, boundaries.left += offsets.left - offsets.marginLeft, boundaries.right = width + offsets.left;
      } else boundaries = offsets;
    }
    padding = padding || 0;
    var isPaddingNumber = typeof padding == "number";
    return boundaries.left += isPaddingNumber ? padding : padding.left || 0, boundaries.top += isPaddingNumber ? padding : padding.top || 0, boundaries.right -= isPaddingNumber ? padding : padding.right || 0, boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0, boundaries;
  }
  function getArea(_ref) {
    var width = _ref.width, height = _ref.height;
    return width * height;
  }
  function computeAutoPlacement(placement, refRect, popper, reference, boundariesElement) {
    var padding = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : 0;
    if (placement.indexOf("auto") === -1) return placement;
    var boundaries = getBoundaries(popper, reference, padding, boundariesElement), rects = { top: { width: boundaries.width, height: refRect.top - boundaries.top }, right: { width: boundaries.right - refRect.right, height: boundaries.height }, bottom: { width: boundaries.width, height: boundaries.bottom - refRect.bottom }, left: { width: refRect.left - boundaries.left, height: boundaries.height } }, sortedAreas = Object.keys(rects).map(function(key) {
      return _extends({ key }, rects[key], { area: getArea(rects[key]) });
    }).sort(function(a2, b2) {
      return b2.area - a2.area;
    }), filteredAreas = sortedAreas.filter(function(_ref2) {
      var width = _ref2.width, height = _ref2.height;
      return width >= popper.clientWidth && height >= popper.clientHeight;
    }), computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].key : sortedAreas[0].key, variation = placement.split("-")[1];
    return computedPlacement + (variation ? "-" + variation : "");
  }
  function getReferenceOffsets(state, popper, reference) {
    var fixedPosition = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null, commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));
    return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);
  }
  function getOuterSizes(element) {
    var window2 = element.ownerDocument.defaultView, styles = window2.getComputedStyle(element), x2 = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0), y2 = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0), result = { width: element.offsetWidth + y2, height: element.offsetHeight + x2 };
    return result;
  }
  function getOppositePlacement(placement) {
    var hash = { left: "right", right: "left", bottom: "top", top: "bottom" };
    return placement.replace(/left|right|bottom|top/g, function(matched) {
      return hash[matched];
    });
  }
  function getPopperOffsets(popper, referenceOffsets, placement) {
    placement = placement.split("-")[0];
    var popperRect = getOuterSizes(popper), popperOffsets = { width: popperRect.width, height: popperRect.height }, isHoriz = ["right", "left"].indexOf(placement) !== -1, mainSide = isHoriz ? "top" : "left", secondarySide = isHoriz ? "left" : "top", measurement = isHoriz ? "height" : "width", secondaryMeasurement = isHoriz ? "width" : "height";
    return popperOffsets[mainSide] = referenceOffsets[mainSide] + referenceOffsets[measurement] / 2 - popperRect[measurement] / 2, placement === secondarySide ? popperOffsets[secondarySide] = referenceOffsets[secondarySide] - popperRect[secondaryMeasurement] : popperOffsets[secondarySide] = referenceOffsets[getOppositePlacement(secondarySide)], popperOffsets;
  }
  function find(arr, check) {
    return Array.prototype.find ? arr.find(check) : arr.filter(check)[0];
  }
  function findIndex(arr, prop, value) {
    if (Array.prototype.findIndex) return arr.findIndex(function(cur) {
      return cur[prop] === value;
    });
    var match = find(arr, function(obj) {
      return obj[prop] === value;
    });
    return arr.indexOf(match);
  }
  function runModifiers(modifiers2, data, ends) {
    var modifiersToRun = ends === void 0 ? modifiers2 : modifiers2.slice(0, findIndex(modifiers2, "name", ends));
    return modifiersToRun.forEach(function(modifier) {
      modifier.function && console.warn("`modifier.function` is deprecated, use `modifier.fn`!");
      var fn = modifier.function || modifier.fn;
      modifier.enabled && isFunction2(fn) && (data.offsets.popper = getClientRect(data.offsets.popper), data.offsets.reference = getClientRect(data.offsets.reference), data = fn(data, modifier));
    }), data;
  }
  function update() {
    if (!this.state.isDestroyed) {
      var data = { instance: this, styles: {}, arrowStyles: {}, attributes: {}, flipped: !1, offsets: {} };
      data.offsets.reference = getReferenceOffsets(this.state, this.popper, this.reference, this.options.positionFixed), data.placement = computeAutoPlacement(this.options.placement, data.offsets.reference, this.popper, this.reference, this.options.modifiers.flip.boundariesElement, this.options.modifiers.flip.padding), data.originalPlacement = data.placement, data.positionFixed = this.options.positionFixed, data.offsets.popper = getPopperOffsets(this.popper, data.offsets.reference, data.placement), data.offsets.popper.position = this.options.positionFixed ? "fixed" : "absolute", data = runModifiers(this.modifiers, data), this.state.isCreated ? this.options.onUpdate(data) : (this.state.isCreated = !0, this.options.onCreate(data));
    }
  }
  function isModifierEnabled(modifiers2, modifierName) {
    return modifiers2.some(function(_ref) {
      var name = _ref.name, enabled = _ref.enabled;
      return enabled && name === modifierName;
    });
  }
  function getSupportedPropertyName(property) {
    for (var prefixes = [!1, "ms", "Webkit", "Moz", "O"], upperProp = property.charAt(0).toUpperCase() + property.slice(1), i2 = 0; i2 < prefixes.length; i2++) {
      var prefix = prefixes[i2], toCheck = prefix ? "" + prefix + upperProp : property;
      if (typeof document.body.style[toCheck] < "u") return toCheck;
    }
    return null;
  }
  function destroy() {
    return this.state.isDestroyed = !0, isModifierEnabled(this.modifiers, "applyStyle") && (this.popper.removeAttribute("x-placement"), this.popper.style.position = "", this.popper.style.top = "", this.popper.style.left = "", this.popper.style.right = "", this.popper.style.bottom = "", this.popper.style.willChange = "", this.popper.style[getSupportedPropertyName("transform")] = ""), this.disableEventListeners(), this.options.removeOnDestroy && this.popper.parentNode.removeChild(this.popper), this;
  }
  function getWindow(element) {
    var ownerDocument = element.ownerDocument;
    return ownerDocument ? ownerDocument.defaultView : window;
  }
  function attachToScrollParents(scrollParent2, event, callback, scrollParents) {
    var isBody = scrollParent2.nodeName === "BODY", target = isBody ? scrollParent2.ownerDocument.defaultView : scrollParent2;
    target.addEventListener(event, callback, { passive: !0 }), isBody || attachToScrollParents(getScrollParent(target.parentNode), event, callback, scrollParents), scrollParents.push(target);
  }
  function setupEventListeners(reference, options, state, updateBound) {
    state.updateBound = updateBound, getWindow(reference).addEventListener("resize", state.updateBound, { passive: !0 });
    var scrollElement = getScrollParent(reference);
    return attachToScrollParents(scrollElement, "scroll", state.updateBound, state.scrollParents), state.scrollElement = scrollElement, state.eventsEnabled = !0, state;
  }
  function enableEventListeners() {
    this.state.eventsEnabled || (this.state = setupEventListeners(this.reference, this.options, this.state, this.scheduleUpdate));
  }
  function removeEventListeners(reference, state) {
    return getWindow(reference).removeEventListener("resize", state.updateBound), state.scrollParents.forEach(function(target) {
      target.removeEventListener("scroll", state.updateBound);
    }), state.updateBound = null, state.scrollParents = [], state.scrollElement = null, state.eventsEnabled = !1, state;
  }
  function disableEventListeners() {
    this.state.eventsEnabled && (cancelAnimationFrame(this.scheduleUpdate), this.state = removeEventListeners(this.reference, this.state));
  }
  function isNumeric(n2) {
    return n2 !== "" && !isNaN(parseFloat(n2)) && isFinite(n2);
  }
  function setStyles(element, styles) {
    Object.keys(styles).forEach(function(prop) {
      var unit = "";
      ["width", "height", "top", "right", "bottom", "left"].indexOf(prop) !== -1 && isNumeric(styles[prop]) && (unit = "px"), element.style[prop] = styles[prop] + unit;
    });
  }
  function setAttributes(element, attributes) {
    Object.keys(attributes).forEach(function(prop) {
      var value = attributes[prop];
      value !== !1 ? element.setAttribute(prop, attributes[prop]) : element.removeAttribute(prop);
    });
  }
  function applyStyle(data) {
    return setStyles(data.instance.popper, data.styles), setAttributes(data.instance.popper, data.attributes), data.arrowElement && Object.keys(data.arrowStyles).length && setStyles(data.arrowElement, data.arrowStyles), data;
  }
  function applyStyleOnLoad(reference, popper, options, modifierOptions, state) {
    var referenceOffsets = getReferenceOffsets(state, popper, reference, options.positionFixed), placement = computeAutoPlacement(options.placement, referenceOffsets, popper, reference, options.modifiers.flip.boundariesElement, options.modifiers.flip.padding);
    return popper.setAttribute("x-placement", placement), setStyles(popper, { position: options.positionFixed ? "fixed" : "absolute" }), options;
  }
  function getRoundedOffsets(data, shouldRound) {
    var _data$offsets = data.offsets, popper = _data$offsets.popper, reference = _data$offsets.reference, round = Math.round, floor = Math.floor, noRound = function(v2) {
      return v2;
    }, referenceWidth = round(reference.width), popperWidth = round(popper.width), isVertical = ["left", "right"].indexOf(data.placement) !== -1, isVariation = data.placement.indexOf("-") !== -1, sameWidthParity = referenceWidth % 2 === popperWidth % 2, bothOddWidth = referenceWidth % 2 === 1 && popperWidth % 2 === 1, horizontalToInteger = shouldRound ? isVertical || isVariation || sameWidthParity ? round : floor : noRound, verticalToInteger = shouldRound ? round : noRound;
    return { left: horizontalToInteger(bothOddWidth && !isVariation && shouldRound ? popper.left - 1 : popper.left), top: verticalToInteger(popper.top), bottom: verticalToInteger(popper.bottom), right: horizontalToInteger(popper.right) };
  }
  function computeStyle(data, options) {
    var x2 = options.x, y2 = options.y, popper = data.offsets.popper, legacyGpuAccelerationOption = find(data.instance.modifiers, function(modifier) {
      return modifier.name === "applyStyle";
    }).gpuAcceleration;
    legacyGpuAccelerationOption !== void 0 && console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");
    var gpuAcceleration = legacyGpuAccelerationOption !== void 0 ? legacyGpuAccelerationOption : options.gpuAcceleration, offsetParent = getOffsetParent(data.instance.popper), offsetParentRect = getBoundingClientRect(offsetParent), styles = { position: popper.position }, offsets = getRoundedOffsets(data, window.devicePixelRatio < 2 || !isFirefox), sideA = x2 === "bottom" ? "top" : "bottom", sideB = y2 === "right" ? "left" : "right", prefixedProperty = getSupportedPropertyName("transform"), left = void 0, top = void 0;
    if (sideA === "bottom" ? offsetParent.nodeName === "HTML" ? top = -offsetParent.clientHeight + offsets.bottom : top = -offsetParentRect.height + offsets.bottom : top = offsets.top, sideB === "right" ? offsetParent.nodeName === "HTML" ? left = -offsetParent.clientWidth + offsets.right : left = -offsetParentRect.width + offsets.right : left = offsets.left, gpuAcceleration && prefixedProperty) styles[prefixedProperty] = "translate3d(" + left + "px, " + top + "px, 0)", styles[sideA] = 0, styles[sideB] = 0, styles.willChange = "transform";
    else {
      var invertTop = sideA === "bottom" ? -1 : 1, invertLeft = sideB === "right" ? -1 : 1;
      styles[sideA] = top * invertTop, styles[sideB] = left * invertLeft, styles.willChange = sideA + ", " + sideB;
    }
    var attributes = { "x-placement": data.placement };
    return data.attributes = _extends({}, attributes, data.attributes), data.styles = _extends({}, styles, data.styles), data.arrowStyles = _extends({}, data.offsets.arrow, data.arrowStyles), data;
  }
  function isModifierRequired(modifiers2, requestingName, requestedName) {
    var requesting = find(modifiers2, function(_ref) {
      var name = _ref.name;
      return name === requestingName;
    }), isRequired = !!requesting && modifiers2.some(function(modifier) {
      return modifier.name === requestedName && modifier.enabled && modifier.order < requesting.order;
    });
    if (!isRequired) {
      var _requesting = "`" + requestingName + "`", requested = "`" + requestedName + "`";
      console.warn(requested + " modifier is required by " + _requesting + " modifier in order to work, be sure to include it before " + _requesting + "!");
    }
    return isRequired;
  }
  function arrow(data, options) {
    var _data$offsets$arrow;
    if (!isModifierRequired(data.instance.modifiers, "arrow", "keepTogether")) return data;
    var arrowElement = options.element;
    if (typeof arrowElement == "string") {
      if (arrowElement = data.instance.popper.querySelector(arrowElement), !arrowElement) return data;
    } else if (!data.instance.popper.contains(arrowElement)) return console.warn("WARNING: `arrow.element` must be child of its popper element!"), data;
    var placement = data.placement.split("-")[0], _data$offsets = data.offsets, popper = _data$offsets.popper, reference = _data$offsets.reference, isVertical = ["left", "right"].indexOf(placement) !== -1, len = isVertical ? "height" : "width", sideCapitalized = isVertical ? "Top" : "Left", side = sideCapitalized.toLowerCase(), altSide = isVertical ? "left" : "top", opSide = isVertical ? "bottom" : "right", arrowElementSize = getOuterSizes(arrowElement)[len];
    reference[opSide] - arrowElementSize < popper[side] && (data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowElementSize)), reference[side] + arrowElementSize > popper[opSide] && (data.offsets.popper[side] += reference[side] + arrowElementSize - popper[opSide]), data.offsets.popper = getClientRect(data.offsets.popper);
    var center = reference[side] + reference[len] / 2 - arrowElementSize / 2, css2 = getStyleComputedProperty(data.instance.popper), popperMarginSide = parseFloat(css2["margin" + sideCapitalized]), popperBorderSide = parseFloat(css2["border" + sideCapitalized + "Width"]), sideValue = center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;
    return sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0), data.arrowElement = arrowElement, data.offsets.arrow = (_data$offsets$arrow = {}, defineProperty(_data$offsets$arrow, side, Math.round(sideValue)), defineProperty(_data$offsets$arrow, altSide, ""), _data$offsets$arrow), data;
  }
  function getOppositeVariation(variation) {
    return variation === "end" ? "start" : variation === "start" ? "end" : variation;
  }
  function clockwise(placement) {
    var counter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : !1, index = validPlacements.indexOf(placement), arr = validPlacements.slice(index + 1).concat(validPlacements.slice(0, index));
    return counter ? arr.reverse() : arr;
  }
  function flip(data, options) {
    if (isModifierEnabled(data.instance.modifiers, "inner") || data.flipped && data.placement === data.originalPlacement) return data;
    var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, options.boundariesElement, data.positionFixed), placement = data.placement.split("-")[0], placementOpposite = getOppositePlacement(placement), variation = data.placement.split("-")[1] || "", flipOrder = [];
    switch (options.behavior) {
      case BEHAVIORS.FLIP:
        flipOrder = [placement, placementOpposite];
        break;
      case BEHAVIORS.CLOCKWISE:
        flipOrder = clockwise(placement);
        break;
      case BEHAVIORS.COUNTERCLOCKWISE:
        flipOrder = clockwise(placement, !0);
        break;
      default:
        flipOrder = options.behavior;
    }
    return flipOrder.forEach(function(step, index) {
      if (placement !== step || flipOrder.length === index + 1) return data;
      placement = data.placement.split("-")[0], placementOpposite = getOppositePlacement(placement);
      var popperOffsets = data.offsets.popper, refOffsets = data.offsets.reference, floor = Math.floor, overlapsRef = placement === "left" && floor(popperOffsets.right) > floor(refOffsets.left) || placement === "right" && floor(popperOffsets.left) < floor(refOffsets.right) || placement === "top" && floor(popperOffsets.bottom) > floor(refOffsets.top) || placement === "bottom" && floor(popperOffsets.top) < floor(refOffsets.bottom), overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left), overflowsRight = floor(popperOffsets.right) > floor(boundaries.right), overflowsTop = floor(popperOffsets.top) < floor(boundaries.top), overflowsBottom = floor(popperOffsets.bottom) > floor(boundaries.bottom), overflowsBoundaries = placement === "left" && overflowsLeft || placement === "right" && overflowsRight || placement === "top" && overflowsTop || placement === "bottom" && overflowsBottom, isVertical = ["top", "bottom"].indexOf(placement) !== -1, flippedVariationByRef = !!options.flipVariations && (isVertical && variation === "start" && overflowsLeft || isVertical && variation === "end" && overflowsRight || !isVertical && variation === "start" && overflowsTop || !isVertical && variation === "end" && overflowsBottom), flippedVariationByContent = !!options.flipVariationsByContent && (isVertical && variation === "start" && overflowsRight || isVertical && variation === "end" && overflowsLeft || !isVertical && variation === "start" && overflowsBottom || !isVertical && variation === "end" && overflowsTop), flippedVariation = flippedVariationByRef || flippedVariationByContent;
      (overlapsRef || overflowsBoundaries || flippedVariation) && (data.flipped = !0, (overlapsRef || overflowsBoundaries) && (placement = flipOrder[index + 1]), flippedVariation && (variation = getOppositeVariation(variation)), data.placement = placement + (variation ? "-" + variation : ""), data.offsets.popper = _extends({}, data.offsets.popper, getPopperOffsets(data.instance.popper, data.offsets.reference, data.placement)), data = runModifiers(data.instance.modifiers, data, "flip"));
    }), data;
  }
  function keepTogether(data) {
    var _data$offsets = data.offsets, popper = _data$offsets.popper, reference = _data$offsets.reference, placement = data.placement.split("-")[0], floor = Math.floor, isVertical = ["top", "bottom"].indexOf(placement) !== -1, side = isVertical ? "right" : "bottom", opSide = isVertical ? "left" : "top", measurement = isVertical ? "width" : "height";
    return popper[side] < floor(reference[opSide]) && (data.offsets.popper[opSide] = floor(reference[opSide]) - popper[measurement]), popper[opSide] > floor(reference[side]) && (data.offsets.popper[opSide] = floor(reference[side])), data;
  }
  function toValue(str, measurement, popperOffsets, referenceOffsets) {
    var split = str.match(/((?:\-|\+)?\d*\.?\d*)(.*)/), value = +split[1], unit = split[2];
    if (!value) return str;
    if (unit.indexOf("%") === 0) {
      var element = void 0;
      switch (unit) {
        case "%p":
          element = popperOffsets;
          break;
        case "%":
        case "%r":
        default:
          element = referenceOffsets;
      }
      var rect = getClientRect(element);
      return rect[measurement] / 100 * value;
    } else if (unit === "vh" || unit === "vw") {
      var size = void 0;
      return unit === "vh" ? size = Math.max(document.documentElement.clientHeight, window.innerHeight || 0) : size = Math.max(document.documentElement.clientWidth, window.innerWidth || 0), size / 100 * value;
    } else return value;
  }
  function parseOffset(offset2, popperOffsets, referenceOffsets, basePlacement) {
    var offsets = [0, 0], useHeight = ["right", "left"].indexOf(basePlacement) !== -1, fragments = offset2.split(/(\+|\-)/).map(function(frag) {
      return frag.trim();
    }), divider = fragments.indexOf(find(fragments, function(frag) {
      return frag.search(/,|\s/) !== -1;
    }));
    fragments[divider] && fragments[divider].indexOf(",") === -1 && console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");
    var splitRegex = /\s*,\s*|\s+/, ops = divider !== -1 ? [fragments.slice(0, divider).concat([fragments[divider].split(splitRegex)[0]]), [fragments[divider].split(splitRegex)[1]].concat(fragments.slice(divider + 1))] : [fragments];
    return ops = ops.map(function(op, index) {
      var measurement = (index === 1 ? !useHeight : useHeight) ? "height" : "width", mergeWithPrevious = !1;
      return op.reduce(function(a2, b2) {
        return a2[a2.length - 1] === "" && ["+", "-"].indexOf(b2) !== -1 ? (a2[a2.length - 1] = b2, mergeWithPrevious = !0, a2) : mergeWithPrevious ? (a2[a2.length - 1] += b2, mergeWithPrevious = !1, a2) : a2.concat(b2);
      }, []).map(function(str) {
        return toValue(str, measurement, popperOffsets, referenceOffsets);
      });
    }), ops.forEach(function(op, index) {
      op.forEach(function(frag, index2) {
        isNumeric(frag) && (offsets[index] += frag * (op[index2 - 1] === "-" ? -1 : 1));
      });
    }), offsets;
  }
  function offset(data, _ref) {
    var offset2 = _ref.offset, placement = data.placement, _data$offsets = data.offsets, popper = _data$offsets.popper, reference = _data$offsets.reference, basePlacement = placement.split("-")[0], offsets = void 0;
    return isNumeric(+offset2) ? offsets = [+offset2, 0] : offsets = parseOffset(offset2, popper, reference, basePlacement), basePlacement === "left" ? (popper.top += offsets[0], popper.left -= offsets[1]) : basePlacement === "right" ? (popper.top += offsets[0], popper.left += offsets[1]) : basePlacement === "top" ? (popper.left += offsets[0], popper.top -= offsets[1]) : basePlacement === "bottom" && (popper.left += offsets[0], popper.top += offsets[1]), data.popper = popper, data;
  }
  function preventOverflow(data, options) {
    var boundariesElement = options.boundariesElement || getOffsetParent(data.instance.popper);
    data.instance.reference === boundariesElement && (boundariesElement = getOffsetParent(boundariesElement));
    var transformProp = getSupportedPropertyName("transform"), popperStyles = data.instance.popper.style, top = popperStyles.top, left = popperStyles.left, transform = popperStyles[transformProp];
    popperStyles.top = "", popperStyles.left = "", popperStyles[transformProp] = "";
    var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, boundariesElement, data.positionFixed);
    popperStyles.top = top, popperStyles.left = left, popperStyles[transformProp] = transform, options.boundaries = boundaries;
    var order = options.priority, popper = data.offsets.popper, check = { primary: function(placement) {
      var value = popper[placement];
      return popper[placement] < boundaries[placement] && !options.escapeWithReference && (value = Math.max(popper[placement], boundaries[placement])), defineProperty({}, placement, value);
    }, secondary: function(placement) {
      var mainSide = placement === "right" ? "left" : "top", value = popper[mainSide];
      return popper[placement] > boundaries[placement] && !options.escapeWithReference && (value = Math.min(popper[mainSide], boundaries[placement] - (placement === "right" ? popper.width : popper.height))), defineProperty({}, mainSide, value);
    } };
    return order.forEach(function(placement) {
      var side = ["left", "top"].indexOf(placement) !== -1 ? "primary" : "secondary";
      popper = _extends({}, popper, check[side](placement));
    }), data.offsets.popper = popper, data;
  }
  function shift(data) {
    var placement = data.placement, basePlacement = placement.split("-")[0], shiftvariation = placement.split("-")[1];
    if (shiftvariation) {
      var _data$offsets = data.offsets, reference = _data$offsets.reference, popper = _data$offsets.popper, isVertical = ["bottom", "top"].indexOf(basePlacement) !== -1, side = isVertical ? "left" : "top", measurement = isVertical ? "width" : "height", shiftOffsets = { start: defineProperty({}, side, reference[side]), end: defineProperty({}, side, reference[side] + reference[measurement] - popper[measurement]) };
      data.offsets.popper = _extends({}, popper, shiftOffsets[shiftvariation]);
    }
    return data;
  }
  function hide(data) {
    if (!isModifierRequired(data.instance.modifiers, "hide", "preventOverflow")) return data;
    var refRect = data.offsets.reference, bound = find(data.instance.modifiers, function(modifier) {
      return modifier.name === "preventOverflow";
    }).boundaries;
    if (refRect.bottom < bound.top || refRect.left > bound.right || refRect.top > bound.bottom || refRect.right < bound.left) {
      if (data.hide === !0) return data;
      data.hide = !0, data.attributes["x-out-of-boundaries"] = "";
    } else {
      if (data.hide === !1) return data;
      data.hide = !1, data.attributes["x-out-of-boundaries"] = !1;
    }
    return data;
  }
  function inner(data) {
    var placement = data.placement, basePlacement = placement.split("-")[0], _data$offsets = data.offsets, popper = _data$offsets.popper, reference = _data$offsets.reference, isHoriz = ["left", "right"].indexOf(basePlacement) !== -1, subtractLength = ["top", "left"].indexOf(basePlacement) === -1;
    return popper[isHoriz ? "left" : "top"] = reference[basePlacement] - (subtractLength ? popper[isHoriz ? "width" : "height"] : 0), data.placement = getOppositePlacement(placement), data.offsets.popper = getClientRect(popper), data;
  }
  function getObjectType2(value) {
    var objectTypeName = Object.prototype.toString.call(value).slice(8, -1);
    if (/HTML\w+Element/.test(objectTypeName)) return "HTMLElement";
    if (isObjectType2(objectTypeName)) return objectTypeName;
  }
  function isObjectOfType2(type) {
    return function(value) {
      return getObjectType2(value) === type;
    };
  }
  function isObjectType2(name) {
    return objectTypes2.includes(name);
  }
  function isOfType3(type) {
    return function(value) {
      return typeof value === type;
    };
  }
  function isPrimitiveType2(name) {
    return primitiveTypes2.includes(name);
  }
  function is2(value) {
    if (value === null) return "null";
    switch (typeof value) {
      case "bigint":
        return "bigint";
      case "boolean":
        return "boolean";
      case "number":
        return "number";
      case "string":
        return "string";
      case "symbol":
        return "symbol";
      case "undefined":
        return "undefined";
    }
    if (is2.array(value)) return "Array";
    if (is2.plainFunction(value)) return "Function";
    var tagType = getObjectType2(value);
    return tagType || "Object";
  }
  function isOfType4(type) {
    return function(value) {
      return typeof value === type;
    };
  }
  function equalArray2(left, right) {
    var length = left.length;
    if (length !== right.length) return !1;
    for (var index = length; index-- !== 0; ) if (!equal2(left[index], right[index])) return !1;
    return !0;
  }
  function equalArrayBuffer2(left, right) {
    if (left.byteLength !== right.byteLength) return !1;
    for (var view1 = new DataView(left.buffer), view2 = new DataView(right.buffer), index = left.byteLength; index--; ) if (view1.getUint8(index) !== view2.getUint8(index)) return !1;
    return !0;
  }
  function equalMap2(left, right) {
    var e_1, _a, e_2, _b;
    if (left.size !== right.size) return !1;
    try {
      for (var _c = __values(left.entries()), _d = _c.next(); !_d.done; _d = _c.next()) {
        var index = _d.value;
        if (!right.has(index[0])) return !1;
      }
    } catch (e_1_1) {
      e_1 = { error: e_1_1 };
    } finally {
      try {
        _d && !_d.done && (_a = _c.return) && _a.call(_c);
      } finally {
        if (e_1) throw e_1.error;
      }
    }
    try {
      for (var _e = __values(left.entries()), _f = _e.next(); !_f.done; _f = _e.next()) {
        var index = _f.value;
        if (!equal2(index[1], right.get(index[0]))) return !1;
      }
    } catch (e_2_1) {
      e_2 = { error: e_2_1 };
    } finally {
      try {
        _f && !_f.done && (_b = _e.return) && _b.call(_e);
      } finally {
        if (e_2) throw e_2.error;
      }
    }
    return !0;
  }
  function equalSet2(left, right) {
    var e_3, _a;
    if (left.size !== right.size) return !1;
    try {
      for (var _b = __values(left.entries()), _c = _b.next(); !_c.done; _c = _b.next()) {
        var index = _c.value;
        if (!right.has(index[0])) return !1;
      }
    } catch (e_3_1) {
      e_3 = { error: e_3_1 };
    } finally {
      try {
        _c && !_c.done && (_a = _b.return) && _a.call(_b);
      } finally {
        if (e_3) throw e_3.error;
      }
    }
    return !0;
  }
  function equal2(left, right) {
    if (left === right) return !0;
    if (left && isObject2(left) && right && isObject2(right)) {
      if (left.constructor !== right.constructor) return !1;
      if (Array.isArray(left) && Array.isArray(right)) return equalArray2(left, right);
      if (left instanceof Map && right instanceof Map) return equalMap2(left, right);
      if (left instanceof Set && right instanceof Set) return equalSet2(left, right);
      if (ArrayBuffer.isView(left) && ArrayBuffer.isView(right)) return equalArrayBuffer2(left, right);
      if (isRegex2(left) && isRegex2(right)) return left.source === right.source && left.flags === right.flags;
      if (left.valueOf !== Object.prototype.valueOf) return left.valueOf() === right.valueOf();
      if (left.toString !== Object.prototype.toString) return left.toString() === right.toString();
      var leftKeys = Object.keys(left), rightKeys = Object.keys(right);
      if (leftKeys.length !== rightKeys.length) return !1;
      for (var index = leftKeys.length; index-- !== 0; ) if (!Object.prototype.hasOwnProperty.call(right, leftKeys[index])) return !1;
      for (var index = leftKeys.length; index-- !== 0; ) {
        var key = leftKeys[index];
        if (!(key === "_owner" && left.$$typeof) && !equal2(left[key], right[key])) return !1;
      }
      return !0;
    }
    return Number.isNaN(left) && Number.isNaN(right) ? !0 : left === right;
  }
  function getObjectType3(value) {
    var objectTypeName = Object.prototype.toString.call(value).slice(8, -1);
    if (/HTML\w+Element/.test(objectTypeName)) return "HTMLElement";
    if (isObjectType3(objectTypeName)) return objectTypeName;
  }
  function isObjectOfType3(type) {
    return function(value) {
      return getObjectType3(value) === type;
    };
  }
  function isObjectType3(name) {
    return objectTypes3.includes(name);
  }
  function isOfType5(type) {
    return function(value) {
      return typeof value === type;
    };
  }
  function isPrimitiveType3(name) {
    return primitiveTypes3.includes(name);
  }
  function is3(value) {
    if (value === null) return "null";
    switch (typeof value) {
      case "bigint":
        return "bigint";
      case "boolean":
        return "boolean";
      case "number":
        return "number";
      case "string":
        return "string";
      case "symbol":
        return "symbol";
      case "undefined":
        return "undefined";
    }
    if (is3.array(value)) return "Array";
    if (is3.plainFunction(value)) return "Function";
    var tagType = getObjectType3(value);
    return tagType || "Object";
  }
  function canHaveLength2() {
    for (var arguments_ = [], _i = 0; _i < arguments.length; _i++) arguments_[_i] = arguments[_i];
    return arguments_.every(function(d2) {
      return esm_default2.string(d2) || esm_default2.array(d2) || esm_default2.plainObject(d2);
    });
  }
  function checkEquality2(left, right, value) {
    return isSameType2(left, right) ? [left, right].every(esm_default2.array) ? !left.some(hasValue2(value)) && right.some(hasValue2(value)) : [left, right].every(esm_default2.plainObject) ? !Object.entries(left).some(hasEntry2(value)) && Object.entries(right).some(hasEntry2(value)) : right === value : !1;
  }
  function compareNumbers2(previousData, data, options) {
    var actual = options.actual, key = options.key, previous = options.previous, type = options.type, left = nested2(previousData, key), right = nested2(data, key), changed = [left, right].every(esm_default2.number) && (type === "increased" ? left < right : left > right);
    return esm_default2.undefined(actual) || (changed = changed && right === actual), esm_default2.undefined(previous) || (changed = changed && left === previous), changed;
  }
  function compareValues2(previousData, data, options) {
    var key = options.key, type = options.type, value = options.value, left = nested2(previousData, key), right = nested2(data, key), primary = type === "added" ? left : right, secondary = type === "added" ? right : left;
    if (!esm_default2.nullOrUndefined(value)) {
      if (esm_default2.defined(primary)) {
        if (esm_default2.array(primary) || esm_default2.plainObject(primary)) return checkEquality2(primary, secondary, value);
      } else return equal2(secondary, value);
      return !1;
    }
    return [left, right].every(esm_default2.array) ? !secondary.every(isEqualPredicate2(primary)) : [left, right].every(esm_default2.plainObject) ? hasExtraKeys2(Object.keys(primary), Object.keys(secondary)) : ![left, right].every(function(d2) {
      return esm_default2.primitive(d2) && esm_default2.defined(d2);
    }) && (type === "added" ? !esm_default2.defined(left) && esm_default2.defined(right) : esm_default2.defined(left) && !esm_default2.defined(right));
  }
  function getIterables2(previousData, data, _a) {
    var _b = _a === void 0 ? {} : _a, key = _b.key, left = nested2(previousData, key), right = nested2(data, key);
    if (!isSameType2(left, right)) throw new TypeError("Inputs have different types");
    if (!canHaveLength2(left, right)) throw new TypeError("Inputs don't have length");
    return [left, right].every(esm_default2.plainObject) && (left = Object.keys(left), right = Object.keys(right)), [left, right];
  }
  function hasEntry2(input) {
    return function(_a) {
      var key = _a[0], value = _a[1];
      return esm_default2.array(input) ? equal2(input, value) || input.some(function(d2) {
        return equal2(d2, value) || esm_default2.array(value) && isEqualPredicate2(value)(d2);
      }) : esm_default2.plainObject(input) && input[key] ? !!input[key] && equal2(input[key], value) : equal2(input, value);
    };
  }
  function hasExtraKeys2(left, right) {
    return right.some(function(d2) {
      return !left.includes(d2);
    });
  }
  function hasValue2(input) {
    return function(value) {
      return esm_default2.array(input) ? input.some(function(d2) {
        return equal2(d2, value) || esm_default2.array(value) && isEqualPredicate2(value)(d2);
      }) : equal2(input, value);
    };
  }
  function includesOrEqualsTo2(previousValue, value) {
    return esm_default2.array(previousValue) ? previousValue.some(function(d2) {
      return equal2(d2, value);
    }) : equal2(previousValue, value);
  }
  function isEqualPredicate2(data) {
    return function(value) {
      return data.some(function(d2) {
        return equal2(d2, value);
      });
    };
  }
  function isSameType2() {
    for (var arguments_ = [], _i = 0; _i < arguments.length; _i++) arguments_[_i] = arguments[_i];
    return arguments_.every(esm_default2.array) || arguments_.every(esm_default2.number) || arguments_.every(esm_default2.plainObject) || arguments_.every(esm_default2.string);
  }
  function nested2(data, property) {
    if (esm_default2.plainObject(data) || esm_default2.array(data)) {
      if (esm_default2.string(property)) {
        var props = property.split(".");
        return props.reduce(function(acc, d2) {
          return acc && acc[d2];
        }, data);
      }
      return esm_default2.number(property) ? data[property] : data;
    }
    return data;
  }
  function treeChanges2(previousData, data) {
    if ([previousData, data].some(esm_default2.nullOrUndefined)) throw new Error("Missing required parameters");
    if (![previousData, data].every(function(d2) {
      return esm_default2.plainObject(d2) || esm_default2.array(d2);
    })) throw new Error("Expected plain objects or array");
    var added = function(key, value) {
      try {
        return compareValues2(previousData, data, { key, type: "added", value });
      } catch {
        return !1;
      }
    }, changed = function(key, actual, previous) {
      try {
        var left = nested2(previousData, key), right = nested2(data, key), hasActual = esm_default2.defined(actual), hasPrevious = esm_default2.defined(previous);
        if (hasActual || hasPrevious) {
          var leftComparator = hasPrevious ? includesOrEqualsTo2(previous, left) : !includesOrEqualsTo2(actual, left), rightComparator = includesOrEqualsTo2(actual, right);
          return leftComparator && rightComparator;
        }
        return [left, right].every(esm_default2.array) || [left, right].every(esm_default2.plainObject) ? !equal2(left, right) : left !== right;
      } catch {
        return !1;
      }
    }, changedFrom = function(key, previous, actual) {
      if (!esm_default2.defined(key)) return !1;
      try {
        var left = nested2(previousData, key), right = nested2(data, key), hasActual = esm_default2.defined(actual);
        return includesOrEqualsTo2(previous, left) && (hasActual ? includesOrEqualsTo2(actual, right) : !hasActual);
      } catch {
        return !1;
      }
    }, changedTo = function(key, actual) {
      return esm_default2.defined(key) ? (console.warn("`changedTo` is deprecated! Replace it with `change`"), changed(key, actual)) : !1;
    }, decreased = function(key, actual, previous) {
      if (!esm_default2.defined(key)) return !1;
      try {
        return compareNumbers2(previousData, data, { key, actual, previous, type: "decreased" });
      } catch {
        return !1;
      }
    }, emptied = function(key) {
      try {
        var _a = getIterables2(previousData, data, { key }), left = _a[0], right = _a[1];
        return !!left.length && !right.length;
      } catch {
        return !1;
      }
    }, filled = function(key) {
      try {
        var _a = getIterables2(previousData, data, { key }), left = _a[0], right = _a[1];
        return !left.length && !!right.length;
      } catch {
        return !1;
      }
    }, increased = function(key, actual, previous) {
      if (!esm_default2.defined(key)) return !1;
      try {
        return compareNumbers2(previousData, data, { key, actual, previous, type: "increased" });
      } catch {
        return !1;
      }
    }, removed = function(key, value) {
      try {
        return compareValues2(previousData, data, { key, type: "removed", value });
      } catch {
        return !1;
      }
    };
    return { added, changed, changedFrom, changedTo, decreased, emptied, filled, increased, removed };
  }
  function ownKeys(e2, r2) {
    var t2 = Object.keys(e2);
    if (Object.getOwnPropertySymbols) {
      var o22 = Object.getOwnPropertySymbols(e2);
      r2 && (o22 = o22.filter(function(r3) {
        return Object.getOwnPropertyDescriptor(e2, r3).enumerable;
      })), t2.push.apply(t2, o22);
    }
    return t2;
  }
  function _objectSpread2(e2) {
    for (var r2 = 1; r2 < arguments.length; r2++) {
      var t2 = arguments[r2] != null ? arguments[r2] : {};
      r2 % 2 ? ownKeys(Object(t2), !0).forEach(function(r3) {
        _defineProperty(e2, r3, t2[r3]);
      }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e2, Object.getOwnPropertyDescriptors(t2)) : ownKeys(Object(t2)).forEach(function(r3) {
        Object.defineProperty(e2, r3, Object.getOwnPropertyDescriptor(t2, r3));
      });
    }
    return e2;
  }
  function _classCallCheck(instance, Constructor) {
    if (!(instance instanceof Constructor)) throw new TypeError("Cannot call a class as a function");
  }
  function _defineProperties(target, props) {
    for (var i2 = 0; i2 < props.length; i2++) {
      var descriptor = props[i2];
      descriptor.enumerable = descriptor.enumerable || !1, descriptor.configurable = !0, "value" in descriptor && (descriptor.writable = !0), Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);
    }
  }
  function _createClass(Constructor, protoProps, staticProps) {
    return protoProps && _defineProperties(Constructor.prototype, protoProps), staticProps && _defineProperties(Constructor, staticProps), Object.defineProperty(Constructor, "prototype", { writable: !1 }), Constructor;
  }
  function _defineProperty(obj, key, value) {
    return key = _toPropertyKey(key), key in obj ? Object.defineProperty(obj, key, { value, enumerable: !0, configurable: !0, writable: !0 }) : obj[key] = value, obj;
  }
  function _inherits(subClass, superClass) {
    if (typeof superClass != "function" && superClass !== null) throw new TypeError("Super expression must either be null or a function");
    subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: !0, configurable: !0 } }), Object.defineProperty(subClass, "prototype", { writable: !1 }), superClass && _setPrototypeOf(subClass, superClass);
  }
  function _getPrototypeOf(o22) {
    return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(o3) {
      return o3.__proto__ || Object.getPrototypeOf(o3);
    }, _getPrototypeOf(o22);
  }
  function _setPrototypeOf(o22, p2) {
    return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(o3, p3) {
      return o3.__proto__ = p3, o3;
    }, _setPrototypeOf(o22, p2);
  }
  function _isNativeReflectConstruct() {
    if (typeof Reflect > "u" || !Reflect.construct || Reflect.construct.sham) return !1;
    if (typeof Proxy == "function") return !0;
    try {
      return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
      })), !0;
    } catch {
      return !1;
    }
  }
  function _objectWithoutPropertiesLoose(source, excluded) {
    if (source == null) return {};
    var target = {}, sourceKeys = Object.keys(source), key, i2;
    for (i2 = 0; i2 < sourceKeys.length; i2++) key = sourceKeys[i2], !(excluded.indexOf(key) >= 0) && (target[key] = source[key]);
    return target;
  }
  function _objectWithoutProperties(source, excluded) {
    if (source == null) return {};
    var target = _objectWithoutPropertiesLoose(source, excluded), key, i2;
    if (Object.getOwnPropertySymbols) {
      var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
      for (i2 = 0; i2 < sourceSymbolKeys.length; i2++) key = sourceSymbolKeys[i2], !(excluded.indexOf(key) >= 0) && Object.prototype.propertyIsEnumerable.call(source, key) && (target[key] = source[key]);
    }
    return target;
  }
  function _assertThisInitialized(self) {
    if (self === void 0) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    return self;
  }
  function _possibleConstructorReturn(self, call) {
    if (call && (typeof call == "object" || typeof call == "function")) return call;
    if (call !== void 0) throw new TypeError("Derived constructors may only return object or undefined");
    return _assertThisInitialized(self);
  }
  function _createSuper(Derived) {
    var hasNativeReflectConstruct = _isNativeReflectConstruct();
    return function() {
      var Super = _getPrototypeOf(Derived), result;
      if (hasNativeReflectConstruct) {
        var NewTarget = _getPrototypeOf(this).constructor;
        result = Reflect.construct(Super, arguments, NewTarget);
      } else result = Super.apply(this, arguments);
      return _possibleConstructorReturn(this, result);
    };
  }
  function _toPrimitive(input, hint) {
    if (typeof input != "object" || input === null) return input;
    var prim = input[Symbol.toPrimitive];
    if (prim !== void 0) {
      var res = prim.call(input, hint || "default");
      if (typeof res != "object") return res;
      throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return (hint === "string" ? String : Number)(input);
  }
  function _toPropertyKey(arg) {
    var key = _toPrimitive(arg, "string");
    return typeof key == "symbol" ? key : String(key);
  }
  function propIsRequired(condition, props, propName, componentName) {
    return typeof condition == "boolean" ? condition : typeof condition == "function" ? condition(props, propName, componentName) : condition ? !!condition : !1;
  }
  function propExists(props, propName) {
    return Object.hasOwnProperty.call(props, propName);
  }
  function missingPropError(props, propName, componentName, message) {
    return message ? new Error(message) : new Error("Required ".concat(props[propName], " `").concat(propName, "` was not specified in `").concat(componentName, "`."));
  }
  function guardAgainstInvalidArgTypes(typeValidator, message) {
    if (typeof typeValidator != "function") throw new TypeError(VALIDATOR_ARG_ERROR_MESSAGE);
    if (message && typeof message != "string") throw new TypeError(MESSAGE_ARG_ERROR_MESSAGE);
  }
  function isRequiredIf(typeValidator, condition, message) {
    return guardAgainstInvalidArgTypes(typeValidator, message), function(props, propName, componentName) {
      for (var _len = arguments.length, rest = new Array(_len > 3 ? _len - 3 : 0), _key = 3; _key < _len; _key++) rest[_key - 3] = arguments[_key];
      return propIsRequired(condition, props, propName, componentName) ? propExists(props, propName) ? typeValidator.apply(void 0, [props, propName, componentName].concat(rest)) : missingPropError(props, propName, componentName, message) : typeValidator.apply(void 0, [props, propName, componentName].concat(rest));
    };
  }
  function canUseDOM() {
    return !!(typeof window < "u" && window.document && window.document.createElement);
  }
  function isMobile() {
    return "ontouchstart" in window && /Mobi/.test(navigator.userAgent);
  }
  function log(_ref) {
    var title = _ref.title, data = _ref.data, _ref$warn = _ref.warn, warn = _ref$warn === void 0 ? !1 : _ref$warn, _ref$debug = _ref.debug, debug = _ref$debug === void 0 ? !1 : _ref$debug, logFn = warn ? console.warn || console.error : console.log;
    debug && title && data && (console.groupCollapsed("%creact-floater: ".concat(title), "color: #9b00ff; font-weight: bold; font-size: 12px;"), Array.isArray(data) ? data.forEach(function(d2) {
      esm_default.plainObject(d2) && d2.key ? logFn.apply(console, [d2.key, d2.value]) : logFn.apply(console, [d2]);
    }) : logFn.apply(console, [data]), console.groupEnd());
  }
  function on(element, event, cb) {
    var capture = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : !1;
    element.addEventListener(event, cb, capture);
  }
  function off(element, event, cb) {
    var capture = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : !1;
    element.removeEventListener(event, cb, capture);
  }
  function once(element, event, cb) {
    var capture = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : !1, _nextCB;
    _nextCB = function(e2) {
      cb(e2), off(element, event, _nextCB);
    }, on(element, event, _nextCB, capture);
  }
  function noop() {
  }
  function FloaterCloseBtn(_ref) {
    var handleClick = _ref.handleClick, styles = _ref.styles, color2 = styles.color, height = styles.height, width = styles.width, style = _objectWithoutProperties(styles, _excluded$1);
    return react_default.createElement("button", { "aria-label": "close", onClick: handleClick, style, type: "button" }, react_default.createElement("svg", { width: "".concat(width, "px"), height: "".concat(height, "px"), viewBox: "0 0 18 18", version: "1.1", xmlns: "http://www.w3.org/2000/svg", preserveAspectRatio: "xMidYMid" }, react_default.createElement("g", null, react_default.createElement("path", { d: "M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z", fill: color2 }))));
  }
  function FloaterContainer(_ref) {
    var content = _ref.content, footer = _ref.footer, handleClick = _ref.handleClick, open = _ref.open, positionWrapper = _ref.positionWrapper, showCloseButton = _ref.showCloseButton, title = _ref.title, styles = _ref.styles, output = { content: react_default.isValidElement(content) ? content : react_default.createElement("div", { className: "__floater__content", style: styles.content }, content) };
    return title && (output.title = react_default.isValidElement(title) ? title : react_default.createElement("div", { className: "__floater__title", style: styles.title }, title)), footer && (output.footer = react_default.isValidElement(footer) ? footer : react_default.createElement("div", { className: "__floater__footer", style: styles.footer }, footer)), (showCloseButton || positionWrapper) && !esm_default.boolean(open) && (output.close = react_default.createElement(FloaterCloseBtn, { styles: styles.close, handleClick })), react_default.createElement("div", { className: "__floater__container", style: styles.container }, output.close, output.title, output.content, output.footer);
  }
  function getStyles(styles) {
    var options = (0, import_deepmerge.default)(defaultOptions, styles.options || {});
    return { wrapper: { cursor: "help", display: "inline-flex", flexDirection: "column", zIndex: options.zIndex }, wrapperPosition: { left: -1e3, position: "absolute", top: -1e3, visibility: "hidden" }, floater: { display: "inline-block", filter: "drop-shadow(0 0 3px rgba(0, 0, 0, 0.3))", maxWidth: 300, opacity: 0, position: "relative", transition: "opacity 0.3s", visibility: "hidden", zIndex: options.zIndex }, floaterOpening: { opacity: 1, visibility: "visible" }, floaterWithAnimation: { opacity: 1, transition: "opacity 0.3s, transform 0.2s", visibility: "visible" }, floaterWithComponent: { maxWidth: "100%" }, floaterClosing: { opacity: 0, visibility: "visible" }, floaterCentered: { left: "50%", position: "fixed", top: "50%", transform: "translate(-50%, -50%)" }, container: { backgroundColor: "#fff", color: "#666", minHeight: 60, minWidth: 200, padding: 20, position: "relative", zIndex: 10 }, title: { borderBottom: "1px solid #555", color: "#555", fontSize: 18, marginBottom: 5, paddingBottom: 6, paddingRight: 18 }, content: { fontSize: 15 }, close: { backgroundColor: "transparent", border: 0, borderRadius: 0, color: "#555", fontSize: 0, height: 15, outline: "none", padding: 10, position: "absolute", right: 0, top: 0, width: 15, WebkitAppearance: "none" }, footer: { borderTop: "1px solid #ccc", fontSize: 13, marginTop: 10, paddingTop: 5 }, arrow: { color: "#fff", display: "inline-flex", length: 16, margin: 8, position: "absolute", spread: 32 }, options };
  }
  function canUseDOM2() {
    var _a;
    return !!(typeof window < "u" && (_a = window.document) != null && _a.createElement);
  }
  function getClientRect2(element) {
    return element ? element.getBoundingClientRect() : null;
  }
  function getDocumentHeight(median = !1) {
    let { body, documentElement } = document;
    if (!body || !documentElement) return 0;
    if (median) {
      let heights = [body.scrollHeight, body.offsetHeight, documentElement.clientHeight, documentElement.scrollHeight, documentElement.offsetHeight].sort((a2, b2) => a2 - b2), middle = Math.floor(heights.length / 2);
      return heights.length % 2 === 0 ? (heights[middle - 1] + heights[middle]) / 2 : heights[middle];
    }
    return Math.max(body.scrollHeight, body.offsetHeight, documentElement.clientHeight, documentElement.scrollHeight, documentElement.offsetHeight);
  }
  function getElement(element) {
    if (typeof element == "string") try {
      return document.querySelector(element);
    } catch (error) {
      return console.error(error), null;
    }
    return element;
  }
  function getStyleComputedProperty2(el) {
    return !el || el.nodeType !== 1 ? null : getComputedStyle(el);
  }
  function getScrollParent2(element, skipFix, forListener) {
    if (!element) return scrollDocument();
    let parent = (0, import_scrollparent.default)(element);
    if (parent) {
      if (parent.isSameNode(scrollDocument())) return forListener ? document : scrollDocument();
      if (!(parent.scrollHeight > parent.offsetHeight) && !skipFix) return parent.style.overflow = "initial", scrollDocument();
    }
    return parent;
  }
  function hasCustomScrollParent(element, skipFix) {
    if (!element) return !1;
    let parent = getScrollParent2(element, skipFix);
    return parent ? !parent.isSameNode(scrollDocument()) : !1;
  }
  function hasCustomOffsetParent(element) {
    return element.offsetParent !== document.body;
  }
  function hasPosition(el, type = "fixed") {
    if (!el || !(el instanceof HTMLElement)) return !1;
    let { nodeName } = el, styles = getStyleComputedProperty2(el);
    return nodeName === "BODY" || nodeName === "HTML" ? !1 : styles && styles.position === type ? !0 : el.parentNode ? hasPosition(el.parentNode, type) : !1;
  }
  function isElementVisible(element) {
    var _a;
    if (!element) return !1;
    let parentElement = element;
    for (; parentElement && parentElement !== document.body; ) {
      if (parentElement instanceof HTMLElement) {
        let { display, visibility } = getComputedStyle(parentElement);
        if (display === "none" || visibility === "hidden") return !1;
      }
      parentElement = (_a = parentElement.parentElement) != null ? _a : null;
    }
    return !0;
  }
  function getElementPosition(element, offset2, skipFix) {
    var _a, _b, _c;
    let elementRect = getClientRect2(element), parent = getScrollParent2(element, skipFix), hasScrollParent = hasCustomScrollParent(element, skipFix), isFixedTarget = hasPosition(element), parentTop = 0, top = (_a = elementRect?.top) != null ? _a : 0;
    if (hasScrollParent && isFixedTarget) {
      let offsetTop = (_b = element?.offsetTop) != null ? _b : 0, parentScrollTop = (_c = parent?.scrollTop) != null ? _c : 0;
      top = offsetTop - parentScrollTop;
    } else parent instanceof HTMLElement && (parentTop = parent.scrollTop, !hasScrollParent && !hasPosition(element) && (top += parentTop), parent.isSameNode(scrollDocument()) || (top += scrollDocument().scrollTop));
    return Math.floor(top - offset2);
  }
  function getScrollTo(element, offset2, skipFix) {
    var _a;
    if (!element) return 0;
    let { offsetTop = 0, scrollTop = 0 } = (_a = (0, import_scrollparent.default)(element)) != null ? _a : {}, top = element.getBoundingClientRect().top + scrollTop;
    offsetTop && (hasCustomScrollParent(element, skipFix) || hasCustomOffsetParent(element)) && (top -= offsetTop);
    let output = Math.floor(top - offset2);
    return output < 0 ? 0 : output;
  }
  function scrollDocument() {
    var _a;
    return (_a = document.scrollingElement) != null ? _a : document.documentElement;
  }
  function scrollTo(value, options) {
    let { duration, element } = options;
    return new Promise((resolve, reject) => {
      let { scrollTop } = element, limit = value > scrollTop ? value - scrollTop : scrollTop - value;
      import_scroll.default.top(element, value, { duration: limit < 100 ? 50 : duration }, (error) => error && error.message !== "Element already at target scroll position" ? reject(error) : resolve());
    });
  }
  function getBrowser(userAgent = navigator.userAgent) {
    let browser = userAgent;
    return typeof window > "u" ? browser = "node" : document.documentMode ? browser = "ie" : /Edge/.test(userAgent) ? browser = "edge" : window.opera || userAgent.includes(" OPR/") ? browser = "opera" : typeof window.InstallTrigger < "u" ? browser = "firefox" : window.chrome ? browser = "chrome" : /(Version\/([\d._]+).*Safari|CriOS|FxiOS| Mobile\/)/.test(userAgent) && (browser = "safari"), browser;
  }
  function getObjectType4(value) {
    return Object.prototype.toString.call(value).slice(8, -1).toLowerCase();
  }
  function getReactNodeText(input, options = {}) {
    let { defaultValue, step, steps } = options, text = (0, import_react_innertext.default)(input);
    if (text) (text.includes("{step}") || text.includes("{steps}")) && step && steps && (text = text.replace("{step}", step.toString()).replace("{steps}", steps.toString()));
    else if (isValidElement(input) && !Object.values(input.props).length && getObjectType4(input.type) === "function") {
      let component = input.type({});
      text = getReactNodeText(component, options);
    } else text = (0, import_react_innertext.default)(defaultValue);
    return text;
  }
  function hasValidKeys(object, keys) {
    return !src_default.plainObject(object) || !src_default.array(keys) ? !1 : Object.keys(object).every((d2) => keys.includes(d2));
  }
  function hexToRGB(hex) {
    let shorthandRegex = /^#?([\da-f])([\da-f])([\da-f])$/i, properHex = hex.replace(shorthandRegex, (_m, r2, g2, b2) => r2 + r2 + g2 + g2 + b2 + b2), result = /^#?([\da-f]{2})([\da-f]{2})([\da-f]{2})$/i.exec(properHex);
    return result ? [parseInt(result[1], 16), parseInt(result[2], 16), parseInt(result[3], 16)] : [];
  }
  function hideBeacon(step) {
    return step.disableBeacon || step.placement === "center";
  }
  function isLegacy() {
    return !["chrome", "safari", "firefox", "opera"].includes(getBrowser());
  }
  function log2({ data, debug = !1, title, warn = !1 }) {
    let logFn = warn ? console.warn || console.error : console.log;
    debug && (title && data ? (console.groupCollapsed(`%creact-joyride: ${title}`, "color: #ff0044; font-weight: bold; font-size: 12px;"), Array.isArray(data) ? data.forEach((d2) => {
      src_default.plainObject(d2) && d2.key ? logFn.apply(console, [d2.key, d2.value]) : logFn.apply(console, [d2]);
    }) : logFn.apply(console, [data]), console.groupEnd()) : console.error("Missing title or data props"));
  }
  function objectKeys(input) {
    return Object.keys(input);
  }
  function omit(input, ...filter) {
    if (!src_default.plainObject(input)) throw new TypeError("Expected an object");
    let output = {};
    for (let key in input) ({}).hasOwnProperty.call(input, key) && (filter.includes(key) || (output[key] = input[key]));
    return output;
  }
  function pick(input, ...filter) {
    if (!src_default.plainObject(input)) throw new TypeError("Expected an object");
    if (!filter.length) return input;
    let output = {};
    for (let key in input) ({}).hasOwnProperty.call(input, key) && filter.includes(key) && (output[key] = input[key]);
    return output;
  }
  function replaceLocaleContent(input, step, steps) {
    let replacer = (text) => text.replace("{step}", String(step)).replace("{steps}", String(steps));
    if (getObjectType4(input) === "string") return replacer(input);
    if (!isValidElement(input)) return input;
    let { children } = input.props;
    if (getObjectType4(children) === "string" && children.includes("{step}")) return cloneElement(input, { children: replacer(children) });
    if (Array.isArray(children)) return cloneElement(input, { children: children.map((child) => typeof child == "string" ? replacer(child) : replaceLocaleContent(child, step, steps)) });
    if (getObjectType4(input.type) === "function" && !Object.values(input.props).length) {
      let component = input.type({});
      return replaceLocaleContent(component, step, steps);
    }
    return input;
  }
  function shouldScroll(options) {
    let { isFirstStep, lifecycle, previousLifecycle, scrollToFirstStep, step, target } = options;
    return !step.disableScrolling && (!isFirstStep || scrollToFirstStep || lifecycle === LIFECYCLE.TOOLTIP) && step.placement !== "center" && (!step.isFixed || !hasPosition(target)) && previousLifecycle !== lifecycle && [LIFECYCLE.BEACON, LIFECYCLE.TOOLTIP].includes(lifecycle);
  }
  function getStyles2(props, step) {
    var _a, _b, _c, _d, _e;
    let { floaterProps, styles } = props, mergedFloaterProps = (0, import_deepmerge3.default)((_a = step.floaterProps) != null ? _a : {}, floaterProps ?? {}), mergedStyles = (0, import_deepmerge3.default)(styles ?? {}, (_b = step.styles) != null ? _b : {}), options = (0, import_deepmerge3.default)(defaultOptions2, mergedStyles.options || {}), hideBeacon2 = step.placement === "center" || step.disableBeacon, { width } = options;
    window.innerWidth > 480 && (width = 380), "width" in options && (width = typeof options.width == "number" && window.innerWidth < options.width ? window.innerWidth - 30 : options.width);
    let overlay = { bottom: 0, left: 0, overflow: "hidden", position: "absolute", right: 0, top: 0, zIndex: options.zIndex }, defaultStyles = { beacon: { ...buttonBase, display: hideBeacon2 ? "none" : "inline-block", height: options.beaconSize, position: "relative", width: options.beaconSize, zIndex: options.zIndex }, beaconInner: { animation: "joyride-beacon-inner 1.2s infinite ease-in-out", backgroundColor: options.primaryColor, borderRadius: "50%", display: "block", height: "50%", left: "50%", opacity: 0.7, position: "absolute", top: "50%", transform: "translate(-50%, -50%)", width: "50%" }, beaconOuter: { animation: "joyride-beacon-outer 1.2s infinite ease-in-out", backgroundColor: `rgba(${hexToRGB(options.primaryColor).join(",")}, 0.2)`, border: `2px solid ${options.primaryColor}`, borderRadius: "50%", boxSizing: "border-box", display: "block", height: "100%", left: 0, opacity: 0.9, position: "absolute", top: 0, transformOrigin: "center", width: "100%" }, tooltip: { backgroundColor: options.backgroundColor, borderRadius: 5, boxSizing: "border-box", color: options.textColor, fontSize: 16, maxWidth: "100%", padding: 15, position: "relative", width }, tooltipContainer: { lineHeight: 1.4, textAlign: "center" }, tooltipTitle: { fontSize: 18, margin: 0 }, tooltipContent: { padding: "20px 10px" }, tooltipFooter: { alignItems: "center", display: "flex", justifyContent: "flex-end", marginTop: 15 }, tooltipFooterSpacer: { flex: 1 }, buttonNext: { ...buttonBase, backgroundColor: options.primaryColor, borderRadius: 4, color: "#fff" }, buttonBack: { ...buttonBase, color: options.primaryColor, marginLeft: "auto", marginRight: 5 }, buttonClose: { ...buttonBase, color: options.textColor, height: 14, padding: 15, position: "absolute", right: 0, top: 0, width: 14 }, buttonSkip: { ...buttonBase, color: options.textColor, fontSize: 14 }, overlay: { ...overlay, backgroundColor: options.overlayColor, mixBlendMode: "hard-light" }, overlayLegacy: { ...overlay }, overlayLegacyCenter: { ...overlay, backgroundColor: options.overlayColor }, spotlight: { ...spotlight, backgroundColor: "gray" }, spotlightLegacy: { ...spotlight, boxShadow: `0 0 0 9999px ${options.overlayColor}, ${options.spotlightShadow}` }, floaterStyles: { arrow: { color: (_e = (_d = (_c = mergedFloaterProps?.styles) == null ? void 0 : _c.arrow) == null ? void 0 : _d.color) != null ? _e : options.arrowColor }, options: { zIndex: options.zIndex + 100 } }, options };
    return (0, import_deepmerge3.default)(defaultStyles, mergedStyles);
  }
  function getTourProps(props) {
    return pick(props, "beaconComponent", "disableCloseOnEsc", "disableOverlay", "disableOverlayClose", "disableScrolling", "disableScrollParentFix", "floaterProps", "hideBackButton", "hideCloseButton", "locale", "showProgress", "showSkipButton", "spotlightClicks", "spotlightPadding", "styles", "tooltipComponent");
  }
  function getMergedStep(props, currentStep) {
    var _a, _b, _c, _d, _e, _f;
    let step = currentStep ?? {}, mergedStep = import_deepmerge2.default.all([defaultStep, getTourProps(props), step], { isMergeableObject: src_default.plainObject }), mergedStyles = getStyles2(props, mergedStep), scrollParent2 = hasCustomScrollParent(getElement(mergedStep.target), mergedStep.disableScrollParentFix), floaterProps = import_deepmerge2.default.all([defaultFloaterProps, (_a = props.floaterProps) != null ? _a : {}, (_b = mergedStep.floaterProps) != null ? _b : {}]);
    return floaterProps.offset = mergedStep.offset, floaterProps.styles = (0, import_deepmerge2.default)((_c = floaterProps.styles) != null ? _c : {}, mergedStyles.floaterStyles), floaterProps.offset += (_e = (_d = props.spotlightPadding) != null ? _d : mergedStep.spotlightPadding) != null ? _e : 0, mergedStep.placementBeacon && floaterProps.wrapperOptions && (floaterProps.wrapperOptions.placement = mergedStep.placementBeacon), scrollParent2 && floaterProps.options.preventOverflow && (floaterProps.options.preventOverflow.boundariesElement = "window"), { ...mergedStep, locale: import_deepmerge2.default.all([defaultLocale, (_f = props.locale) != null ? _f : {}, mergedStep.locale || {}]), floaterProps, styles: omit(mergedStyles, "floaterStyles") };
  }
  function validateStep(step, debug = !1) {
    return src_default.plainObject(step) ? step.target ? !0 : (log2({ title: "validateStep", data: "target is missing from the step", warn: !0, debug }), !1) : (log2({ title: "validateStep", data: "step must be an object", warn: !0, debug }), !1);
  }
  function validateSteps(steps, debug = !1) {
    return src_default.array(steps) ? steps.every((d2) => validateStep(d2, debug)) : (log2({ title: "validateSteps", data: "steps must be an array", warn: !0, debug }), !1);
  }
  function createStore(options) {
    return new Store(options);
  }
  function JoyrideSpotlight({ styles }) {
    return createElement("div", { key: "JoyrideSpotlight", className: "react-joyride__spotlight", "data-test-id": "spotlight", style: styles });
  }
  function JoyrideTooltipCloseButton({ styles, ...props }) {
    let { color: color2, height, width, ...style } = styles;
    return react_default.createElement("button", { style, type: "button", ...props }, react_default.createElement("svg", { height: typeof height == "number" ? `${height}px` : height, preserveAspectRatio: "xMidYMid", version: "1.1", viewBox: "0 0 18 18", width: typeof width == "number" ? `${width}px` : width, xmlns: "http://www.w3.org/2000/svg" }, react_default.createElement("g", null, react_default.createElement("path", { d: "M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z", fill: color2 }))));
  }
  function JoyrideTooltipContainer(props) {
    let { backProps, closeProps, index, isLastStep, primaryProps, skipProps, step, tooltipProps } = props, { content, hideBackButton, hideCloseButton, hideFooter, showSkipButton, styles, title } = step, output = {};
    return output.primary = createElement("button", { "data-test-id": "button-primary", style: styles.buttonNext, type: "button", ...primaryProps }), showSkipButton && !isLastStep && (output.skip = createElement("button", { "aria-live": "off", "data-test-id": "button-skip", style: styles.buttonSkip, type: "button", ...skipProps })), !hideBackButton && index > 0 && (output.back = createElement("button", { "data-test-id": "button-back", style: styles.buttonBack, type: "button", ...backProps })), output.close = !hideCloseButton && createElement(CloseButton_default, { "data-test-id": "button-close", styles: styles.buttonClose, ...closeProps }), createElement("div", { key: "JoyrideTooltip", "aria-label": getReactNodeText(title ?? content), className: "react-joyride__tooltip", style: styles.tooltip, ...tooltipProps }, createElement("div", { style: styles.tooltipContainer }, title && createElement("h1", { "aria-label": getReactNodeText(title), style: styles.tooltipTitle }, title), createElement("div", { style: styles.tooltipContent }, content)), !hideFooter && createElement("div", { style: styles.tooltipFooter }, createElement("div", { style: styles.tooltipFooterSpacer }, output.skip), output.back, output.primary), output.close);
  }
  function GuidedTour({ step, steps, onClose, onComplete }) {
    let [stepIndex, setStepIndex] = useState(null), theme2 = useTheme();
    return useEffect(() => {
      let timeout;
      return setStepIndex((current) => {
        let index = steps.findIndex(({ key }) => key === step);
        return index === -1 ? null : index === current ? current : (timeout = setTimeout(setStepIndex, 500, index), null);
      }), () => clearTimeout(timeout);
    }, [step, steps]), stepIndex === null ? null : react_default.createElement(components_default2, { continuous: !0, steps, stepIndex, spotlightPadding: 0, disableCloseOnEsc: !0, disableOverlayClose: !0, disableScrolling: !0, callback: (data) => {
      data.action === ACTIONS.CLOSE && onClose(), data.action === ACTIONS.NEXT && data.index === data.size - 1 && onComplete();
    }, floaterProps: { disableAnimation: !0, styles: { arrow: { length: 20, spread: 2 }, floater: { filter: theme2.base === "light" ? "drop-shadow(0px 5px 5px rgba(0,0,0,0.05)) drop-shadow(0 1px 3px rgba(0,0,0,0.1))" : "drop-shadow(#fff5 0px 0px 0.5px) drop-shadow(#fff5 0px 0px 0.5px)" } } }, tooltipComponent: Tooltip, styles: { overlay: { mixBlendMode: "unset", backgroundColor: steps[stepIndex]?.target === "body" ? "rgba(27, 28, 29, 0.2)" : "none" }, spotlight: { backgroundColor: "none", border: `solid 2px ${theme2.color.secondary}`, boxShadow: "0px 0px 0px 9999px rgba(27, 28, 29, 0.2)" }, tooltip: { width: 280, color: theme2.color.lightest, background: theme2.color.secondary }, options: { zIndex: 9998, primaryColor: theme2.color.secondary, arrowColor: theme2.color.secondary } } });
  }
  function Onboarding({ api }) {
    let [enabled, setEnabled] = useState(!0), [showConfetti, setShowConfetti] = useState(!1), [step, setStep] = useState("1:Intro"), [primaryControl, setPrimaryControl] = useState(), [saveFromControls, setSaveFromControls] = useState(), [createNewStoryForm, setCreateNewStoryForm] = useState(), [createdStory, setCreatedStory] = useState(), selectStory = useCallback((storyId) => {
      try {
        let { id, refId } = api.getCurrentStoryData() || {};
        (id !== storyId || refId !== void 0) && api.selectStory(storyId);
      } catch {
      }
    }, [api]), disableOnboarding = useCallback(() => {
      let url = new URL(window.location.href), path = decodeURIComponent(url.searchParams.get("path"));
      url.search = `?path=${path}&onboarding=false`, history.replaceState({}, "", url.href), api.setQueryParams({ onboarding: "false" }), setEnabled(!1);
    }, [api, setEnabled]), completeOnboarding = useCallback(() => {
      api.emit(STORYBOOK_ADDON_ONBOARDING_CHANNEL, { step: "6:FinishedOnboarding", type: "telemetry" }), selectStory("configure-your-project--docs"), disableOnboarding();
    }, [api, selectStory, disableOnboarding]);
    if (useEffect(() => {
      api.setQueryParams({ onboarding: "true" }), selectStory("example-button--primary"), api.togglePanel(!0), api.togglePanelPosition("bottom"), api.setSelectedPanel(o);
    }, [api, selectStory]), useEffect(() => {
      let observer = new MutationObserver(() => {
        setPrimaryControl(document.getElementById("control-primary")), setSaveFromControls(document.getElementById("save-from-controls")), setCreateNewStoryForm(document.getElementById("create-new-story-form"));
      });
      return observer.observe(document.body, { childList: !0, subtree: !0 }), () => observer.disconnect();
    }, []), useEffect(() => {
      setStep((current) => ["1:Intro", "5:StoryCreated", "6:FinishedOnboarding"].includes(current) ? current : createNewStoryForm ? "4:CreateStory" : saveFromControls ? "3:SaveFromControls" : primaryControl ? "2:Controls" : "1:Intro");
    }, [createNewStoryForm, primaryControl, saveFromControls]), useEffect(() => api.on(SAVE_STORY_RESPONSE, ({ payload, success }) => {
      !success || !payload?.newStoryName || (setCreatedStory(payload), setShowConfetti(!0), setStep("5:StoryCreated"), setTimeout(() => api.clearNotification("save-story-success")));
    }), [api]), useEffect(() => api.emit(STORYBOOK_ADDON_ONBOARDING_CHANNEL, { step, type: "telemetry" }), [api, step]), !enabled) return null;
    let source = createdStory?.sourceFileContent, startIndex = source?.lastIndexOf(`export const ${createdStory?.newStoryExportName}`), snippet = source?.slice(startIndex).trim(), startingLineNumber = source?.slice(0, startIndex).split(`
`).length, steps = [{ key: "2:Controls", target: "#control-primary", title: "Interactive story playground", content: react_default.createElement(react_default.Fragment, null, "See how a story renders with different data and state without touching code. Try it out by toggling this button.", react_default.createElement(HighlightElement, { targetSelector: "#control-primary", pulsating: !0 })), offset: 20, placement: "right", disableBeacon: !0, disableOverlay: !0, spotlightClicks: !0, onNextButtonClick: () => {
      document.querySelector("#control-primary").click();
    } }, { key: "3:SaveFromControls", target: 'button[aria-label="Create new story with these settings"]', title: "Save your changes as a new story", content: react_default.createElement(react_default.Fragment, null, "Great! Storybook stories represent the key states of each of your components. After modifying a story, you can save your changes from here or reset it.", react_default.createElement(HighlightElement, { targetSelector: "button[aria-label='Create new story with these settings']" })), offset: 6, placement: "top", disableBeacon: !0, disableOverlay: !0, spotlightClicks: !0, onNextButtonClick: () => {
      document.querySelector('button[aria-label="Create new story with these settings"]').click();
    }, styles: { tooltip: { width: 400 } } }, { key: "5:StoryCreated", target: '#storybook-explorer-tree [data-selected="true"]', title: "You just added your first story!", content: react_default.createElement(react_default.Fragment, null, "Well done! You just created your first story from the Storybook manager. This automatically added a few lines of code in", " ", react_default.createElement(SpanHighlight, null, createdStory?.sourceFileName), ".", snippet && react_default.createElement(ThemeProvider, { theme: convert(themes.dark) }, react_default.createElement(CodeWrapper, null, react_default.createElement(SyntaxHighlighter, { language: "jsx", showLineNumbers: !0, startingLineNumber }, snippet)))), offset: 12, placement: "right", disableBeacon: !0, disableOverlay: !0, styles: { tooltip: { width: 400 } } }];
    return react_default.createElement(ThemeProvider, { theme }, showConfetti && react_default.createElement(Confetti, null), step === "1:Intro" ? react_default.createElement(SplashScreen, { onDismiss: () => setStep("2:Controls") }) : react_default.createElement(GuidedTour, { step, steps, onClose: disableOnboarding, onComplete: completeOnboarding }));
  }
  var __create, __defProp2, __getOwnPropDesc, __getOwnPropNames2, __getProtoOf, __hasOwnProp, __commonJS, __copyProps, __toESM, require_scroll, require_scrollparent, require_react_innertext, require_cjs, require_react_is_development, require_react_is, require_object_assign, require_ReactPropTypesSecret, require_has, require_checkPropTypes, require_factoryWithTypeCheckers, require_prop_types, e, t, r, a, o2, n, i, c, s, l, d, p, u, f, g, m, h, v, x, b, P2, B, W, _, S, w, C, L, M, T, N, k, Wrapper, Confetti, STORYBOOK_ADDON_ONBOARDING_CHANNEL, isFunction, isNull, isRegex, isObject, isUndefined, objectTypes, primitiveTypes, DOM_PROPERTIES_TO_CHECK, src_default, import_scroll, import_scrollparent, import_react_innertext, import_deepmerge2, import_deepmerge3, import_prop_types, isBrowser, timeoutDuration, supportsMicroTasks, debounce, isIE11, isIE10, classCallCheck, createClass, defineProperty, _extends, isFirefox, placements, validPlacements, BEHAVIORS, modifiers, Defaults, Popper, popper_default, import_deepmerge, DOM_PROPERTIES_TO_CHECK2, objectTypes2, primitiveTypes2, esm_default, isFunction3, isNull2, isRegex2, isObject2, isUndefined2, __values, DOM_PROPERTIES_TO_CHECK3, objectTypes3, primitiveTypes3, esm_default2, DEFAULTS, VALIDATOR_ARG_ERROR_MESSAGE, MESSAGE_ARG_ERROR_MESSAGE, STATUS, isReact16, ReactFloaterPortal, FloaterArrow, _excluded$1, Floater, ReactFloaterWrapper, defaultOptions, _excluded, POSITIONING_PROPS, ReactFloater, __defProp22, __defNormalProp, __publicField, ACTIONS, EVENTS, LIFECYCLE, STATUS2, isReact162, defaultFloaterProps, defaultLocale, defaultStep, defaultProps, defaultOptions2, buttonBase, spotlight, defaultState, validKeys, Store, Spotlight_default, JoyrideOverlay, JoyridePortal, Scope, JoyrideBeacon, CloseButton_default, Container_default, JoyrideTooltip, JoyrideStep, Joyride, components_default2, StyledButton, Button2, TooltipBody, Wrapper2, TooltipHeader, TooltipTitle, TooltipContent, TooltipFooter, Count, Tooltip, fadeIn, slideIn, scaleIn, rotate, Wrapper3, Backdrop, Content, Features, RadialButton, ArrowIcon, ProgressCircle, SplashScreen, SpanHighlight, CodeWrapper, theme, init_Onboarding_C2PY5T7U = __esm({
    "node_modules/@storybook/addon-onboarding/dist/Onboarding-C2PY5T7U.js"() {
      init_define_module();
      init_define_process_env();
      init_define_process_env_NODE_PATH();
      init_react();
      init_react();
      init_components();
      init_controls();
      init_core_events();
      init_theming();
      init_react_dom();
      init_react_dom();
      init_icons();
      __create = Object.create, __defProp2 = Object.defineProperty, __getOwnPropDesc = Object.getOwnPropertyDescriptor, __getOwnPropNames2 = Object.getOwnPropertyNames, __getProtoOf = Object.getPrototypeOf, __hasOwnProp = Object.prototype.hasOwnProperty, __commonJS = (cb, mod) => function() {
        return mod || (0, cb[__getOwnPropNames2(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
      }, __copyProps = (to, from, except, desc) => {
        if (from && typeof from == "object" || typeof from == "function") for (let key of __getOwnPropNames2(from)) !__hasOwnProp.call(to, key) && key !== except && __defProp2(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
        return to;
      }, __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(isNodeMode || !mod || !mod.__esModule ? __defProp2(target, "default", { value: mod, enumerable: !0 }) : target, mod)), require_scroll = __commonJS({ "../../node_modules/scroll/index.js"(exports, module) {
        var E_NOSCROLL = new Error("Element already at target scroll position"), E_CANCELLED = new Error("Scroll cancelled"), min = Math.min, ms = Date.now;
        module.exports = { left: make("scrollLeft"), top: make("scrollTop") };
        function make(prop) {
          return function(el, to, opts, cb) {
            opts = opts || {}, typeof opts == "function" && (cb = opts, opts = {}), typeof cb != "function" && (cb = noop2);
            var start = ms(), from = el[prop], ease = opts.ease || inOutSine, duration = isNaN(opts.duration) ? 350 : +opts.duration, cancelled = !1;
            return from === to ? cb(E_NOSCROLL, el[prop]) : requestAnimationFrame(animate), cancel;
            function cancel() {
              cancelled = !0;
            }
            function animate(timestamp) {
              if (cancelled) return cb(E_CANCELLED, el[prop]);
              var now = ms(), time = min(1, (now - start) / duration), eased = ease(time);
              el[prop] = eased * (to - from) + from, time < 1 ? requestAnimationFrame(animate) : requestAnimationFrame(function() {
                cb(null, el[prop]);
              });
            }
          };
        }
        function inOutSine(n2) {
          return 0.5 * (1 - Math.cos(Math.PI * n2));
        }
        function noop2() {
        }
      } }), require_scrollparent = __commonJS({ "../../node_modules/scrollparent/scrollparent.js"(exports, module) {
        (function(root, factory) {
          typeof define == "function" && define.amd ? define([], factory) : typeof module == "object" && module.exports ? module.exports = factory() : root.Scrollparent = factory();
        })(exports, function() {
          function isScrolling(node) {
            var overflow = getComputedStyle(node, null).getPropertyValue("overflow");
            return overflow.indexOf("scroll") > -1 || overflow.indexOf("auto") > -1;
          }
          function scrollParent2(node) {
            if (node instanceof HTMLElement || node instanceof SVGElement) {
              for (var current = node.parentNode; current.parentNode; ) {
                if (isScrolling(current)) return current;
                current = current.parentNode;
              }
              return document.scrollingElement || document.documentElement;
            }
          }
          return scrollParent2;
        });
      } }), require_react_innertext = __commonJS({ "../../node_modules/react-innertext/index.js"(exports, module) {
        var hasProps = function(jsx2) {
          return Object.prototype.hasOwnProperty.call(jsx2, "props");
        }, reduceJsxToString = function(previous, current) {
          return previous + innerText2(current);
        }, innerText2 = function(jsx2) {
          return jsx2 === null || typeof jsx2 == "boolean" || typeof jsx2 > "u" ? "" : typeof jsx2 == "number" ? jsx2.toString() : typeof jsx2 == "string" ? jsx2 : Array.isArray(jsx2) ? jsx2.reduce(reduceJsxToString, "") : hasProps(jsx2) && Object.prototype.hasOwnProperty.call(jsx2.props, "children") ? innerText2(jsx2.props.children) : "";
        };
        innerText2.default = innerText2, module.exports = innerText2;
      } }), require_cjs = __commonJS({ "../../node_modules/deepmerge/dist/cjs.js"(exports, module) {
        var isMergeableObject = function(value) {
          return isNonNullObject(value) && !isSpecial(value);
        };
        function isNonNullObject(value) {
          return !!value && typeof value == "object";
        }
        function isSpecial(value) {
          var stringValue = Object.prototype.toString.call(value);
          return stringValue === "[object RegExp]" || stringValue === "[object Date]" || isReactElement(value);
        }
        var canUseSymbol = typeof Symbol == "function" && Symbol.for, REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for("react.element") : 60103;
        function isReactElement(value) {
          return value.$$typeof === REACT_ELEMENT_TYPE;
        }
        function emptyTarget(val) {
          return Array.isArray(val) ? [] : {};
        }
        function cloneUnlessOtherwiseSpecified(value, options) {
          return options.clone !== !1 && options.isMergeableObject(value) ? deepmerge4(emptyTarget(value), value, options) : value;
        }
        function defaultArrayMerge(target, source, options) {
          return target.concat(source).map(function(element) {
            return cloneUnlessOtherwiseSpecified(element, options);
          });
        }
        function getMergeFunction(key, options) {
          if (!options.customMerge) return deepmerge4;
          var customMerge = options.customMerge(key);
          return typeof customMerge == "function" ? customMerge : deepmerge4;
        }
        function getEnumerableOwnPropertySymbols(target) {
          return Object.getOwnPropertySymbols ? Object.getOwnPropertySymbols(target).filter(function(symbol) {
            return Object.propertyIsEnumerable.call(target, symbol);
          }) : [];
        }
        function getKeys(target) {
          return Object.keys(target).concat(getEnumerableOwnPropertySymbols(target));
        }
        function propertyIsOnObject(object, property) {
          try {
            return property in object;
          } catch {
            return !1;
          }
        }
        function propertyIsUnsafe(target, key) {
          return propertyIsOnObject(target, key) && !(Object.hasOwnProperty.call(target, key) && Object.propertyIsEnumerable.call(target, key));
        }
        function mergeObject(target, source, options) {
          var destination = {};
          return options.isMergeableObject(target) && getKeys(target).forEach(function(key) {
            destination[key] = cloneUnlessOtherwiseSpecified(target[key], options);
          }), getKeys(source).forEach(function(key) {
            propertyIsUnsafe(target, key) || (propertyIsOnObject(target, key) && options.isMergeableObject(source[key]) ? destination[key] = getMergeFunction(key, options)(target[key], source[key], options) : destination[key] = cloneUnlessOtherwiseSpecified(source[key], options));
          }), destination;
        }
        function deepmerge4(target, source, options) {
          options = options || {}, options.arrayMerge = options.arrayMerge || defaultArrayMerge, options.isMergeableObject = options.isMergeableObject || isMergeableObject, options.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;
          var sourceIsArray = Array.isArray(source), targetIsArray = Array.isArray(target), sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;
          return sourceAndTargetTypesMatch ? sourceIsArray ? options.arrayMerge(target, source, options) : mergeObject(target, source, options) : cloneUnlessOtherwiseSpecified(source, options);
        }
        deepmerge4.all = function(array, options) {
          if (!Array.isArray(array)) throw new Error("first argument should be an array");
          return array.reduce(function(prev, next) {
            return deepmerge4(prev, next, options);
          }, {});
        };
        var deepmerge_1 = deepmerge4;
        module.exports = deepmerge_1;
      } }), require_react_is_development = __commonJS({ "../../node_modules/react-is/cjs/react-is.development.js"(exports) {
        (function() {
          var hasSymbol = typeof Symbol == "function" && Symbol.for, REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for("react.element") : 60103, REACT_PORTAL_TYPE = hasSymbol ? Symbol.for("react.portal") : 60106, REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for("react.fragment") : 60107, REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for("react.strict_mode") : 60108, REACT_PROFILER_TYPE = hasSymbol ? Symbol.for("react.profiler") : 60114, REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for("react.provider") : 60109, REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for("react.context") : 60110, REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for("react.async_mode") : 60111, REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for("react.concurrent_mode") : 60111, REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for("react.forward_ref") : 60112, REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for("react.suspense") : 60113, REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for("react.suspense_list") : 60120, REACT_MEMO_TYPE = hasSymbol ? Symbol.for("react.memo") : 60115, REACT_LAZY_TYPE = hasSymbol ? Symbol.for("react.lazy") : 60116, REACT_BLOCK_TYPE = hasSymbol ? Symbol.for("react.block") : 60121, REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for("react.fundamental") : 60117, REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for("react.responder") : 60118, REACT_SCOPE_TYPE = hasSymbol ? Symbol.for("react.scope") : 60119;
          function isValidElementType(type) {
            return typeof type == "string" || typeof type == "function" || type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type == "object" && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);
          }
          function typeOf(object) {
            if (typeof object == "object" && object !== null) {
              var $$typeof = object.$$typeof;
              switch ($$typeof) {
                case REACT_ELEMENT_TYPE:
                  var type = object.type;
                  switch (type) {
                    case REACT_ASYNC_MODE_TYPE:
                    case REACT_CONCURRENT_MODE_TYPE:
                    case REACT_FRAGMENT_TYPE:
                    case REACT_PROFILER_TYPE:
                    case REACT_STRICT_MODE_TYPE:
                    case REACT_SUSPENSE_TYPE:
                      return type;
                    default:
                      var $$typeofType = type && type.$$typeof;
                      switch ($$typeofType) {
                        case REACT_CONTEXT_TYPE:
                        case REACT_FORWARD_REF_TYPE:
                        case REACT_LAZY_TYPE:
                        case REACT_MEMO_TYPE:
                        case REACT_PROVIDER_TYPE:
                          return $$typeofType;
                        default:
                          return $$typeof;
                      }
                  }
                case REACT_PORTAL_TYPE:
                  return $$typeof;
              }
            }
          }
          var AsyncMode = REACT_ASYNC_MODE_TYPE, ConcurrentMode = REACT_CONCURRENT_MODE_TYPE, ContextConsumer = REACT_CONTEXT_TYPE, ContextProvider = REACT_PROVIDER_TYPE, Element = REACT_ELEMENT_TYPE, ForwardRef = REACT_FORWARD_REF_TYPE, Fragment2 = REACT_FRAGMENT_TYPE, Lazy = REACT_LAZY_TYPE, Memo = REACT_MEMO_TYPE, Portal = REACT_PORTAL_TYPE, Profiler2 = REACT_PROFILER_TYPE, StrictMode2 = REACT_STRICT_MODE_TYPE, Suspense2 = REACT_SUSPENSE_TYPE, hasWarnedAboutDeprecatedIsAsyncMode = !1;
          function isAsyncMode(object) {
            return hasWarnedAboutDeprecatedIsAsyncMode || (hasWarnedAboutDeprecatedIsAsyncMode = !0, console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.")), isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;
          }
          function isConcurrentMode(object) {
            return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;
          }
          function isContextConsumer(object) {
            return typeOf(object) === REACT_CONTEXT_TYPE;
          }
          function isContextProvider(object) {
            return typeOf(object) === REACT_PROVIDER_TYPE;
          }
          function isElement(object) {
            return typeof object == "object" && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;
          }
          function isForwardRef(object) {
            return typeOf(object) === REACT_FORWARD_REF_TYPE;
          }
          function isFragment(object) {
            return typeOf(object) === REACT_FRAGMENT_TYPE;
          }
          function isLazy(object) {
            return typeOf(object) === REACT_LAZY_TYPE;
          }
          function isMemo(object) {
            return typeOf(object) === REACT_MEMO_TYPE;
          }
          function isPortal(object) {
            return typeOf(object) === REACT_PORTAL_TYPE;
          }
          function isProfiler(object) {
            return typeOf(object) === REACT_PROFILER_TYPE;
          }
          function isStrictMode(object) {
            return typeOf(object) === REACT_STRICT_MODE_TYPE;
          }
          function isSuspense(object) {
            return typeOf(object) === REACT_SUSPENSE_TYPE;
          }
          exports.AsyncMode = AsyncMode, exports.ConcurrentMode = ConcurrentMode, exports.ContextConsumer = ContextConsumer, exports.ContextProvider = ContextProvider, exports.Element = Element, exports.ForwardRef = ForwardRef, exports.Fragment = Fragment2, exports.Lazy = Lazy, exports.Memo = Memo, exports.Portal = Portal, exports.Profiler = Profiler2, exports.StrictMode = StrictMode2, exports.Suspense = Suspense2, exports.isAsyncMode = isAsyncMode, exports.isConcurrentMode = isConcurrentMode, exports.isContextConsumer = isContextConsumer, exports.isContextProvider = isContextProvider, exports.isElement = isElement, exports.isForwardRef = isForwardRef, exports.isFragment = isFragment, exports.isLazy = isLazy, exports.isMemo = isMemo, exports.isPortal = isPortal, exports.isProfiler = isProfiler, exports.isStrictMode = isStrictMode, exports.isSuspense = isSuspense, exports.isValidElementType = isValidElementType, exports.typeOf = typeOf;
        })();
      } }), require_react_is = __commonJS({ "../../node_modules/react-is/index.js"(exports, module) {
        module.exports = require_react_is_development();
      } }), require_object_assign = __commonJS({ "../../node_modules/object-assign/index.js"(exports, module) {
        var getOwnPropertySymbols = Object.getOwnPropertySymbols, hasOwnProperty = Object.prototype.hasOwnProperty, propIsEnumerable = Object.prototype.propertyIsEnumerable;
        function toObject(val) {
          if (val == null) throw new TypeError("Object.assign cannot be called with null or undefined");
          return Object(val);
        }
        function shouldUseNative() {
          try {
            if (!Object.assign) return !1;
            var test1 = new String("abc");
            if (test1[5] = "de", Object.getOwnPropertyNames(test1)[0] === "5") return !1;
            for (var test2 = {}, i2 = 0; i2 < 10; i2++) test2["_" + String.fromCharCode(i2)] = i2;
            var order2 = Object.getOwnPropertyNames(test2).map(function(n2) {
              return test2[n2];
            });
            if (order2.join("") !== "0123456789") return !1;
            var test3 = {};
            return "abcdefghijklmnopqrst".split("").forEach(function(letter) {
              test3[letter] = letter;
            }), Object.keys(Object.assign({}, test3)).join("") === "abcdefghijklmnopqrst";
          } catch {
            return !1;
          }
        }
        module.exports = shouldUseNative() ? Object.assign : function(target, source) {
          for (var from, to = toObject(target), symbols, s2 = 1; s2 < arguments.length; s2++) {
            from = Object(arguments[s2]);
            for (var key in from) hasOwnProperty.call(from, key) && (to[key] = from[key]);
            if (getOwnPropertySymbols) {
              symbols = getOwnPropertySymbols(from);
              for (var i2 = 0; i2 < symbols.length; i2++) propIsEnumerable.call(from, symbols[i2]) && (to[symbols[i2]] = from[symbols[i2]]);
            }
          }
          return to;
        };
      } }), require_ReactPropTypesSecret = __commonJS({ "../../node_modules/prop-types/lib/ReactPropTypesSecret.js"(exports, module) {
        var ReactPropTypesSecret = "SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";
        module.exports = ReactPropTypesSecret;
      } }), require_has = __commonJS({ "../../node_modules/prop-types/lib/has.js"(exports, module) {
        module.exports = Function.call.bind(Object.prototype.hasOwnProperty);
      } }), require_checkPropTypes = __commonJS({ "../../node_modules/prop-types/checkPropTypes.js"(exports, module) {
        var printWarning = function() {
        };
        ReactPropTypesSecret = require_ReactPropTypesSecret(), loggedTypeFailures = {}, has = require_has(), printWarning = function(text) {
          var message = "Warning: " + text;
          typeof console < "u" && console.error(message);
          try {
            throw new Error(message);
          } catch {
          }
        };
        var ReactPropTypesSecret, loggedTypeFailures, has;
        function checkPropTypes(typeSpecs, values, location, componentName, getStack) {
          for (var typeSpecName in typeSpecs) if (has(typeSpecs, typeSpecName)) {
            var error;
            try {
              if (typeof typeSpecs[typeSpecName] != "function") {
                var err = Error((componentName || "React class") + ": " + location + " type `" + typeSpecName + "` is invalid; it must be a function, usually from the `prop-types` package, but received `" + typeof typeSpecs[typeSpecName] + "`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");
                throw err.name = "Invariant Violation", err;
              }
              error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);
            } catch (ex) {
              error = ex;
            }
            if (error && !(error instanceof Error) && printWarning((componentName || "React class") + ": type specification of " + location + " `" + typeSpecName + "` is invalid; the type checker function must return `null` or an `Error` but returned a " + typeof error + ". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."), error instanceof Error && !(error.message in loggedTypeFailures)) {
              loggedTypeFailures[error.message] = !0;
              var stack = getStack ? getStack() : "";
              printWarning("Failed " + location + " type: " + error.message + (stack ?? ""));
            }
          }
        }
        checkPropTypes.resetWarningCache = function() {
          loggedTypeFailures = {};
        }, module.exports = checkPropTypes;
      } }), require_factoryWithTypeCheckers = __commonJS({ "../../node_modules/prop-types/factoryWithTypeCheckers.js"(exports, module) {
        var ReactIs = require_react_is(), assign = require_object_assign(), ReactPropTypesSecret = require_ReactPropTypesSecret(), has = require_has(), checkPropTypes = require_checkPropTypes(), printWarning = function() {
        };
        printWarning = function(text) {
          var message = "Warning: " + text;
          typeof console < "u" && console.error(message);
          try {
            throw new Error(message);
          } catch {
          }
        };
        function emptyFunctionThatReturnsNull() {
          return null;
        }
        module.exports = function(isValidElement2, throwOnDirectAccess) {
          var ITERATOR_SYMBOL = typeof Symbol == "function" && Symbol.iterator, FAUX_ITERATOR_SYMBOL = "@@iterator";
          function getIteratorFn(maybeIterable) {
            var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);
            if (typeof iteratorFn == "function") return iteratorFn;
          }
          var ANONYMOUS = "<<anonymous>>", ReactPropTypes = { array: createPrimitiveTypeChecker("array"), bigint: createPrimitiveTypeChecker("bigint"), bool: createPrimitiveTypeChecker("boolean"), func: createPrimitiveTypeChecker("function"), number: createPrimitiveTypeChecker("number"), object: createPrimitiveTypeChecker("object"), string: createPrimitiveTypeChecker("string"), symbol: createPrimitiveTypeChecker("symbol"), any: createAnyTypeChecker(), arrayOf: createArrayOfTypeChecker, element: createElementTypeChecker(), elementType: createElementTypeTypeChecker(), instanceOf: createInstanceTypeChecker, node: createNodeChecker(), objectOf: createObjectOfTypeChecker, oneOf: createEnumTypeChecker, oneOfType: createUnionTypeChecker, shape: createShapeTypeChecker, exact: createStrictShapeTypeChecker };
          function is4(x2, y2) {
            return x2 === y2 ? x2 !== 0 || 1 / x2 === 1 / y2 : x2 !== x2 && y2 !== y2;
          }
          function PropTypeError(message, data) {
            this.message = message, this.data = data && typeof data == "object" ? data : {}, this.stack = "";
          }
          PropTypeError.prototype = Error.prototype;
          function createChainableTypeChecker(validate) {
            var manualPropTypeCallCache = {}, manualPropTypeWarningCount = 0;
            function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {
              if (componentName = componentName || ANONYMOUS, propFullName = propFullName || propName, secret !== ReactPropTypesSecret) {
                if (throwOnDirectAccess) {
                  var err = new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types");
                  throw err.name = "Invariant Violation", err;
                } else if (typeof console < "u") {
                  var cacheKey = componentName + ":" + propName;
                  !manualPropTypeCallCache[cacheKey] && manualPropTypeWarningCount < 3 && (printWarning("You are manually calling a React.PropTypes validation function for the `" + propFullName + "` prop on `" + componentName + "`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details."), manualPropTypeCallCache[cacheKey] = !0, manualPropTypeWarningCount++);
                }
              }
              return props[propName] == null ? isRequired ? props[propName] === null ? new PropTypeError("The " + location + " `" + propFullName + "` is marked as required " + ("in `" + componentName + "`, but its value is `null`.")) : new PropTypeError("The " + location + " `" + propFullName + "` is marked as required in " + ("`" + componentName + "`, but its value is `undefined`.")) : null : validate(props, propName, componentName, location, propFullName);
            }
            var chainedCheckType = checkType.bind(null, !1);
            return chainedCheckType.isRequired = checkType.bind(null, !0), chainedCheckType;
          }
          function createPrimitiveTypeChecker(expectedType) {
            function validate(props, propName, componentName, location, propFullName, secret) {
              var propValue = props[propName], propType = getPropType(propValue);
              if (propType !== expectedType) {
                var preciseType = getPreciseType(propValue);
                return new PropTypeError("Invalid " + location + " `" + propFullName + "` of type " + ("`" + preciseType + "` supplied to `" + componentName + "`, expected ") + ("`" + expectedType + "`."), { expectedType });
              }
              return null;
            }
            return createChainableTypeChecker(validate);
          }
          function createAnyTypeChecker() {
            return createChainableTypeChecker(emptyFunctionThatReturnsNull);
          }
          function createArrayOfTypeChecker(typeChecker) {
            function validate(props, propName, componentName, location, propFullName) {
              if (typeof typeChecker != "function") return new PropTypeError("Property `" + propFullName + "` of component `" + componentName + "` has invalid PropType notation inside arrayOf.");
              var propValue = props[propName];
              if (!Array.isArray(propValue)) {
                var propType = getPropType(propValue);
                return new PropTypeError("Invalid " + location + " `" + propFullName + "` of type " + ("`" + propType + "` supplied to `" + componentName + "`, expected an array."));
              }
              for (var i2 = 0; i2 < propValue.length; i2++) {
                var error = typeChecker(propValue, i2, componentName, location, propFullName + "[" + i2 + "]", ReactPropTypesSecret);
                if (error instanceof Error) return error;
              }
              return null;
            }
            return createChainableTypeChecker(validate);
          }
          function createElementTypeChecker() {
            function validate(props, propName, componentName, location, propFullName) {
              var propValue = props[propName];
              if (!isValidElement2(propValue)) {
                var propType = getPropType(propValue);
                return new PropTypeError("Invalid " + location + " `" + propFullName + "` of type " + ("`" + propType + "` supplied to `" + componentName + "`, expected a single ReactElement."));
              }
              return null;
            }
            return createChainableTypeChecker(validate);
          }
          function createElementTypeTypeChecker() {
            function validate(props, propName, componentName, location, propFullName) {
              var propValue = props[propName];
              if (!ReactIs.isValidElementType(propValue)) {
                var propType = getPropType(propValue);
                return new PropTypeError("Invalid " + location + " `" + propFullName + "` of type " + ("`" + propType + "` supplied to `" + componentName + "`, expected a single ReactElement type."));
              }
              return null;
            }
            return createChainableTypeChecker(validate);
          }
          function createInstanceTypeChecker(expectedClass) {
            function validate(props, propName, componentName, location, propFullName) {
              if (!(props[propName] instanceof expectedClass)) {
                var expectedClassName = expectedClass.name || ANONYMOUS, actualClassName = getClassName(props[propName]);
                return new PropTypeError("Invalid " + location + " `" + propFullName + "` of type " + ("`" + actualClassName + "` supplied to `" + componentName + "`, expected ") + ("instance of `" + expectedClassName + "`."));
              }
              return null;
            }
            return createChainableTypeChecker(validate);
          }
          function createEnumTypeChecker(expectedValues) {
            if (!Array.isArray(expectedValues)) return arguments.length > 1 ? printWarning("Invalid arguments supplied to oneOf, expected an array, got " + arguments.length + " arguments. A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).") : printWarning("Invalid argument supplied to oneOf, expected an array."), emptyFunctionThatReturnsNull;
            function validate(props, propName, componentName, location, propFullName) {
              for (var propValue = props[propName], i2 = 0; i2 < expectedValues.length; i2++) if (is4(propValue, expectedValues[i2])) return null;
              var valuesString = JSON.stringify(expectedValues, function(key, value) {
                var type = getPreciseType(value);
                return type === "symbol" ? String(value) : value;
              });
              return new PropTypeError("Invalid " + location + " `" + propFullName + "` of value `" + String(propValue) + "` " + ("supplied to `" + componentName + "`, expected one of " + valuesString + "."));
            }
            return createChainableTypeChecker(validate);
          }
          function createObjectOfTypeChecker(typeChecker) {
            function validate(props, propName, componentName, location, propFullName) {
              if (typeof typeChecker != "function") return new PropTypeError("Property `" + propFullName + "` of component `" + componentName + "` has invalid PropType notation inside objectOf.");
              var propValue = props[propName], propType = getPropType(propValue);
              if (propType !== "object") return new PropTypeError("Invalid " + location + " `" + propFullName + "` of type " + ("`" + propType + "` supplied to `" + componentName + "`, expected an object."));
              for (var key in propValue) if (has(propValue, key)) {
                var error = typeChecker(propValue, key, componentName, location, propFullName + "." + key, ReactPropTypesSecret);
                if (error instanceof Error) return error;
              }
              return null;
            }
            return createChainableTypeChecker(validate);
          }
          function createUnionTypeChecker(arrayOfTypeCheckers) {
            if (!Array.isArray(arrayOfTypeCheckers)) return printWarning("Invalid argument supplied to oneOfType, expected an instance of array."), emptyFunctionThatReturnsNull;
            for (var i2 = 0; i2 < arrayOfTypeCheckers.length; i2++) {
              var checker = arrayOfTypeCheckers[i2];
              if (typeof checker != "function") return printWarning("Invalid argument supplied to oneOfType. Expected an array of check functions, but received " + getPostfixForTypeWarning(checker) + " at index " + i2 + "."), emptyFunctionThatReturnsNull;
            }
            function validate(props, propName, componentName, location, propFullName) {
              for (var expectedTypes = [], i3 = 0; i3 < arrayOfTypeCheckers.length; i3++) {
                var checker2 = arrayOfTypeCheckers[i3], checkerResult = checker2(props, propName, componentName, location, propFullName, ReactPropTypesSecret);
                if (checkerResult == null) return null;
                checkerResult.data && has(checkerResult.data, "expectedType") && expectedTypes.push(checkerResult.data.expectedType);
              }
              var expectedTypesMessage = expectedTypes.length > 0 ? ", expected one of type [" + expectedTypes.join(", ") + "]" : "";
              return new PropTypeError("Invalid " + location + " `" + propFullName + "` supplied to " + ("`" + componentName + "`" + expectedTypesMessage + "."));
            }
            return createChainableTypeChecker(validate);
          }
          function createNodeChecker() {
            function validate(props, propName, componentName, location, propFullName) {
              return isNode(props[propName]) ? null : new PropTypeError("Invalid " + location + " `" + propFullName + "` supplied to " + ("`" + componentName + "`, expected a ReactNode."));
            }
            return createChainableTypeChecker(validate);
          }
          function invalidValidatorError(componentName, location, propFullName, key, type) {
            return new PropTypeError((componentName || "React class") + ": " + location + " type `" + propFullName + "." + key + "` is invalid; it must be a function, usually from the `prop-types` package, but received `" + type + "`.");
          }
          function createShapeTypeChecker(shapeTypes) {
            function validate(props, propName, componentName, location, propFullName) {
              var propValue = props[propName], propType = getPropType(propValue);
              if (propType !== "object") return new PropTypeError("Invalid " + location + " `" + propFullName + "` of type `" + propType + "` " + ("supplied to `" + componentName + "`, expected `object`."));
              for (var key in shapeTypes) {
                var checker = shapeTypes[key];
                if (typeof checker != "function") return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));
                var error = checker(propValue, key, componentName, location, propFullName + "." + key, ReactPropTypesSecret);
                if (error) return error;
              }
              return null;
            }
            return createChainableTypeChecker(validate);
          }
          function createStrictShapeTypeChecker(shapeTypes) {
            function validate(props, propName, componentName, location, propFullName) {
              var propValue = props[propName], propType = getPropType(propValue);
              if (propType !== "object") return new PropTypeError("Invalid " + location + " `" + propFullName + "` of type `" + propType + "` " + ("supplied to `" + componentName + "`, expected `object`."));
              var allKeys = assign({}, props[propName], shapeTypes);
              for (var key in allKeys) {
                var checker = shapeTypes[key];
                if (has(shapeTypes, key) && typeof checker != "function") return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));
                if (!checker) return new PropTypeError("Invalid " + location + " `" + propFullName + "` key `" + key + "` supplied to `" + componentName + "`.\nBad object: " + JSON.stringify(props[propName], null, "  ") + `
Valid keys: ` + JSON.stringify(Object.keys(shapeTypes), null, "  "));
                var error = checker(propValue, key, componentName, location, propFullName + "." + key, ReactPropTypesSecret);
                if (error) return error;
              }
              return null;
            }
            return createChainableTypeChecker(validate);
          }
          function isNode(propValue) {
            switch (typeof propValue) {
              case "number":
              case "string":
              case "undefined":
                return !0;
              case "boolean":
                return !propValue;
              case "object":
                if (Array.isArray(propValue)) return propValue.every(isNode);
                if (propValue === null || isValidElement2(propValue)) return !0;
                var iteratorFn = getIteratorFn(propValue);
                if (iteratorFn) {
                  var iterator = iteratorFn.call(propValue), step;
                  if (iteratorFn !== propValue.entries) {
                    for (; !(step = iterator.next()).done; ) if (!isNode(step.value)) return !1;
                  } else for (; !(step = iterator.next()).done; ) {
                    var entry = step.value;
                    if (entry && !isNode(entry[1])) return !1;
                  }
                } else return !1;
                return !0;
              default:
                return !1;
            }
          }
          function isSymbol(propType, propValue) {
            return propType === "symbol" ? !0 : propValue ? propValue["@@toStringTag"] === "Symbol" || typeof Symbol == "function" && propValue instanceof Symbol : !1;
          }
          function getPropType(propValue) {
            var propType = typeof propValue;
            return Array.isArray(propValue) ? "array" : propValue instanceof RegExp ? "object" : isSymbol(propType, propValue) ? "symbol" : propType;
          }
          function getPreciseType(propValue) {
            if (typeof propValue > "u" || propValue === null) return "" + propValue;
            var propType = getPropType(propValue);
            if (propType === "object") {
              if (propValue instanceof Date) return "date";
              if (propValue instanceof RegExp) return "regexp";
            }
            return propType;
          }
          function getPostfixForTypeWarning(value) {
            var type = getPreciseType(value);
            switch (type) {
              case "array":
              case "object":
                return "an " + type;
              case "boolean":
              case "date":
              case "regexp":
                return "a " + type;
              default:
                return type;
            }
          }
          function getClassName(propValue) {
            return !propValue.constructor || !propValue.constructor.name ? ANONYMOUS : propValue.constructor.name;
          }
          return ReactPropTypes.checkPropTypes = checkPropTypes, ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache, ReactPropTypes.PropTypes = ReactPropTypes, ReactPropTypes;
        };
      } }), require_prop_types = __commonJS({ "../../node_modules/prop-types/index.js"(exports, module) {
        ReactIs = require_react_is(), throwOnDirectAccess = !0, module.exports = require_factoryWithTypeCheckers()(ReactIs.isElement, throwOnDirectAccess);
        var ReactIs, throwOnDirectAccess;
      } }), e = '@keyframes Bc2PgW_ya{to{translate:0 var(--sh)}}@keyframes Bc2PgW_xa{to{translate:var(--xlp)0}}@keyframes Bc2PgW_r{50%{rotate:var(--hr)180deg}to{rotate:var(--r)360deg}}.Bc2PgW_c{z-index:1200;width:0;height:0;position:relative;overflow:visible}.Bc2PgW_p{animation:xa var(--dc)forwards cubic-bezier(var(--x1),var(--x2),var(--x3),var(--x4));animation-name:Bc2PgW_xa}.Bc2PgW_p>div{animation:ya var(--dc)forwards cubic-bezier(var(--y1),var(--y2),var(--y3),var(--y4));width:var(--w);height:var(--h);animation-name:Bc2PgW_ya;position:absolute;top:0;left:0}.Bc2PgW_p>div:before{content:"";background-color:var(--bgc);animation:r var(--rd)infinite linear;border-radius:var(--br);width:100%;height:100%;animation-name:Bc2PgW_r;display:block}', t = "Bc2PgW_p", r = "Bc2PgW_c", a = ["#FFC700", "#FF0000", "#2E3191", "#41BBC7"], o2 = 3500, n = 0.5, i = 150, c = "mix", s = 12, l = "", d = !0, p = 800, u = 1600;
      f = 200, g = 800, m = 0.1, h = 0.3, v = 0.5, x = Math.abs, b = Math.random, P2 = Math.round, B = Math.max, W = (e2) => document.createElement(e2), _ = (e2, t2) => e2.appendChild(t2), S = (e2, t2) => Array.from({ length: e2 }, (r2, a2) => ({ color: t2[a2 % t2.length], degree: 360 * a2 / e2 })), w = (e2, t2 = 2) => P2((e2 + Number.EPSILON) * 10 ** t2) / 10 ** t2, C = (e2, t2, r2, a2, o22) => (e2 - t2) * (o22 - a2) / (r2 - t2) + a2, L = (e2, t2) => e2 + t2 > 360 ? e2 + t2 - 360 : e2 + t2, M = () => b() > 0.5, T = Object.entries, N = 6, k = (e2) => e2 !== 1 && M();
      Wrapper = styled.div({ zIndex: 9999, position: "fixed", top: 0, left: "50%", width: "50%", height: "100%" }), Confetti = react_default.memo(function({ timeToFade = 5e3, colors = ["#CA90FF", "#FC521F", "#66BF3C", "#FF4785", "#FFAE00", "#1EA7FD"], ...confettiProps }) {
        return react_default.createElement(Wrapper, null, react_default.createElement(F, { colors, particleCount: 200, duration: 5e3, stageHeight: window.innerHeight, stageWidth: window.innerWidth, destroyAfterDone: !0, ...confettiProps }));
      });
      STORYBOOK_ADDON_ONBOARDING_CHANNEL = "STORYBOOK_ADDON_ONBOARDING_CHANNEL";
      isFunction = isOfType("function"), isNull = (value) => value === null, isRegex = (value) => Object.prototype.toString.call(value).slice(8, -1) === "RegExp", isObject = (value) => !isUndefined(value) && !isNull(value) && (isFunction(value) || typeof value == "object"), isUndefined = isOfType("undefined");
      objectTypes = ["Array", "ArrayBuffer", "AsyncFunction", "AsyncGenerator", "AsyncGeneratorFunction", "Date", "Error", "Function", "Generator", "GeneratorFunction", "HTMLElement", "Map", "Object", "Promise", "RegExp", "Set", "WeakMap", "WeakSet"], primitiveTypes = ["bigint", "boolean", "null", "number", "string", "symbol", "undefined"];
      DOM_PROPERTIES_TO_CHECK = ["innerHTML", "ownerDocument", "style", "attributes", "nodeValue"];
      is.array = Array.isArray;
      is.arrayOf = (target, predicate) => !is.array(target) && !is.function(predicate) ? !1 : target.every((d2) => predicate(d2));
      is.asyncGeneratorFunction = (value) => getObjectType(value) === "AsyncGeneratorFunction";
      is.asyncFunction = isObjectOfType("AsyncFunction");
      is.bigint = isOfType2("bigint");
      is.boolean = (value) => value === !0 || value === !1;
      is.date = isObjectOfType("Date");
      is.defined = (value) => !is.undefined(value);
      is.domElement = (value) => is.object(value) && !is.plainObject(value) && value.nodeType === 1 && is.string(value.nodeName) && DOM_PROPERTIES_TO_CHECK.every((property) => property in value);
      is.empty = (value) => is.string(value) && value.length === 0 || is.array(value) && value.length === 0 || is.object(value) && !is.map(value) && !is.set(value) && Object.keys(value).length === 0 || is.set(value) && value.size === 0 || is.map(value) && value.size === 0;
      is.error = isObjectOfType("Error");
      is.function = isOfType2("function");
      is.generator = (value) => is.iterable(value) && is.function(value.next) && is.function(value.throw);
      is.generatorFunction = isObjectOfType("GeneratorFunction");
      is.instanceOf = (instance, class_) => !instance || !class_ ? !1 : Object.getPrototypeOf(instance) === class_.prototype;
      is.iterable = (value) => !is.nullOrUndefined(value) && is.function(value[Symbol.iterator]);
      is.map = isObjectOfType("Map");
      is.nan = (value) => Number.isNaN(value);
      is.null = (value) => value === null;
      is.nullOrUndefined = (value) => is.null(value) || is.undefined(value);
      is.number = (value) => isOfType2("number")(value) && !is.nan(value);
      is.numericString = (value) => is.string(value) && value.length > 0 && !Number.isNaN(Number(value));
      is.object = (value) => !is.nullOrUndefined(value) && (is.function(value) || typeof value == "object");
      is.oneOf = (target, value) => is.array(target) ? target.indexOf(value) > -1 : !1;
      is.plainFunction = isObjectOfType("Function");
      is.plainObject = (value) => {
        if (getObjectType(value) !== "Object") return !1;
        let prototype = Object.getPrototypeOf(value);
        return prototype === null || prototype === Object.getPrototypeOf({});
      };
      is.primitive = (value) => is.null(value) || isPrimitiveType(typeof value);
      is.promise = isObjectOfType("Promise");
      is.propertyOf = (target, key, predicate) => {
        if (!is.object(target) || !key) return !1;
        let value = target[key];
        return is.function(predicate) ? predicate(value) : is.defined(value);
      };
      is.regexp = isObjectOfType("RegExp");
      is.set = isObjectOfType("Set");
      is.string = isOfType2("string");
      is.symbol = isOfType2("symbol");
      is.undefined = isOfType2("undefined");
      is.weakMap = isObjectOfType("WeakMap");
      is.weakSet = isObjectOfType("WeakSet");
      src_default = is;
      import_scroll = __toESM(require_scroll(), 1), import_scrollparent = __toESM(require_scrollparent(), 1), import_react_innertext = __toESM(require_react_innertext(), 1), import_deepmerge2 = __toESM(require_cjs(), 1), import_deepmerge3 = __toESM(require_cjs(), 1), import_prop_types = __toESM(require_prop_types()), isBrowser = typeof window < "u" && typeof document < "u" && typeof navigator < "u", timeoutDuration = function() {
        for (var longerTimeoutBrowsers = ["Edge", "Trident", "Firefox"], i2 = 0; i2 < longerTimeoutBrowsers.length; i2 += 1) if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i2]) >= 0) return 1;
        return 0;
      }();
      supportsMicroTasks = isBrowser && window.Promise, debounce = supportsMicroTasks ? microtaskDebounce : taskDebounce;
      isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode), isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);
      classCallCheck = function(instance, Constructor) {
        if (!(instance instanceof Constructor)) throw new TypeError("Cannot call a class as a function");
      }, createClass = /* @__PURE__ */ function() {
        function defineProperties(target, props) {
          for (var i2 = 0; i2 < props.length; i2++) {
            var descriptor = props[i2];
            descriptor.enumerable = descriptor.enumerable || !1, descriptor.configurable = !0, "value" in descriptor && (descriptor.writable = !0), Object.defineProperty(target, descriptor.key, descriptor);
          }
        }
        return function(Constructor, protoProps, staticProps) {
          return protoProps && defineProperties(Constructor.prototype, protoProps), staticProps && defineProperties(Constructor, staticProps), Constructor;
        };
      }(), defineProperty = function(obj, key, value) {
        return key in obj ? Object.defineProperty(obj, key, { value, enumerable: !0, configurable: !0, writable: !0 }) : obj[key] = value, obj;
      }, _extends = Object.assign || function(target) {
        for (var i2 = 1; i2 < arguments.length; i2++) {
          var source = arguments[i2];
          for (var key in source) Object.prototype.hasOwnProperty.call(source, key) && (target[key] = source[key]);
        }
        return target;
      };
      isFirefox = isBrowser && /Firefox/i.test(navigator.userAgent);
      placements = ["auto-start", "auto", "auto-end", "top-start", "top", "top-end", "right-start", "right", "right-end", "bottom-end", "bottom", "bottom-start", "left-end", "left", "left-start"], validPlacements = placements.slice(3);
      BEHAVIORS = { FLIP: "flip", CLOCKWISE: "clockwise", COUNTERCLOCKWISE: "counterclockwise" };
      modifiers = { shift: { order: 100, enabled: !0, fn: shift }, offset: { order: 200, enabled: !0, fn: offset, offset: 0 }, preventOverflow: { order: 300, enabled: !0, fn: preventOverflow, priority: ["left", "right", "top", "bottom"], padding: 5, boundariesElement: "scrollParent" }, keepTogether: { order: 400, enabled: !0, fn: keepTogether }, arrow: { order: 500, enabled: !0, fn: arrow, element: "[x-arrow]" }, flip: { order: 600, enabled: !0, fn: flip, behavior: "flip", padding: 5, boundariesElement: "viewport", flipVariations: !1, flipVariationsByContent: !1 }, inner: { order: 700, enabled: !1, fn: inner }, hide: { order: 800, enabled: !0, fn: hide }, computeStyle: { order: 850, enabled: !0, fn: computeStyle, gpuAcceleration: !0, x: "bottom", y: "right" }, applyStyle: { order: 900, enabled: !0, fn: applyStyle, onLoad: applyStyleOnLoad, gpuAcceleration: void 0 } }, Defaults = { placement: "bottom", positionFixed: !1, eventsEnabled: !0, removeOnDestroy: !1, onCreate: function() {
      }, onUpdate: function() {
      }, modifiers }, Popper = function() {
        function Popper2(reference, popper) {
          var _this = this, options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
          classCallCheck(this, Popper2), this.scheduleUpdate = function() {
            return requestAnimationFrame(_this.update);
          }, this.update = debounce(this.update.bind(this)), this.options = _extends({}, Popper2.Defaults, options), this.state = { isDestroyed: !1, isCreated: !1, scrollParents: [] }, this.reference = reference && reference.jquery ? reference[0] : reference, this.popper = popper && popper.jquery ? popper[0] : popper, this.options.modifiers = {}, Object.keys(_extends({}, Popper2.Defaults.modifiers, options.modifiers)).forEach(function(name) {
            _this.options.modifiers[name] = _extends({}, Popper2.Defaults.modifiers[name] || {}, options.modifiers ? options.modifiers[name] : {});
          }), this.modifiers = Object.keys(this.options.modifiers).map(function(name) {
            return _extends({ name }, _this.options.modifiers[name]);
          }).sort(function(a2, b2) {
            return a2.order - b2.order;
          }), this.modifiers.forEach(function(modifierOptions) {
            modifierOptions.enabled && isFunction2(modifierOptions.onLoad) && modifierOptions.onLoad(_this.reference, _this.popper, _this.options, modifierOptions, _this.state);
          }), this.update();
          var eventsEnabled = this.options.eventsEnabled;
          eventsEnabled && this.enableEventListeners(), this.state.eventsEnabled = eventsEnabled;
        }
        return createClass(Popper2, [{ key: "update", value: function() {
          return update.call(this);
        } }, { key: "destroy", value: function() {
          return destroy.call(this);
        } }, { key: "enableEventListeners", value: function() {
          return enableEventListeners.call(this);
        } }, { key: "disableEventListeners", value: function() {
          return disableEventListeners.call(this);
        } }]), Popper2;
      }();
      Popper.Utils = window.PopperUtils;
      Popper.placements = placements;
      Popper.Defaults = Defaults;
      popper_default = Popper, import_deepmerge = __toESM(require_cjs()), DOM_PROPERTIES_TO_CHECK2 = ["innerHTML", "ownerDocument", "style", "attributes", "nodeValue"], objectTypes2 = ["Array", "ArrayBuffer", "AsyncFunction", "AsyncGenerator", "AsyncGeneratorFunction", "Date", "Error", "Function", "Generator", "GeneratorFunction", "HTMLElement", "Map", "Object", "Promise", "RegExp", "Set", "WeakMap", "WeakSet"], primitiveTypes2 = ["bigint", "boolean", "null", "number", "string", "symbol", "undefined"];
      is2.array = Array.isArray;
      is2.arrayOf = function(target, predicate) {
        return !is2.array(target) && !is2.function(predicate) ? !1 : target.every(function(d2) {
          return predicate(d2);
        });
      };
      is2.asyncGeneratorFunction = function(value) {
        return getObjectType2(value) === "AsyncGeneratorFunction";
      };
      is2.asyncFunction = isObjectOfType2("AsyncFunction");
      is2.bigint = isOfType3("bigint");
      is2.boolean = function(value) {
        return value === !0 || value === !1;
      };
      is2.date = isObjectOfType2("Date");
      is2.defined = function(value) {
        return !is2.undefined(value);
      };
      is2.domElement = function(value) {
        return is2.object(value) && !is2.plainObject(value) && value.nodeType === 1 && is2.string(value.nodeName) && DOM_PROPERTIES_TO_CHECK2.every(function(property) {
          return property in value;
        });
      };
      is2.empty = function(value) {
        return is2.string(value) && value.length === 0 || is2.array(value) && value.length === 0 || is2.object(value) && !is2.map(value) && !is2.set(value) && Object.keys(value).length === 0 || is2.set(value) && value.size === 0 || is2.map(value) && value.size === 0;
      };
      is2.error = isObjectOfType2("Error");
      is2.function = isOfType3("function");
      is2.generator = function(value) {
        return is2.iterable(value) && is2.function(value.next) && is2.function(value.throw);
      };
      is2.generatorFunction = isObjectOfType2("GeneratorFunction");
      is2.instanceOf = function(instance, class_) {
        return !instance || !class_ ? !1 : Object.getPrototypeOf(instance) === class_.prototype;
      };
      is2.iterable = function(value) {
        return !is2.nullOrUndefined(value) && is2.function(value[Symbol.iterator]);
      };
      is2.map = isObjectOfType2("Map");
      is2.nan = function(value) {
        return Number.isNaN(value);
      };
      is2.null = function(value) {
        return value === null;
      };
      is2.nullOrUndefined = function(value) {
        return is2.null(value) || is2.undefined(value);
      };
      is2.number = function(value) {
        return isOfType3("number")(value) && !is2.nan(value);
      };
      is2.numericString = function(value) {
        return is2.string(value) && value.length > 0 && !Number.isNaN(Number(value));
      };
      is2.object = function(value) {
        return !is2.nullOrUndefined(value) && (is2.function(value) || typeof value == "object");
      };
      is2.oneOf = function(target, value) {
        return is2.array(target) ? target.indexOf(value) > -1 : !1;
      };
      is2.plainFunction = isObjectOfType2("Function");
      is2.plainObject = function(value) {
        if (getObjectType2(value) !== "Object") return !1;
        var prototype = Object.getPrototypeOf(value);
        return prototype === null || prototype === Object.getPrototypeOf({});
      };
      is2.primitive = function(value) {
        return is2.null(value) || isPrimitiveType2(typeof value);
      };
      is2.promise = isObjectOfType2("Promise");
      is2.propertyOf = function(target, key, predicate) {
        if (!is2.object(target) || !key) return !1;
        var value = target[key];
        return is2.function(predicate) ? predicate(value) : is2.defined(value);
      };
      is2.regexp = isObjectOfType2("RegExp");
      is2.set = isObjectOfType2("Set");
      is2.string = isOfType3("string");
      is2.symbol = isOfType3("symbol");
      is2.undefined = isOfType3("undefined");
      is2.weakMap = isObjectOfType2("WeakMap");
      is2.weakSet = isObjectOfType2("WeakSet");
      esm_default = is2;
      isFunction3 = isOfType4("function"), isNull2 = function(value) {
        return value === null;
      }, isRegex2 = function(value) {
        return Object.prototype.toString.call(value).slice(8, -1) === "RegExp";
      }, isObject2 = function(value) {
        return !isUndefined2(value) && !isNull2(value) && (isFunction3(value) || typeof value == "object");
      }, isUndefined2 = isOfType4("undefined"), __values = function(o22) {
        var s2 = typeof Symbol == "function" && Symbol.iterator, m2 = s2 && o22[s2], i2 = 0;
        if (m2) return m2.call(o22);
        if (o22 && typeof o22.length == "number") return { next: function() {
          return o22 && i2 >= o22.length && (o22 = void 0), { value: o22 && o22[i2++], done: !o22 };
        } };
        throw new TypeError(s2 ? "Object is not iterable." : "Symbol.iterator is not defined.");
      };
      DOM_PROPERTIES_TO_CHECK3 = ["innerHTML", "ownerDocument", "style", "attributes", "nodeValue"], objectTypes3 = ["Array", "ArrayBuffer", "AsyncFunction", "AsyncGenerator", "AsyncGeneratorFunction", "Date", "Error", "Function", "Generator", "GeneratorFunction", "HTMLElement", "Map", "Object", "Promise", "RegExp", "Set", "WeakMap", "WeakSet"], primitiveTypes3 = ["bigint", "boolean", "null", "number", "string", "symbol", "undefined"];
      is3.array = Array.isArray;
      is3.arrayOf = function(target, predicate) {
        return !is3.array(target) && !is3.function(predicate) ? !1 : target.every(function(d2) {
          return predicate(d2);
        });
      };
      is3.asyncGeneratorFunction = function(value) {
        return getObjectType3(value) === "AsyncGeneratorFunction";
      };
      is3.asyncFunction = isObjectOfType3("AsyncFunction");
      is3.bigint = isOfType5("bigint");
      is3.boolean = function(value) {
        return value === !0 || value === !1;
      };
      is3.date = isObjectOfType3("Date");
      is3.defined = function(value) {
        return !is3.undefined(value);
      };
      is3.domElement = function(value) {
        return is3.object(value) && !is3.plainObject(value) && value.nodeType === 1 && is3.string(value.nodeName) && DOM_PROPERTIES_TO_CHECK3.every(function(property) {
          return property in value;
        });
      };
      is3.empty = function(value) {
        return is3.string(value) && value.length === 0 || is3.array(value) && value.length === 0 || is3.object(value) && !is3.map(value) && !is3.set(value) && Object.keys(value).length === 0 || is3.set(value) && value.size === 0 || is3.map(value) && value.size === 0;
      };
      is3.error = isObjectOfType3("Error");
      is3.function = isOfType5("function");
      is3.generator = function(value) {
        return is3.iterable(value) && is3.function(value.next) && is3.function(value.throw);
      };
      is3.generatorFunction = isObjectOfType3("GeneratorFunction");
      is3.instanceOf = function(instance, class_) {
        return !instance || !class_ ? !1 : Object.getPrototypeOf(instance) === class_.prototype;
      };
      is3.iterable = function(value) {
        return !is3.nullOrUndefined(value) && is3.function(value[Symbol.iterator]);
      };
      is3.map = isObjectOfType3("Map");
      is3.nan = function(value) {
        return Number.isNaN(value);
      };
      is3.null = function(value) {
        return value === null;
      };
      is3.nullOrUndefined = function(value) {
        return is3.null(value) || is3.undefined(value);
      };
      is3.number = function(value) {
        return isOfType5("number")(value) && !is3.nan(value);
      };
      is3.numericString = function(value) {
        return is3.string(value) && value.length > 0 && !Number.isNaN(Number(value));
      };
      is3.object = function(value) {
        return !is3.nullOrUndefined(value) && (is3.function(value) || typeof value == "object");
      };
      is3.oneOf = function(target, value) {
        return is3.array(target) ? target.indexOf(value) > -1 : !1;
      };
      is3.plainFunction = isObjectOfType3("Function");
      is3.plainObject = function(value) {
        if (getObjectType3(value) !== "Object") return !1;
        var prototype = Object.getPrototypeOf(value);
        return prototype === null || prototype === Object.getPrototypeOf({});
      };
      is3.primitive = function(value) {
        return is3.null(value) || isPrimitiveType3(typeof value);
      };
      is3.promise = isObjectOfType3("Promise");
      is3.propertyOf = function(target, key, predicate) {
        if (!is3.object(target) || !key) return !1;
        var value = target[key];
        return is3.function(predicate) ? predicate(value) : is3.defined(value);
      };
      is3.regexp = isObjectOfType3("RegExp");
      is3.set = isObjectOfType3("Set");
      is3.string = isOfType5("string");
      is3.symbol = isOfType5("symbol");
      is3.undefined = isOfType5("undefined");
      is3.weakMap = isObjectOfType3("WeakMap");
      is3.weakSet = isObjectOfType3("WeakSet");
      esm_default2 = is3;
      DEFAULTS = { flip: { padding: 20 }, preventOverflow: { padding: 10 } }, VALIDATOR_ARG_ERROR_MESSAGE = "The typeValidator argument must be a function with the signature function(props, propName, componentName).", MESSAGE_ARG_ERROR_MESSAGE = "The error message is optional, but must be a string if provided.";
      STATUS = { INIT: "init", IDLE: "idle", OPENING: "opening", OPEN: "open", CLOSING: "closing", ERROR: "error" }, isReact16 = react_dom_default.createPortal !== void 0;
      ReactFloaterPortal = function(_React$Component) {
        _inherits(ReactFloaterPortal2, _React$Component);
        var _super = _createSuper(ReactFloaterPortal2);
        function ReactFloaterPortal2() {
          return _classCallCheck(this, ReactFloaterPortal2), _super.apply(this, arguments);
        }
        return _createClass(ReactFloaterPortal2, [{ key: "componentDidMount", value: function() {
          canUseDOM() && (this.node || this.appendNode(), isReact16 || this.renderPortal());
        } }, { key: "componentDidUpdate", value: function() {
          canUseDOM() && (isReact16 || this.renderPortal());
        } }, { key: "componentWillUnmount", value: function() {
          !canUseDOM() || !this.node || (isReact16 || react_dom_default.unmountComponentAtNode(this.node), this.node && this.node.parentNode === document.body && (document.body.removeChild(this.node), this.node = void 0));
        } }, { key: "appendNode", value: function() {
          var _this$props = this.props, id = _this$props.id, zIndex = _this$props.zIndex;
          this.node || (this.node = document.createElement("div"), id && (this.node.id = id), zIndex && (this.node.style.zIndex = zIndex), document.body.appendChild(this.node));
        } }, { key: "renderPortal", value: function() {
          if (!canUseDOM()) return null;
          var _this$props2 = this.props, children = _this$props2.children, setRef = _this$props2.setRef;
          if (this.node || this.appendNode(), isReact16) return react_dom_default.createPortal(children, this.node);
          var portal = react_dom_default.unstable_renderSubtreeIntoContainer(this, children.length > 1 ? react_default.createElement("div", null, children) : children[0], this.node);
          return setRef(portal), null;
        } }, { key: "renderReact16", value: function() {
          var _this$props3 = this.props, hasChildren = _this$props3.hasChildren, placement = _this$props3.placement, target = _this$props3.target;
          return hasChildren ? this.renderPortal() : target || placement === "center" ? this.renderPortal() : null;
        } }, { key: "render", value: function() {
          return isReact16 ? this.renderReact16() : null;
        } }]), ReactFloaterPortal2;
      }(react_default.Component);
      _defineProperty(ReactFloaterPortal, "propTypes", { children: import_prop_types.default.oneOfType([import_prop_types.default.element, import_prop_types.default.array]), hasChildren: import_prop_types.default.bool, id: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number]), placement: import_prop_types.default.string, setRef: import_prop_types.default.func.isRequired, target: import_prop_types.default.oneOfType([import_prop_types.default.object, import_prop_types.default.string]), zIndex: import_prop_types.default.number });
      FloaterArrow = function(_React$Component) {
        _inherits(FloaterArrow2, _React$Component);
        var _super = _createSuper(FloaterArrow2);
        function FloaterArrow2() {
          return _classCallCheck(this, FloaterArrow2), _super.apply(this, arguments);
        }
        return _createClass(FloaterArrow2, [{ key: "parentStyle", get: function() {
          var _this$props = this.props, placement = _this$props.placement, styles = _this$props.styles, length = styles.arrow.length, arrow2 = { pointerEvents: "none", position: "absolute", width: "100%" };
          return placement.startsWith("top") ? (arrow2.bottom = 0, arrow2.left = 0, arrow2.right = 0, arrow2.height = length) : placement.startsWith("bottom") ? (arrow2.left = 0, arrow2.right = 0, arrow2.top = 0, arrow2.height = length) : placement.startsWith("left") ? (arrow2.right = 0, arrow2.top = 0, arrow2.bottom = 0) : placement.startsWith("right") && (arrow2.left = 0, arrow2.top = 0), arrow2;
        } }, { key: "render", value: function() {
          var _this$props2 = this.props, placement = _this$props2.placement, setArrowRef = _this$props2.setArrowRef, styles = _this$props2.styles, _styles$arrow = styles.arrow, color2 = _styles$arrow.color, display = _styles$arrow.display, length = _styles$arrow.length, margin = _styles$arrow.margin, position = _styles$arrow.position, spread = _styles$arrow.spread, arrowStyles = { display, position }, points, x2 = spread, y2 = length;
          return placement.startsWith("top") ? (points = "0,0 ".concat(x2 / 2, ",").concat(y2, " ").concat(x2, ",0"), arrowStyles.bottom = 0, arrowStyles.marginLeft = margin, arrowStyles.marginRight = margin) : placement.startsWith("bottom") ? (points = "".concat(x2, ",").concat(y2, " ").concat(x2 / 2, ",0 0,").concat(y2), arrowStyles.top = 0, arrowStyles.marginLeft = margin, arrowStyles.marginRight = margin) : placement.startsWith("left") ? (y2 = spread, x2 = length, points = "0,0 ".concat(x2, ",").concat(y2 / 2, " 0,").concat(y2), arrowStyles.right = 0, arrowStyles.marginTop = margin, arrowStyles.marginBottom = margin) : placement.startsWith("right") && (y2 = spread, x2 = length, points = "".concat(x2, ",").concat(y2, " ").concat(x2, ",0 0,").concat(y2 / 2), arrowStyles.left = 0, arrowStyles.marginTop = margin, arrowStyles.marginBottom = margin), react_default.createElement("div", { className: "__floater__arrow", style: this.parentStyle }, react_default.createElement("span", { ref: setArrowRef, style: arrowStyles }, react_default.createElement("svg", { width: x2, height: y2, version: "1.1", xmlns: "http://www.w3.org/2000/svg" }, react_default.createElement("polygon", { points, fill: color2 }))));
        } }]), FloaterArrow2;
      }(react_default.Component);
      _defineProperty(FloaterArrow, "propTypes", { placement: import_prop_types.default.string.isRequired, setArrowRef: import_prop_types.default.func.isRequired, styles: import_prop_types.default.object.isRequired });
      _excluded$1 = ["color", "height", "width"];
      FloaterCloseBtn.propTypes = { handleClick: import_prop_types.default.func.isRequired, styles: import_prop_types.default.object.isRequired };
      FloaterContainer.propTypes = { content: import_prop_types.default.node.isRequired, footer: import_prop_types.default.node, handleClick: import_prop_types.default.func.isRequired, open: import_prop_types.default.bool, positionWrapper: import_prop_types.default.bool.isRequired, showCloseButton: import_prop_types.default.bool.isRequired, styles: import_prop_types.default.object.isRequired, title: import_prop_types.default.node };
      Floater = function(_React$Component) {
        _inherits(Floater2, _React$Component);
        var _super = _createSuper(Floater2);
        function Floater2() {
          return _classCallCheck(this, Floater2), _super.apply(this, arguments);
        }
        return _createClass(Floater2, [{ key: "style", get: function() {
          var _this$props = this.props, disableAnimation = _this$props.disableAnimation, component = _this$props.component, placement = _this$props.placement, hideArrow = _this$props.hideArrow, status = _this$props.status, styles = _this$props.styles, length = styles.arrow.length, floater = styles.floater, floaterCentered = styles.floaterCentered, floaterClosing = styles.floaterClosing, floaterOpening = styles.floaterOpening, floaterWithAnimation = styles.floaterWithAnimation, floaterWithComponent = styles.floaterWithComponent, element = {};
          return hideArrow || (placement.startsWith("top") ? element.padding = "0 0 ".concat(length, "px") : placement.startsWith("bottom") ? element.padding = "".concat(length, "px 0 0") : placement.startsWith("left") ? element.padding = "0 ".concat(length, "px 0 0") : placement.startsWith("right") && (element.padding = "0 0 0 ".concat(length, "px"))), [STATUS.OPENING, STATUS.OPEN].indexOf(status) !== -1 && (element = _objectSpread2(_objectSpread2({}, element), floaterOpening)), status === STATUS.CLOSING && (element = _objectSpread2(_objectSpread2({}, element), floaterClosing)), status === STATUS.OPEN && !disableAnimation && (element = _objectSpread2(_objectSpread2({}, element), floaterWithAnimation)), placement === "center" && (element = _objectSpread2(_objectSpread2({}, element), floaterCentered)), component && (element = _objectSpread2(_objectSpread2({}, element), floaterWithComponent)), _objectSpread2(_objectSpread2({}, floater), element);
        } }, { key: "render", value: function() {
          var _this$props2 = this.props, component = _this$props2.component, closeFn = _this$props2.handleClick, hideArrow = _this$props2.hideArrow, setFloaterRef = _this$props2.setFloaterRef, status = _this$props2.status, output = {}, classes = ["__floater"];
          return component ? react_default.isValidElement(component) ? output.content = react_default.cloneElement(component, { closeFn }) : output.content = component({ closeFn }) : output.content = react_default.createElement(FloaterContainer, this.props), status === STATUS.OPEN && classes.push("__floater__open"), hideArrow || (output.arrow = react_default.createElement(FloaterArrow, this.props)), react_default.createElement("div", { ref: setFloaterRef, className: classes.join(" "), style: this.style }, react_default.createElement("div", { className: "__floater__body" }, output.content, output.arrow));
        } }]), Floater2;
      }(react_default.Component);
      _defineProperty(Floater, "propTypes", { component: import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.element]), content: import_prop_types.default.node, disableAnimation: import_prop_types.default.bool.isRequired, footer: import_prop_types.default.node, handleClick: import_prop_types.default.func.isRequired, hideArrow: import_prop_types.default.bool.isRequired, open: import_prop_types.default.bool, placement: import_prop_types.default.string.isRequired, positionWrapper: import_prop_types.default.bool.isRequired, setArrowRef: import_prop_types.default.func.isRequired, setFloaterRef: import_prop_types.default.func.isRequired, showCloseButton: import_prop_types.default.bool, status: import_prop_types.default.string.isRequired, styles: import_prop_types.default.object.isRequired, title: import_prop_types.default.node });
      ReactFloaterWrapper = function(_React$Component) {
        _inherits(ReactFloaterWrapper2, _React$Component);
        var _super = _createSuper(ReactFloaterWrapper2);
        function ReactFloaterWrapper2() {
          return _classCallCheck(this, ReactFloaterWrapper2), _super.apply(this, arguments);
        }
        return _createClass(ReactFloaterWrapper2, [{ key: "render", value: function() {
          var _this$props = this.props, children = _this$props.children, handleClick = _this$props.handleClick, handleMouseEnter = _this$props.handleMouseEnter, handleMouseLeave = _this$props.handleMouseLeave, setChildRef = _this$props.setChildRef, setWrapperRef = _this$props.setWrapperRef, style = _this$props.style, styles = _this$props.styles, element;
          if (children) if (react_default.Children.count(children) === 1) if (!react_default.isValidElement(children)) element = react_default.createElement("span", null, children);
          else {
            var refProp = esm_default.function(children.type) ? "innerRef" : "ref";
            element = react_default.cloneElement(react_default.Children.only(children), _defineProperty({}, refProp, setChildRef));
          }
          else element = children;
          return element ? react_default.createElement("span", { ref: setWrapperRef, style: _objectSpread2(_objectSpread2({}, styles), style), onClick: handleClick, onMouseEnter: handleMouseEnter, onMouseLeave: handleMouseLeave }, element) : null;
        } }]), ReactFloaterWrapper2;
      }(react_default.Component);
      _defineProperty(ReactFloaterWrapper, "propTypes", { children: import_prop_types.default.node, handleClick: import_prop_types.default.func.isRequired, handleMouseEnter: import_prop_types.default.func.isRequired, handleMouseLeave: import_prop_types.default.func.isRequired, setChildRef: import_prop_types.default.func.isRequired, setWrapperRef: import_prop_types.default.func.isRequired, style: import_prop_types.default.object, styles: import_prop_types.default.object.isRequired });
      defaultOptions = { zIndex: 100 };
      _excluded = ["arrow", "flip", "offset"], POSITIONING_PROPS = ["position", "top", "right", "bottom", "left"], ReactFloater = function(_React$Component) {
        _inherits(ReactFloater2, _React$Component);
        var _super = _createSuper(ReactFloater2);
        function ReactFloater2(props) {
          var _this;
          return _classCallCheck(this, ReactFloater2), _this = _super.call(this, props), _defineProperty(_assertThisInitialized(_this), "setArrowRef", function(ref) {
            _this.arrowRef = ref;
          }), _defineProperty(_assertThisInitialized(_this), "setChildRef", function(ref) {
            _this.childRef = ref;
          }), _defineProperty(_assertThisInitialized(_this), "setFloaterRef", function(ref) {
            _this.floaterRef = ref;
          }), _defineProperty(_assertThisInitialized(_this), "setWrapperRef", function(ref) {
            _this.wrapperRef = ref;
          }), _defineProperty(_assertThisInitialized(_this), "handleTransitionEnd", function() {
            var status = _this.state.status, callback = _this.props.callback;
            _this.wrapperPopper && _this.wrapperPopper.instance.update(), _this.setState({ status: status === STATUS.OPENING ? STATUS.OPEN : STATUS.IDLE }, function() {
              var newStatus = _this.state.status;
              callback(newStatus === STATUS.OPEN ? "open" : "close", _this.props);
            });
          }), _defineProperty(_assertThisInitialized(_this), "handleClick", function() {
            var _this$props = _this.props, event = _this$props.event, open = _this$props.open;
            if (!esm_default.boolean(open)) {
              var _this$state = _this.state, positionWrapper = _this$state.positionWrapper, status = _this$state.status;
              (_this.event === "click" || _this.event === "hover" && positionWrapper) && (log({ title: "click", data: [{ event, status: status === STATUS.OPEN ? "closing" : "opening" }], debug: _this.debug }), _this.toggle());
            }
          }), _defineProperty(_assertThisInitialized(_this), "handleMouseEnter", function() {
            var _this$props2 = _this.props, event = _this$props2.event, open = _this$props2.open;
            if (!(esm_default.boolean(open) || isMobile())) {
              var status = _this.state.status;
              _this.event === "hover" && status === STATUS.IDLE && (log({ title: "mouseEnter", data: [{ key: "originalEvent", value: event }], debug: _this.debug }), clearTimeout(_this.eventDelayTimeout), _this.toggle());
            }
          }), _defineProperty(_assertThisInitialized(_this), "handleMouseLeave", function() {
            var _this$props3 = _this.props, event = _this$props3.event, eventDelay = _this$props3.eventDelay, open = _this$props3.open;
            if (!(esm_default.boolean(open) || isMobile())) {
              var _this$state2 = _this.state, status = _this$state2.status, positionWrapper = _this$state2.positionWrapper;
              _this.event === "hover" && (log({ title: "mouseLeave", data: [{ key: "originalEvent", value: event }], debug: _this.debug }), eventDelay ? [STATUS.OPENING, STATUS.OPEN].indexOf(status) !== -1 && !positionWrapper && !_this.eventDelayTimeout && (_this.eventDelayTimeout = setTimeout(function() {
                delete _this.eventDelayTimeout, _this.toggle();
              }, eventDelay * 1e3)) : _this.toggle(STATUS.IDLE));
            }
          }), _this.state = { currentPlacement: props.placement, needsUpdate: !1, positionWrapper: props.wrapperOptions.position && !!props.target, status: STATUS.INIT, statusWrapper: STATUS.INIT }, _this._isMounted = !1, _this.hasMounted = !1, canUseDOM() && window.addEventListener("load", function() {
            _this.popper && _this.popper.instance.update(), _this.wrapperPopper && _this.wrapperPopper.instance.update();
          }), _this;
        }
        return _createClass(ReactFloater2, [{ key: "componentDidMount", value: function() {
          if (canUseDOM()) {
            var positionWrapper = this.state.positionWrapper, _this$props5 = this.props, children = _this$props5.children, open = _this$props5.open, target = _this$props5.target;
            this._isMounted = !0, log({ title: "init", data: { hasChildren: !!children, hasTarget: !!target, isControlled: esm_default.boolean(open), positionWrapper, target: this.target, floater: this.floaterRef }, debug: this.debug }), this.hasMounted || (this.initPopper(), this.hasMounted = !0), !children && target && esm_default.boolean(open);
          }
        } }, { key: "componentDidUpdate", value: function(prevProps, prevState) {
          if (canUseDOM()) {
            var _this$props6 = this.props, autoOpen = _this$props6.autoOpen, open = _this$props6.open, target = _this$props6.target, wrapperOptions = _this$props6.wrapperOptions, _treeChanges = treeChanges2(prevState, this.state), changedFrom = _treeChanges.changedFrom, changed = _treeChanges.changed;
            if (prevProps.open !== open) {
              var forceStatus;
              esm_default.boolean(open) && (forceStatus = open ? STATUS.OPENING : STATUS.CLOSING), this.toggle(forceStatus);
            }
            (prevProps.wrapperOptions.position !== wrapperOptions.position || prevProps.target !== target) && this.changeWrapperPosition(this.props), changed("status", STATUS.IDLE) && open ? this.toggle(STATUS.OPEN) : changedFrom("status", STATUS.INIT, STATUS.IDLE) && autoOpen && this.toggle(STATUS.OPEN), this.popper && changed("status", STATUS.OPENING) && this.popper.instance.update(), this.floaterRef && (changed("status", STATUS.OPENING) || changed("status", STATUS.CLOSING)) && once(this.floaterRef, "transitionend", this.handleTransitionEnd), changed("needsUpdate", !0) && this.rebuildPopper();
          }
        } }, { key: "componentWillUnmount", value: function() {
          canUseDOM() && (this._isMounted = !1, this.popper && this.popper.instance.destroy(), this.wrapperPopper && this.wrapperPopper.instance.destroy());
        } }, { key: "initPopper", value: function() {
          var _this2 = this, target = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : this.target, positionWrapper = this.state.positionWrapper, _this$props7 = this.props, disableFlip = _this$props7.disableFlip, getPopper = _this$props7.getPopper, hideArrow = _this$props7.hideArrow, offset2 = _this$props7.offset, placement = _this$props7.placement, wrapperOptions = _this$props7.wrapperOptions, flipBehavior = placement === "top" || placement === "bottom" ? "flip" : ["right", "bottom-end", "top-end", "left", "top-start", "bottom-start"];
          if (placement === "center") this.setState({ status: STATUS.IDLE });
          else if (target && this.floaterRef) {
            var _this$options = this.options, arrow2 = _this$options.arrow, flip2 = _this$options.flip, offsetOptions = _this$options.offset, rest = _objectWithoutProperties(_this$options, _excluded);
            new popper_default(target, this.floaterRef, { placement, modifiers: _objectSpread2({ arrow: _objectSpread2({ enabled: !hideArrow, element: this.arrowRef }, arrow2), flip: _objectSpread2({ enabled: !disableFlip, behavior: flipBehavior }, flip2), offset: _objectSpread2({ offset: "0, ".concat(offset2, "px") }, offsetOptions) }, rest), onCreate: function(data) {
              var _this2$floaterRef;
              if (_this2.popper = data, !((_this2$floaterRef = _this2.floaterRef) !== null && _this2$floaterRef !== void 0 && _this2$floaterRef.isConnected)) {
                _this2.setState({ needsUpdate: !0 });
                return;
              }
              getPopper(data, "floater"), _this2._isMounted && _this2.setState({ currentPlacement: data.placement, status: STATUS.IDLE }), placement !== data.placement && setTimeout(function() {
                data.instance.update();
              }, 1);
            }, onUpdate: function(data) {
              _this2.popper = data;
              var currentPlacement = _this2.state.currentPlacement;
              _this2._isMounted && data.placement !== currentPlacement && _this2.setState({ currentPlacement: data.placement });
            } });
          }
          if (positionWrapper) {
            var wrapperOffset = esm_default.undefined(wrapperOptions.offset) ? 0 : wrapperOptions.offset;
            new popper_default(this.target, this.wrapperRef, { placement: wrapperOptions.placement || placement, modifiers: { arrow: { enabled: !1 }, offset: { offset: "0, ".concat(wrapperOffset, "px") }, flip: { enabled: !1 } }, onCreate: function(data) {
              _this2.wrapperPopper = data, _this2._isMounted && _this2.setState({ statusWrapper: STATUS.IDLE }), getPopper(data, "wrapper"), placement !== data.placement && setTimeout(function() {
                data.instance.update();
              }, 1);
            } });
          }
        } }, { key: "rebuildPopper", value: function() {
          var _this3 = this;
          this.floaterRefInterval = setInterval(function() {
            var _this3$floaterRef;
            (_this3$floaterRef = _this3.floaterRef) !== null && _this3$floaterRef !== void 0 && _this3$floaterRef.isConnected && (clearInterval(_this3.floaterRefInterval), _this3.setState({ needsUpdate: !1 }), _this3.initPopper());
          }, 50);
        } }, { key: "changeWrapperPosition", value: function(_ref) {
          var target = _ref.target, wrapperOptions = _ref.wrapperOptions;
          this.setState({ positionWrapper: wrapperOptions.position && !!target });
        } }, { key: "toggle", value: function(forceStatus) {
          var status = this.state.status, nextStatus = status === STATUS.OPEN ? STATUS.CLOSING : STATUS.OPENING;
          esm_default.undefined(forceStatus) || (nextStatus = forceStatus), this.setState({ status: nextStatus });
        } }, { key: "debug", get: function() {
          var debug = this.props.debug;
          return debug || canUseDOM() && "ReactFloaterDebug" in window && !!window.ReactFloaterDebug;
        } }, { key: "event", get: function() {
          var _this$props8 = this.props, disableHoverToClick = _this$props8.disableHoverToClick, event = _this$props8.event;
          return event === "hover" && isMobile() && !disableHoverToClick ? "click" : event;
        } }, { key: "options", get: function() {
          var options = this.props.options;
          return (0, import_deepmerge.default)(DEFAULTS, options || {});
        } }, { key: "styles", get: function() {
          var _this4 = this, _this$state3 = this.state, status = _this$state3.status, positionWrapper = _this$state3.positionWrapper, statusWrapper = _this$state3.statusWrapper, styles = this.props.styles, nextStyles = (0, import_deepmerge.default)(getStyles(styles), styles);
          if (positionWrapper) {
            var wrapperStyles;
            [STATUS.IDLE].indexOf(status) === -1 || [STATUS.IDLE].indexOf(statusWrapper) === -1 ? wrapperStyles = nextStyles.wrapperPosition : wrapperStyles = this.wrapperPopper.styles, nextStyles.wrapper = _objectSpread2(_objectSpread2({}, nextStyles.wrapper), wrapperStyles);
          }
          if (this.target) {
            var targetStyles = window.getComputedStyle(this.target);
            this.wrapperStyles ? nextStyles.wrapper = _objectSpread2(_objectSpread2({}, nextStyles.wrapper), this.wrapperStyles) : ["relative", "static"].indexOf(targetStyles.position) === -1 && (this.wrapperStyles = {}, positionWrapper || (POSITIONING_PROPS.forEach(function(d2) {
              _this4.wrapperStyles[d2] = targetStyles[d2];
            }), nextStyles.wrapper = _objectSpread2(_objectSpread2({}, nextStyles.wrapper), this.wrapperStyles), this.target.style.position = "relative", this.target.style.top = "auto", this.target.style.right = "auto", this.target.style.bottom = "auto", this.target.style.left = "auto"));
          }
          return nextStyles;
        } }, { key: "target", get: function() {
          if (!canUseDOM()) return null;
          var target = this.props.target;
          return target ? esm_default.domElement(target) ? target : document.querySelector(target) : this.childRef || this.wrapperRef;
        } }, { key: "render", value: function() {
          var _this$state4 = this.state, currentPlacement = _this$state4.currentPlacement, positionWrapper = _this$state4.positionWrapper, status = _this$state4.status, _this$props9 = this.props, children = _this$props9.children, component = _this$props9.component, content = _this$props9.content, disableAnimation = _this$props9.disableAnimation, footer = _this$props9.footer, hideArrow = _this$props9.hideArrow, id = _this$props9.id, open = _this$props9.open, showCloseButton = _this$props9.showCloseButton, style = _this$props9.style, target = _this$props9.target, title = _this$props9.title, wrapper = react_default.createElement(ReactFloaterWrapper, { handleClick: this.handleClick, handleMouseEnter: this.handleMouseEnter, handleMouseLeave: this.handleMouseLeave, setChildRef: this.setChildRef, setWrapperRef: this.setWrapperRef, style, styles: this.styles.wrapper }, children), output = {};
          return positionWrapper ? output.wrapperInPortal = wrapper : output.wrapperAsChildren = wrapper, react_default.createElement("span", null, react_default.createElement(ReactFloaterPortal, { hasChildren: !!children, id, placement: currentPlacement, setRef: this.setFloaterRef, target, zIndex: this.styles.options.zIndex }, react_default.createElement(Floater, { component, content, disableAnimation, footer, handleClick: this.handleClick, hideArrow: hideArrow || currentPlacement === "center", open, placement: currentPlacement, positionWrapper, setArrowRef: this.setArrowRef, setFloaterRef: this.setFloaterRef, showCloseButton, status, styles: this.styles, title }), output.wrapperInPortal), output.wrapperAsChildren);
        } }]), ReactFloater2;
      }(react_default.Component);
      _defineProperty(ReactFloater, "propTypes", { autoOpen: import_prop_types.default.bool, callback: import_prop_types.default.func, children: import_prop_types.default.node, component: isRequiredIf(import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.element]), function(props) {
        return !props.content;
      }), content: isRequiredIf(import_prop_types.default.node, function(props) {
        return !props.component;
      }), debug: import_prop_types.default.bool, disableAnimation: import_prop_types.default.bool, disableFlip: import_prop_types.default.bool, disableHoverToClick: import_prop_types.default.bool, event: import_prop_types.default.oneOf(["hover", "click"]), eventDelay: import_prop_types.default.number, footer: import_prop_types.default.node, getPopper: import_prop_types.default.func, hideArrow: import_prop_types.default.bool, id: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number]), offset: import_prop_types.default.number, open: import_prop_types.default.bool, options: import_prop_types.default.object, placement: import_prop_types.default.oneOf(["top", "top-start", "top-end", "bottom", "bottom-start", "bottom-end", "left", "left-start", "left-end", "right", "right-start", "right-end", "auto", "center"]), showCloseButton: import_prop_types.default.bool, style: import_prop_types.default.object, styles: import_prop_types.default.object, target: import_prop_types.default.oneOfType([import_prop_types.default.object, import_prop_types.default.string]), title: import_prop_types.default.node, wrapperOptions: import_prop_types.default.shape({ offset: import_prop_types.default.number, placement: import_prop_types.default.oneOf(["top", "top-start", "top-end", "bottom", "bottom-start", "bottom-end", "left", "left-start", "left-end", "right", "right-start", "right-end", "auto"]), position: import_prop_types.default.bool }) });
      _defineProperty(ReactFloater, "defaultProps", { autoOpen: !1, callback: noop, debug: !1, disableAnimation: !1, disableFlip: !1, disableHoverToClick: !1, event: "click", eventDelay: 0.4, getPopper: noop, hideArrow: !1, offset: 15, placement: "bottom", showCloseButton: !1, styles: {}, target: null, wrapperOptions: { position: !1 } });
      __defProp22 = Object.defineProperty, __defNormalProp = (obj, key, value) => key in obj ? __defProp22(obj, key, { enumerable: !0, configurable: !0, writable: !0, value }) : obj[key] = value, __publicField = (obj, key, value) => __defNormalProp(obj, typeof key != "symbol" ? key + "" : key, value), ACTIONS = { INIT: "init", START: "start", STOP: "stop", RESET: "reset", PREV: "prev", NEXT: "next", GO: "go", CLOSE: "close", SKIP: "skip", UPDATE: "update" }, EVENTS = { TOUR_START: "tour:start", STEP_BEFORE: "step:before", BEACON: "beacon", TOOLTIP: "tooltip", STEP_AFTER: "step:after", TOUR_END: "tour:end", TOUR_STATUS: "tour:status", TARGET_NOT_FOUND: "error:target_not_found", ERROR: "error" }, LIFECYCLE = { INIT: "init", READY: "ready", BEACON: "beacon", TOOLTIP: "tooltip", COMPLETE: "complete", ERROR: "error" }, STATUS2 = { IDLE: "idle", READY: "ready", WAITING: "waiting", RUNNING: "running", PAUSED: "paused", SKIPPED: "skipped", FINISHED: "finished", ERROR: "error" };
      isReact162 = createPortal !== void 0;
      defaultFloaterProps = { options: { preventOverflow: { boundariesElement: "scrollParent" } }, wrapperOptions: { offset: -18, position: !0 } }, defaultLocale = { back: "Back", close: "Close", last: "Last", next: "Next", nextLabelWithProgress: "Next (Step {step} of {steps})", open: "Open the dialog", skip: "Skip" }, defaultStep = { event: "click", placement: "bottom", offset: 10, disableBeacon: !1, disableCloseOnEsc: !1, disableOverlay: !1, disableOverlayClose: !1, disableScrollParentFix: !1, disableScrolling: !1, hideBackButton: !1, hideCloseButton: !1, hideFooter: !1, isFixed: !1, locale: defaultLocale, showProgress: !1, showSkipButton: !1, spotlightClicks: !1, spotlightPadding: 10 }, defaultProps = { continuous: !1, debug: !1, disableCloseOnEsc: !1, disableOverlay: !1, disableOverlayClose: !1, disableScrolling: !1, disableScrollParentFix: !1, getHelpers: void 0, hideBackButton: !1, run: !0, scrollOffset: 20, scrollDuration: 300, scrollToFirstStep: !1, showSkipButton: !1, showProgress: !1, spotlightClicks: !1, spotlightPadding: 10, steps: [] }, defaultOptions2 = { arrowColor: "#fff", backgroundColor: "#fff", beaconSize: 36, overlayColor: "rgba(0, 0, 0, 0.5)", primaryColor: "#f04", spotlightShadow: "0 0 15px rgba(0, 0, 0, 0.5)", textColor: "#333", width: 380, zIndex: 100 }, buttonBase = { backgroundColor: "transparent", border: 0, borderRadius: 0, color: "#555", cursor: "pointer", fontSize: 16, lineHeight: 1, padding: 8, WebkitAppearance: "none" }, spotlight = { borderRadius: 4, position: "absolute" };
      defaultState = { action: "init", controlled: !1, index: 0, lifecycle: LIFECYCLE.INIT, origin: null, size: 0, status: STATUS2.IDLE }, validKeys = objectKeys(omit(defaultState, "controlled", "size")), Store = class {
        constructor(options) {
          __publicField(this, "beaconPopper"), __publicField(this, "tooltipPopper"), __publicField(this, "data", /* @__PURE__ */ new Map()), __publicField(this, "listener"), __publicField(this, "store", /* @__PURE__ */ new Map()), __publicField(this, "addListener", (listener) => {
            this.listener = listener;
          }), __publicField(this, "setSteps", (steps2) => {
            let { size, status } = this.getState(), state = { size: steps2.length, status };
            this.data.set("steps", steps2), status === STATUS2.WAITING && !size && steps2.length && (state.status = STATUS2.RUNNING), this.setState(state);
          }), __publicField(this, "getPopper", (name) => name === "beacon" ? this.beaconPopper : this.tooltipPopper), __publicField(this, "setPopper", (name, popper) => {
            name === "beacon" ? this.beaconPopper = popper : this.tooltipPopper = popper;
          }), __publicField(this, "cleanupPoppers", () => {
            this.beaconPopper = null, this.tooltipPopper = null;
          }), __publicField(this, "close", (origin = null) => {
            let { index, status } = this.getState();
            status === STATUS2.RUNNING && this.setState({ ...this.getNextState({ action: ACTIONS.CLOSE, index: index + 1, origin }) });
          }), __publicField(this, "go", (nextIndex) => {
            let { controlled, status } = this.getState();
            if (controlled || status !== STATUS2.RUNNING) return;
            let step = this.getSteps()[nextIndex];
            this.setState({ ...this.getNextState({ action: ACTIONS.GO, index: nextIndex }), status: step ? status : STATUS2.FINISHED });
          }), __publicField(this, "info", () => this.getState()), __publicField(this, "next", () => {
            let { index, status } = this.getState();
            status === STATUS2.RUNNING && this.setState(this.getNextState({ action: ACTIONS.NEXT, index: index + 1 }));
          }), __publicField(this, "open", () => {
            let { status } = this.getState();
            status === STATUS2.RUNNING && this.setState({ ...this.getNextState({ action: ACTIONS.UPDATE, lifecycle: LIFECYCLE.TOOLTIP }) });
          }), __publicField(this, "prev", () => {
            let { index, status } = this.getState();
            status === STATUS2.RUNNING && this.setState({ ...this.getNextState({ action: ACTIONS.PREV, index: index - 1 }) });
          }), __publicField(this, "reset", (restart = !1) => {
            let { controlled } = this.getState();
            controlled || this.setState({ ...this.getNextState({ action: ACTIONS.RESET, index: 0 }), status: restart ? STATUS2.RUNNING : STATUS2.READY });
          }), __publicField(this, "skip", () => {
            let { status } = this.getState();
            status === STATUS2.RUNNING && this.setState({ action: ACTIONS.SKIP, lifecycle: LIFECYCLE.INIT, status: STATUS2.SKIPPED });
          }), __publicField(this, "start", (nextIndex) => {
            let { index, size } = this.getState();
            this.setState({ ...this.getNextState({ action: ACTIONS.START, index: src_default.number(nextIndex) ? nextIndex : index }, !0), status: size ? STATUS2.RUNNING : STATUS2.WAITING });
          }), __publicField(this, "stop", (advance = !1) => {
            let { index, status } = this.getState();
            [STATUS2.FINISHED, STATUS2.SKIPPED].includes(status) || this.setState({ ...this.getNextState({ action: ACTIONS.STOP, index: index + (advance ? 1 : 0) }), status: STATUS2.PAUSED });
          }), __publicField(this, "update", (state) => {
            var _a, _b;
            if (!hasValidKeys(state, validKeys)) throw new Error(`State is not valid. Valid keys: ${validKeys.join(", ")}`);
            this.setState({ ...this.getNextState({ ...this.getState(), ...state, action: (_a = state.action) != null ? _a : ACTIONS.UPDATE, origin: (_b = state.origin) != null ? _b : null }, !0) });
          });
          let { continuous = !1, stepIndex, steps = [] } = options ?? {};
          this.setState({ action: ACTIONS.INIT, controlled: src_default.number(stepIndex), continuous, index: src_default.number(stepIndex) ? stepIndex : 0, lifecycle: LIFECYCLE.INIT, origin: null, status: steps.length ? STATUS2.READY : STATUS2.IDLE }, !0), this.beaconPopper = null, this.tooltipPopper = null, this.listener = null, this.setSteps(steps);
        }
        getState() {
          return this.store.size ? { action: this.store.get("action") || "", controlled: this.store.get("controlled") || !1, index: parseInt(this.store.get("index"), 10), lifecycle: this.store.get("lifecycle") || "", origin: this.store.get("origin") || null, size: this.store.get("size") || 0, status: this.store.get("status") || "" } : { ...defaultState };
        }
        getNextState(state, force = !1) {
          var _a, _b, _c, _d, _e;
          let { action, controlled, index, size, status } = this.getState(), newIndex = src_default.number(state.index) ? state.index : index, nextIndex = controlled && !force ? index : Math.min(Math.max(newIndex, 0), size);
          return { action: (_a = state.action) != null ? _a : action, controlled, index: nextIndex, lifecycle: (_b = state.lifecycle) != null ? _b : LIFECYCLE.INIT, origin: (_c = state.origin) != null ? _c : null, size: (_d = state.size) != null ? _d : size, status: nextIndex === size ? STATUS2.FINISHED : (_e = state.status) != null ? _e : status };
        }
        getSteps() {
          let steps = this.data.get("steps");
          return Array.isArray(steps) ? steps : [];
        }
        hasUpdatedState(oldState) {
          let before = JSON.stringify(oldState), after = JSON.stringify(this.getState());
          return before !== after;
        }
        setState(nextState, initial = !1) {
          let state = this.getState(), { action, index, lifecycle, origin = null, size, status } = { ...state, ...nextState };
          this.store.set("action", action), this.store.set("index", index), this.store.set("lifecycle", lifecycle), this.store.set("origin", origin), this.store.set("size", size), this.store.set("status", status), initial && (this.store.set("controlled", nextState.controlled), this.store.set("continuous", nextState.continuous)), this.listener && this.hasUpdatedState(state) && this.listener(this.getState());
        }
        getHelpers() {
          return { close: this.close, go: this.go, info: this.info, next: this.next, open: this.open, prev: this.prev, reset: this.reset, skip: this.skip };
        }
      };
      Spotlight_default = JoyrideSpotlight, JoyrideOverlay = class extends Component {
        constructor() {
          super(...arguments), __publicField(this, "isActive", !1), __publicField(this, "resizeTimeout"), __publicField(this, "scrollTimeout"), __publicField(this, "scrollParent"), __publicField(this, "state", { isScrolling: !1, mouseOverSpotlight: !1, showSpotlight: !0 }), __publicField(this, "hideSpotlight", () => {
            let { continuous, disableOverlay, lifecycle } = this.props, hiddenLifecycles = [LIFECYCLE.INIT, LIFECYCLE.BEACON, LIFECYCLE.COMPLETE, LIFECYCLE.ERROR];
            return disableOverlay || (continuous ? hiddenLifecycles.includes(lifecycle) : lifecycle !== LIFECYCLE.TOOLTIP);
          }), __publicField(this, "handleMouseMove", (event) => {
            let { mouseOverSpotlight } = this.state, { height, left, position, top, width } = this.spotlightStyles, offsetY = position === "fixed" ? event.clientY : event.pageY, offsetX = position === "fixed" ? event.clientX : event.pageX, inSpotlightHeight = offsetY >= top && offsetY <= top + height, inSpotlight = offsetX >= left && offsetX <= left + width && inSpotlightHeight;
            inSpotlight !== mouseOverSpotlight && this.updateState({ mouseOverSpotlight: inSpotlight });
          }), __publicField(this, "handleScroll", () => {
            let { target } = this.props, element = getElement(target);
            if (this.scrollParent !== document) {
              let { isScrolling } = this.state;
              isScrolling || this.updateState({ isScrolling: !0, showSpotlight: !1 }), clearTimeout(this.scrollTimeout), this.scrollTimeout = window.setTimeout(() => {
                this.updateState({ isScrolling: !1, showSpotlight: !0 });
              }, 50);
            } else hasPosition(element, "sticky") && this.updateState({});
          }), __publicField(this, "handleResize", () => {
            clearTimeout(this.resizeTimeout), this.resizeTimeout = window.setTimeout(() => {
              this.isActive && this.forceUpdate();
            }, 100);
          });
        }
        componentDidMount() {
          let { debug, disableScrolling, disableScrollParentFix = !1, target } = this.props, element = getElement(target);
          this.scrollParent = getScrollParent2(element ?? document.body, disableScrollParentFix, !0), this.isActive = !0, !disableScrolling && hasCustomScrollParent(element, !0) && log2({ title: "step has a custom scroll parent and can cause trouble with scrolling", data: [{ key: "parent", value: this.scrollParent }], debug }), window.addEventListener("resize", this.handleResize);
        }
        componentDidUpdate(previousProps) {
          var _a;
          let { disableScrollParentFix, lifecycle, spotlightClicks, target } = this.props, { changed } = treeChanges(previousProps, this.props);
          if (changed("target") || changed("disableScrollParentFix")) {
            let element = getElement(target);
            this.scrollParent = getScrollParent2(element ?? document.body, disableScrollParentFix, !0);
          }
          changed("lifecycle", LIFECYCLE.TOOLTIP) && ((_a = this.scrollParent) == null || _a.addEventListener("scroll", this.handleScroll, { passive: !0 }), setTimeout(() => {
            let { isScrolling } = this.state;
            isScrolling || this.updateState({ showSpotlight: !0 });
          }, 100)), (changed("spotlightClicks") || changed("disableOverlay") || changed("lifecycle")) && (spotlightClicks && lifecycle === LIFECYCLE.TOOLTIP ? window.addEventListener("mousemove", this.handleMouseMove, !1) : lifecycle !== LIFECYCLE.TOOLTIP && window.removeEventListener("mousemove", this.handleMouseMove));
        }
        componentWillUnmount() {
          var _a;
          this.isActive = !1, window.removeEventListener("mousemove", this.handleMouseMove), window.removeEventListener("resize", this.handleResize), clearTimeout(this.resizeTimeout), clearTimeout(this.scrollTimeout), (_a = this.scrollParent) == null || _a.removeEventListener("scroll", this.handleScroll);
        }
        get overlayStyles() {
          let { mouseOverSpotlight } = this.state, { disableOverlayClose, placement, styles } = this.props, baseStyles = styles.overlay;
          return isLegacy() && (baseStyles = placement === "center" ? styles.overlayLegacyCenter : styles.overlayLegacy), { cursor: disableOverlayClose ? "default" : "pointer", height: getDocumentHeight(), pointerEvents: mouseOverSpotlight ? "none" : "auto", ...baseStyles };
        }
        get spotlightStyles() {
          var _a, _b, _c;
          let { showSpotlight } = this.state, { disableScrollParentFix = !1, spotlightClicks, spotlightPadding = 0, styles, target } = this.props, element = getElement(target), elementRect = getClientRect2(element), isFixedTarget = hasPosition(element), top = getElementPosition(element, spotlightPadding, disableScrollParentFix);
          return { ...isLegacy() ? styles.spotlightLegacy : styles.spotlight, height: Math.round(((_a = elementRect?.height) != null ? _a : 0) + spotlightPadding * 2), left: Math.round(((_b = elementRect?.left) != null ? _b : 0) - spotlightPadding), opacity: showSpotlight ? 1 : 0, pointerEvents: spotlightClicks ? "none" : "auto", position: isFixedTarget ? "fixed" : "absolute", top, transition: "opacity 0.2s", width: Math.round(((_c = elementRect?.width) != null ? _c : 0) + spotlightPadding * 2) };
        }
        updateState(state) {
          this.isActive && this.setState((previousState) => ({ ...previousState, ...state }));
        }
        render() {
          let { showSpotlight } = this.state, { onClickOverlay, placement } = this.props, { hideSpotlight, overlayStyles, spotlightStyles } = this;
          if (hideSpotlight()) return null;
          let spotlight2 = placement !== "center" && showSpotlight && createElement(Spotlight_default, { styles: spotlightStyles });
          if (getBrowser() === "safari") {
            let { mixBlendMode, zIndex, ...safariOverlay } = overlayStyles;
            spotlight2 = createElement("div", { style: { ...safariOverlay } }, spotlight2), delete overlayStyles.backgroundColor;
          }
          return createElement("div", { className: "react-joyride__overlay", "data-test-id": "overlay", onClick: onClickOverlay, role: "presentation", style: overlayStyles }, spotlight2);
        }
      }, JoyridePortal = class extends Component {
        constructor() {
          super(...arguments), __publicField(this, "node", null);
        }
        componentDidMount() {
          let { id } = this.props;
          canUseDOM2() && (this.node = document.createElement("div"), this.node.id = id, document.body.appendChild(this.node), isReact162 || this.renderReact15());
        }
        componentDidUpdate() {
          canUseDOM2() && (isReact162 || this.renderReact15());
        }
        componentWillUnmount() {
          !canUseDOM2() || !this.node || (isReact162 || unmountComponentAtNode(this.node), this.node.parentNode === document.body && (document.body.removeChild(this.node), this.node = null));
        }
        renderReact15() {
          if (!canUseDOM2()) return;
          let { children } = this.props;
          this.node && unstable_renderSubtreeIntoContainer(this, children, this.node);
        }
        renderReact16() {
          if (!canUseDOM2() || !isReact162) return null;
          let { children } = this.props;
          return this.node ? createPortal(children, this.node) : null;
        }
        render() {
          return isReact162 ? this.renderReact16() : null;
        }
      }, Scope = class {
        constructor(element, options) {
          if (__publicField(this, "element"), __publicField(this, "options"), __publicField(this, "canBeTabbed", (element2) => {
            let { tabIndex } = element2;
            return tabIndex === null || tabIndex < 0 ? !1 : this.canHaveFocus(element2);
          }), __publicField(this, "canHaveFocus", (element2) => {
            let validTabNodes = /input|select|textarea|button|object/, nodeName = element2.nodeName.toLowerCase();
            return (validTabNodes.test(nodeName) && !element2.getAttribute("disabled") || nodeName === "a" && !!element2.getAttribute("href")) && this.isVisible(element2);
          }), __publicField(this, "findValidTabElements", () => [].slice.call(this.element.querySelectorAll("*"), 0).filter(this.canBeTabbed)), __publicField(this, "handleKeyDown", (event) => {
            let { code = "Tab" } = this.options;
            event.code === code && this.interceptTab(event);
          }), __publicField(this, "interceptTab", (event) => {
            event.preventDefault();
            let elements = this.findValidTabElements(), { shiftKey } = event;
            if (!elements.length) return;
            let x2 = document.activeElement ? elements.indexOf(document.activeElement) : 0;
            x2 === -1 || !shiftKey && x2 + 1 === elements.length ? x2 = 0 : shiftKey && x2 === 0 ? x2 = elements.length - 1 : x2 += shiftKey ? -1 : 1, elements[x2].focus();
          }), __publicField(this, "isHidden", (element2) => {
            let noSize = element2.offsetWidth <= 0 && element2.offsetHeight <= 0, style = window.getComputedStyle(element2);
            return noSize && !element2.innerHTML ? !0 : noSize && style.getPropertyValue("overflow") !== "visible" || style.getPropertyValue("display") === "none";
          }), __publicField(this, "isVisible", (element2) => {
            let parentElement = element2;
            for (; parentElement; ) if (parentElement instanceof HTMLElement) {
              if (parentElement === document.body) break;
              if (this.isHidden(parentElement)) return !1;
              parentElement = parentElement.parentNode;
            }
            return !0;
          }), __publicField(this, "removeScope", () => {
            window.removeEventListener("keydown", this.handleKeyDown);
          }), __publicField(this, "checkFocus", (target) => {
            document.activeElement !== target && (target.focus(), window.requestAnimationFrame(() => this.checkFocus(target)));
          }), __publicField(this, "setFocus", () => {
            let { selector } = this.options;
            if (!selector) return;
            let target = this.element.querySelector(selector);
            target && window.requestAnimationFrame(() => this.checkFocus(target));
          }), !(element instanceof HTMLElement)) throw new TypeError("Invalid parameter: element must be an HTMLElement");
          this.element = element, this.options = options, window.addEventListener("keydown", this.handleKeyDown, !1), this.setFocus();
        }
      }, JoyrideBeacon = class extends Component {
        constructor(props) {
          if (super(props), __publicField(this, "beacon", null), __publicField(this, "setBeaconRef", (c2) => {
            this.beacon = c2;
          }), props.beaconComponent) return;
          let head = document.head || document.getElementsByTagName("head")[0], style = document.createElement("style");
          style.id = "joyride-beacon-animation", props.nonce && style.setAttribute("nonce", props.nonce), style.appendChild(document.createTextNode(`
        @keyframes joyride-beacon-inner {
          20% {
            opacity: 0.9;
          }
        
          90% {
            opacity: 0.7;
          }
        }
        
        @keyframes joyride-beacon-outer {
          0% {
            transform: scale(1);
          }
        
          45% {
            opacity: 0.7;
            transform: scale(0.75);
          }
        
          100% {
            opacity: 0.9;
            transform: scale(1);
          }
        }
      `)), head.appendChild(style);
        }
        componentDidMount() {
          let { shouldFocus } = this.props;
          src_default.domElement(this.beacon) || console.warn("beacon is not a valid DOM element"), setTimeout(() => {
            src_default.domElement(this.beacon) && shouldFocus && this.beacon.focus();
          }, 0);
        }
        componentWillUnmount() {
          let style = document.getElementById("joyride-beacon-animation");
          style?.parentNode && style.parentNode.removeChild(style);
        }
        render() {
          let { beaconComponent, continuous, index, isLastStep, locale, onClickOrHover, size, step, styles } = this.props, title = getReactNodeText(locale.open), sharedProps = { "aria-label": title, onClick: onClickOrHover, onMouseEnter: onClickOrHover, ref: this.setBeaconRef, title }, component;
          return beaconComponent ? component = createElement(beaconComponent, { continuous, index, isLastStep, size, step, ...sharedProps }) : component = createElement("button", { key: "JoyrideBeacon", className: "react-joyride__beacon", "data-test-id": "button-beacon", style: styles.beacon, type: "button", ...sharedProps }, createElement("span", { style: styles.beaconInner }), createElement("span", { style: styles.beaconOuter })), component;
        }
      };
      CloseButton_default = JoyrideTooltipCloseButton;
      Container_default = JoyrideTooltipContainer, JoyrideTooltip = class extends Component {
        constructor() {
          super(...arguments), __publicField(this, "handleClickBack", (event) => {
            event.preventDefault();
            let { helpers } = this.props;
            helpers.prev();
          }), __publicField(this, "handleClickClose", (event) => {
            event.preventDefault();
            let { helpers } = this.props;
            helpers.close("button_close");
          }), __publicField(this, "handleClickPrimary", (event) => {
            event.preventDefault();
            let { continuous, helpers } = this.props;
            if (!continuous) {
              helpers.close("button_primary");
              return;
            }
            helpers.next();
          }), __publicField(this, "handleClickSkip", (event) => {
            event.preventDefault();
            let { helpers } = this.props;
            helpers.skip();
          }), __publicField(this, "getElementsProps", () => {
            let { continuous, index, isLastStep, setTooltipRef, size, step } = this.props, { back, close, last, next, nextLabelWithProgress, skip } = step.locale, backText = getReactNodeText(back), closeText = getReactNodeText(close), lastText = getReactNodeText(last), nextText = getReactNodeText(next), skipText = getReactNodeText(skip), primary = close, primaryText = closeText;
            if (continuous) {
              if (primary = next, primaryText = nextText, step.showProgress && !isLastStep) {
                let labelWithProgress = getReactNodeText(nextLabelWithProgress, { step: index + 1, steps: size });
                primary = replaceLocaleContent(nextLabelWithProgress, index + 1, size), primaryText = labelWithProgress;
              }
              isLastStep && (primary = last, primaryText = lastText);
            }
            return { backProps: { "aria-label": backText, children: back, "data-action": "back", onClick: this.handleClickBack, role: "button", title: backText }, closeProps: { "aria-label": closeText, children: close, "data-action": "close", onClick: this.handleClickClose, role: "button", title: closeText }, primaryProps: { "aria-label": primaryText, children: primary, "data-action": "primary", onClick: this.handleClickPrimary, role: "button", title: primaryText }, skipProps: { "aria-label": skipText, children: skip, "data-action": "skip", onClick: this.handleClickSkip, role: "button", title: skipText }, tooltipProps: { "aria-modal": !0, ref: setTooltipRef, role: "alertdialog" } };
          });
        }
        render() {
          let { continuous, index, isLastStep, setTooltipRef, size, step } = this.props, { beaconComponent, tooltipComponent, ...cleanStep } = step, component;
          if (tooltipComponent) {
            let renderProps = { ...this.getElementsProps(), continuous, index, isLastStep, size, step: cleanStep, setTooltipRef };
            component = createElement(tooltipComponent, { ...renderProps });
          } else component = createElement(Container_default, { ...this.getElementsProps(), continuous, index, isLastStep, size, step });
          return component;
        }
      }, JoyrideStep = class extends Component {
        constructor() {
          super(...arguments), __publicField(this, "scope", null), __publicField(this, "tooltip", null), __publicField(this, "handleClickHoverBeacon", (event) => {
            let { step, store } = this.props;
            event.type === "mouseenter" && step.event !== "hover" || store.update({ lifecycle: LIFECYCLE.TOOLTIP });
          }), __publicField(this, "setTooltipRef", (element) => {
            this.tooltip = element;
          }), __publicField(this, "setPopper", (popper, type) => {
            var _a;
            let { action, lifecycle, step, store } = this.props;
            type === "wrapper" ? store.setPopper("beacon", popper) : store.setPopper("tooltip", popper), store.getPopper("beacon") && (store.getPopper("tooltip") || step.placement === "center") && lifecycle === LIFECYCLE.INIT && store.update({ action, lifecycle: LIFECYCLE.READY }), (_a = step.floaterProps) != null && _a.getPopper && step.floaterProps.getPopper(popper, type);
          }), __publicField(this, "renderTooltip", (renderProps) => {
            let { continuous, helpers, index, size, step } = this.props;
            return createElement(JoyrideTooltip, { continuous, helpers, index, isLastStep: index + 1 === size, setTooltipRef: this.setTooltipRef, size, step, ...renderProps });
          });
        }
        componentDidMount() {
          let { debug, index } = this.props;
          log2({ title: `step:${index}`, data: [{ key: "props", value: this.props }], debug });
        }
        componentDidUpdate(previousProps) {
          var _a;
          let { action, callback, continuous, controlled, debug, helpers, index, lifecycle, shouldScroll: shouldScroll2, status, step, store } = this.props, { changed, changedFrom } = treeChanges(previousProps, this.props), state = helpers.info(), skipBeacon = continuous && action !== ACTIONS.CLOSE && (index > 0 || action === ACTIONS.PREV), hasStoreChanged = changed("action") || changed("index") || changed("lifecycle") || changed("status"), isInitial = changedFrom("lifecycle", [LIFECYCLE.TOOLTIP, LIFECYCLE.INIT], LIFECYCLE.INIT), isAfterAction = changed("action", [ACTIONS.NEXT, ACTIONS.PREV, ACTIONS.SKIP, ACTIONS.CLOSE]), isControlled = controlled && index === previousProps.index;
          if (isAfterAction && (isInitial || isControlled) && callback({ ...state, index: previousProps.index, lifecycle: LIFECYCLE.COMPLETE, step: previousProps.step, type: EVENTS.STEP_AFTER }), step.placement === "center" && status === STATUS2.RUNNING && changed("index") && action !== ACTIONS.START && lifecycle === LIFECYCLE.INIT && store.update({ lifecycle: LIFECYCLE.READY }), hasStoreChanged) {
            let element = getElement(step.target), elementExists = !!element;
            elementExists && isElementVisible(element) ? (changedFrom("status", STATUS2.READY, STATUS2.RUNNING) || changedFrom("lifecycle", LIFECYCLE.INIT, LIFECYCLE.READY)) && callback({ ...state, step, type: EVENTS.STEP_BEFORE }) : (console.warn(elementExists ? "Target not visible" : "Target not mounted", step), callback({ ...state, type: EVENTS.TARGET_NOT_FOUND, step }), controlled || store.update({ index: index + (action === ACTIONS.PREV ? -1 : 1) }));
          }
          changedFrom("lifecycle", LIFECYCLE.INIT, LIFECYCLE.READY) && store.update({ lifecycle: hideBeacon(step) || skipBeacon ? LIFECYCLE.TOOLTIP : LIFECYCLE.BEACON }), changed("index") && log2({ title: `step:${lifecycle}`, data: [{ key: "props", value: this.props }], debug }), changed("lifecycle", LIFECYCLE.BEACON) && callback({ ...state, step, type: EVENTS.BEACON }), changed("lifecycle", LIFECYCLE.TOOLTIP) && (callback({ ...state, step, type: EVENTS.TOOLTIP }), shouldScroll2 && this.tooltip && (this.scope = new Scope(this.tooltip, { selector: "[data-action=primary]" }), this.scope.setFocus())), changedFrom("lifecycle", [LIFECYCLE.TOOLTIP, LIFECYCLE.INIT], LIFECYCLE.INIT) && ((_a = this.scope) == null || _a.removeScope(), store.cleanupPoppers());
        }
        componentWillUnmount() {
          var _a;
          (_a = this.scope) == null || _a.removeScope();
        }
        get open() {
          let { lifecycle, step } = this.props;
          return hideBeacon(step) || lifecycle === LIFECYCLE.TOOLTIP;
        }
        render() {
          let { continuous, debug, index, nonce, shouldScroll: shouldScroll2, size, step } = this.props, target = getElement(step.target);
          return !validateStep(step) || !src_default.domElement(target) ? null : createElement("div", { key: `JoyrideStep-${index}`, className: "react-joyride__step" }, createElement(ReactFloater, { ...step.floaterProps, component: this.renderTooltip, debug, getPopper: this.setPopper, id: `react-joyride-step-${index}`, open: this.open, placement: step.placement, target: step.target }, createElement(JoyrideBeacon, { beaconComponent: step.beaconComponent, continuous, index, isLastStep: index + 1 === size, locale: step.locale, nonce, onClickOrHover: this.handleClickHoverBeacon, shouldFocus: shouldScroll2, size, step, styles: step.styles })));
        }
      }, Joyride = class extends Component {
        constructor(props) {
          super(props), __publicField(this, "helpers"), __publicField(this, "store"), __publicField(this, "callback", (data) => {
            let { callback } = this.props;
            src_default.function(callback) && callback(data);
          }), __publicField(this, "handleKeyboard", (event) => {
            let { index, lifecycle } = this.state, { steps } = this.props, step = steps[index];
            lifecycle === LIFECYCLE.TOOLTIP && event.code === "Escape" && step && !step.disableCloseOnEsc && this.store.close("keyboard");
          }), __publicField(this, "handleClickOverlay", () => {
            let { index } = this.state, { steps } = this.props;
            getMergedStep(this.props, steps[index]).disableOverlayClose || this.helpers.close("overlay");
          }), __publicField(this, "syncState", (state) => {
            this.setState(state);
          });
          let { debug, getHelpers, run = !0, stepIndex } = props;
          this.store = createStore({ ...props, controlled: run && src_default.number(stepIndex) }), this.helpers = this.store.getHelpers();
          let { addListener } = this.store;
          log2({ title: "init", data: [{ key: "props", value: this.props }, { key: "state", value: this.state }], debug }), addListener(this.syncState), getHelpers && getHelpers(this.helpers), this.state = this.store.getState();
        }
        componentDidMount() {
          if (!canUseDOM2()) return;
          let { debug, disableCloseOnEsc, run, steps } = this.props, { start } = this.store;
          validateSteps(steps, debug) && run && start(), disableCloseOnEsc || document.body.addEventListener("keydown", this.handleKeyboard, { passive: !0 });
        }
        componentDidUpdate(previousProps, previousState) {
          if (!canUseDOM2()) return;
          let { action, controlled, index, status } = this.state, { debug, run, stepIndex, steps } = this.props, { stepIndex: previousStepIndex, steps: previousSteps } = previousProps, { reset, setSteps, start, stop, update: update2 } = this.store, { changed: changedProps } = treeChanges(previousProps, this.props), { changed, changedFrom } = treeChanges(previousState, this.state), step = getMergedStep(this.props, steps[index]), stepsChanged = !equal(previousSteps, steps), stepIndexChanged = src_default.number(stepIndex) && changedProps("stepIndex"), target = getElement(step.target);
          if (stepsChanged && (validateSteps(steps, debug) ? setSteps(steps) : console.warn("Steps are not valid", steps)), changedProps("run") && (run ? start(stepIndex) : stop()), stepIndexChanged) {
            let nextAction = src_default.number(previousStepIndex) && previousStepIndex < stepIndex ? ACTIONS.NEXT : ACTIONS.PREV;
            action === ACTIONS.STOP && (nextAction = ACTIONS.START), [STATUS2.FINISHED, STATUS2.SKIPPED].includes(status) || update2({ action: action === ACTIONS.CLOSE ? ACTIONS.CLOSE : nextAction, index: stepIndex, lifecycle: LIFECYCLE.INIT });
          }
          !controlled && status === STATUS2.RUNNING && index === 0 && !target && (this.store.update({ index: index + 1 }), this.callback({ ...this.state, type: EVENTS.TARGET_NOT_FOUND, step }));
          let callbackData = { ...this.state, index, step };
          if (changed("action", [ACTIONS.NEXT, ACTIONS.PREV, ACTIONS.SKIP, ACTIONS.CLOSE]) && changed("status", STATUS2.PAUSED)) {
            let previousStep = getMergedStep(this.props, steps[previousState.index]);
            this.callback({ ...callbackData, index: previousState.index, lifecycle: LIFECYCLE.COMPLETE, step: previousStep, type: EVENTS.STEP_AFTER });
          }
          if (changed("status", [STATUS2.FINISHED, STATUS2.SKIPPED])) {
            let previousStep = getMergedStep(this.props, steps[previousState.index]);
            controlled || this.callback({ ...callbackData, index: previousState.index, lifecycle: LIFECYCLE.COMPLETE, step: previousStep, type: EVENTS.STEP_AFTER }), this.callback({ ...callbackData, type: EVENTS.TOUR_END, step: previousStep, index: previousState.index }), reset();
          } else changedFrom("status", [STATUS2.IDLE, STATUS2.READY], STATUS2.RUNNING) ? this.callback({ ...callbackData, type: EVENTS.TOUR_START }) : (changed("status") || changed("action", ACTIONS.RESET)) && this.callback({ ...callbackData, type: EVENTS.TOUR_STATUS });
          this.scrollToStep(previousState);
        }
        componentWillUnmount() {
          let { disableCloseOnEsc } = this.props;
          disableCloseOnEsc || document.body.removeEventListener("keydown", this.handleKeyboard);
        }
        scrollToStep(previousState) {
          let { index, lifecycle, status } = this.state, { debug, disableScrollParentFix = !1, scrollDuration, scrollOffset = 20, scrollToFirstStep = !1, steps } = this.props, step = getMergedStep(this.props, steps[index]), target = getElement(step.target), shouldScrollToStep = shouldScroll({ isFirstStep: index === 0, lifecycle, previousLifecycle: previousState.lifecycle, scrollToFirstStep, step, target });
          if (status === STATUS2.RUNNING && shouldScrollToStep) {
            let hasCustomScroll = hasCustomScrollParent(target, disableScrollParentFix), scrollParent2 = getScrollParent2(target, disableScrollParentFix), scrollY = Math.floor(getScrollTo(target, scrollOffset, disableScrollParentFix)) || 0;
            log2({ title: "scrollToStep", data: [{ key: "index", value: index }, { key: "lifecycle", value: lifecycle }, { key: "status", value: status }], debug });
            let beaconPopper = this.store.getPopper("beacon"), tooltipPopper = this.store.getPopper("tooltip");
            if (lifecycle === LIFECYCLE.BEACON && beaconPopper) {
              let { offsets, placement } = beaconPopper;
              !["bottom"].includes(placement) && !hasCustomScroll && (scrollY = Math.floor(offsets.popper.top - scrollOffset));
            } else if (lifecycle === LIFECYCLE.TOOLTIP && tooltipPopper) {
              let { flipped, offsets, placement } = tooltipPopper;
              ["top", "right", "left"].includes(placement) && !flipped && !hasCustomScroll ? scrollY = Math.floor(offsets.popper.top - scrollOffset) : scrollY -= step.spotlightPadding;
            }
            scrollY = scrollY >= 0 ? scrollY : 0, status === STATUS2.RUNNING && scrollTo(scrollY, { element: scrollParent2, duration: scrollDuration }).then(() => {
              setTimeout(() => {
                var _a;
                (_a = this.store.getPopper("tooltip")) == null || _a.instance.update();
              }, 10);
            });
          }
        }
        render() {
          if (!canUseDOM2()) return null;
          let { index, lifecycle, status } = this.state, { continuous = !1, debug = !1, nonce, scrollToFirstStep = !1, steps } = this.props, isRunning = status === STATUS2.RUNNING, content = {};
          if (isRunning && steps[index]) {
            let step = getMergedStep(this.props, steps[index]);
            content.step = createElement(JoyrideStep, { ...this.state, callback: this.callback, continuous, debug, helpers: this.helpers, nonce, shouldScroll: !step.disableScrolling && (index !== 0 || scrollToFirstStep), step, store: this.store }), content.overlay = createElement(JoyridePortal, { id: "react-joyride-portal" }, createElement(JoyrideOverlay, { ...step, continuous, debug, lifecycle, onClickOverlay: this.handleClickOverlay }));
          }
          return createElement("div", { className: "react-joyride" }, content.step, content.overlay);
        }
      };
      __publicField(Joyride, "defaultProps", defaultProps);
      components_default2 = Joyride, StyledButton = styled.button`
  all: unset;
  box-sizing: border-box;
  border: 0;
  border-radius: 0.25rem;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.75rem;
  background: ${({ theme: theme2, variant }) => variant === "primary" ? theme2.color.secondary : variant === "secondary" ? theme2.color.lighter : variant === "outline" ? "transparent" : variant === "white" ? theme2.color.lightest : theme2.color.secondary};
  color: ${({ theme: theme2, variant }) => variant === "primary" ? theme2.color.lightest : variant === "secondary" || variant === "outline" ? theme2.darkest : variant === "white" ? theme2.color.secondary : theme2.color.lightest};
  box-shadow: ${({ variant }) => variant === "secondary" || variant === "outline" ? "#D9E8F2 0 0 0 1px inset" : "none"};
  height: 32px;
  font-size: 0.8125rem;
  font-weight: 700;
  font-family: ${({ theme: theme2 }) => theme2.typography.fonts.base};
  transition: background-color, box-shadow, color, opacity;
  transition-duration: 0.16s;
  transition-timing-function: ease-in-out;
  text-decoration: none;

  &:hover {
    background-color: ${({ theme: theme2, variant }) => variant === "primary" ? "#0b94eb" : variant === "secondary" ? "#eef4f9" : variant === "outline" ? "transparent" : variant === "white" ? theme2.color.lightest : "#0b94eb"};
    color: ${({ theme: theme2, variant }) => variant === "primary" ? theme2.color.lightest : variant === "secondary" || variant === "outline" ? theme2.darkest : variant === "white" ? theme2.color.darkest : theme2.color.lightest};
  }

  &:focus {
    box-shadow: ${({ variant }) => variant === "primary" ? "inset 0 0 0 1px rgba(0, 0, 0, 0.2)" : variant === "secondary" || variant === "outline" ? "inset 0 0 0 1px #0b94eb" : variant === "white" ? "none" : "inset 0 0 0 2px rgba(0, 0, 0, 0.1)"};
  }
`, Button2 = forwardRef(function({ children, onClick, variant = "primary", ...rest }, ref) {
        return react_default.createElement(StyledButton, { ref, onClick, variant, ...rest }, children);
      }), TooltipBody = styled.div`
  padding: 15px;
  border-radius: 5px;
`, Wrapper2 = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
`, TooltipHeader = styled.div`
  display: flex;
  align-items: center;
  align-self: stretch;
  justify-content: space-between;
  margin: -5px -5px 5px 0;
`, TooltipTitle = styled.div`
  line-height: 18px;
  font-weight: 700;
  font-size: 14px;
  margin: 5px 5px 5px 0;
`, TooltipContent = styled.p`
  font-size: 14px;
  line-height: 18px;
  text-align: start;
  text-wrap: balance;
  margin: 0;
  margin-top: 5px;
`, TooltipFooter = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 15px;
`, Count = styled.span`
  font-size: 13px;
`, Tooltip = ({ index, size, step, closeProps, primaryProps, tooltipProps }) => (useEffect(() => {
        let style = document.createElement("style");
        return style.id = "#sb-onboarding-arrow-style", style.innerHTML = `
      .__floater__arrow { container-type: size; }
      .__floater__arrow span { background: ${color.secondary}; }
      .__floater__arrow span::before, .__floater__arrow span::after {
        content: '';
        display: block;
        width: 2px;
        height: 2px;
        background: ${color.secondary};
        box-shadow: 0 0 0 2px ${color.secondary};
        border-radius: 3px;
        flex: 0 0 2px;
      }
      @container (min-height: 1px) {
        .__floater__arrow span { flex-direction: column; }
      }
    `, document.head.appendChild(style), () => {
          let styleElement = document.querySelector("#sb-onboarding-arrow-style");
          styleElement && styleElement.remove();
        };
      }, []), react_default.createElement(TooltipBody, { ...tooltipProps, style: step.styles?.tooltip }, react_default.createElement(Wrapper2, null, react_default.createElement(TooltipHeader, null, step.title && react_default.createElement(TooltipTitle, null, step.title), react_default.createElement(IconButton, { ...closeProps, onClick: closeProps.onClick, variant: "solid" }, react_default.createElement(CloseAltIcon, null))), react_default.createElement(TooltipContent, null, step.content)), react_default.createElement(TooltipFooter, { id: "buttonNext" }, react_default.createElement(Count, null, index + 1, " of ", size), !step.hideNextButton && react_default.createElement(Button2, { ...primaryProps, onClick: step.onNextButtonClick || primaryProps.onClick, variant: "white" }, index + 1 === size ? "Done" : "Next"))));
      fadeIn = keyframes({ from: { opacity: 0 }, to: { opacity: 1 } }), slideIn = keyframes({ from: { transform: "translate(0, 20px)", opacity: 0 }, to: { transform: "translate(0, 0)", opacity: 1 } }), scaleIn = keyframes({ from: { opacity: 0, transform: "scale(0.8)" }, to: { opacity: 1, transform: "scale(1)" } }), rotate = keyframes({ "0%": { transform: "rotate(0deg)" }, "100%": { transform: "rotate(360deg)" } }), Wrapper3 = styled.div(({ visible }) => ({ position: "fixed", top: 0, left: 0, right: 0, bottom: 0, display: "flex", opacity: visible ? 1 : 0, alignItems: "center", justifyContent: "center", zIndex: 1e3, transition: "opacity 1s 0.5s" })), Backdrop = styled.div({ position: "absolute", top: 0, left: 0, right: 0, bottom: 0, animation: `${fadeIn} 2s`, background: `
    radial-gradient(90% 90%, #ff4785 0%, #db5698 30%, #1ea7fdcc 100%),
    radial-gradient(circle, #ff4785 0%, transparent 80%),
    radial-gradient(circle at 30% 40%, #fc521f99 0%, #fc521f66 20%, transparent 40%),
    radial-gradient(circle at 75% 75%, #fc521f99 0%, #fc521f77 18%, transparent 30%)`, "&::before": { opacity: 0.5, background: `
      radial-gradient(circle at 30% 40%, #fc521f99 0%, #fc521f66 10%, transparent 20%),
      radial-gradient(circle at 75% 75%, #fc521f99 0%, #fc521f77 8%, transparent 20%)`, content: '""', position: "absolute", top: "-50vw", left: "-50vh", transform: "translate(-50%, -50%)", width: "calc(100vw + 100vh)", height: "calc(100vw + 100vh)", animation: `${rotate} 12s linear infinite` } }), Content = styled.div(({ visible }) => ({ position: "absolute", top: "50%", left: "50%", transform: "translate(-50%, -50%)", color: "white", textAlign: "center", width: "90vw", minWidth: 290, maxWidth: 410, opacity: visible ? 1 : 0, transition: "opacity 0.5s", h1: { fontSize: 45, fontWeight: "bold", animation: `${slideIn} 1.5s 1s backwards` } })), Features = styled.div({ display: "flex", marginTop: 40, div: { display: "flex", flexBasis: "33.33%", flexDirection: "column", alignItems: "center", animation: `${slideIn} 1s backwards`, "&:nth-child(1)": { animationDelay: "2s" }, "&:nth-child(2)": { animationDelay: "2.5s" }, "&:nth-child(3)": { animationDelay: "3s" } }, svg: { marginBottom: 10 } }), RadialButton = styled.button({ display: "inline-flex", position: "relative", alignItems: "center", justifyContent: "center", marginTop: 40, width: 48, height: 48, padding: 0, borderRadius: "50%", border: 0, outline: "none", background: "rgba(255, 255, 255, 0.3)", cursor: "pointer", transition: "background 0.2s", animation: `${scaleIn} 1.5s 4s backwards`, "&:hover, &:focus": { background: "rgba(255, 255, 255, 0.4)" } }), ArrowIcon = styled(ArrowRightIcon)({ width: 30, color: "white" }), ProgressCircle = styled.svg(({ progress }) => ({ position: "absolute", top: -1, left: -1, width: "50px!important", height: "50px!important", transform: "rotate(-90deg)", color: "white", circle: { r: "24", cx: "25", cy: "25", fill: "transparent", stroke: progress ? "currentColor" : "transparent", strokeWidth: "1", strokeLinecap: "round", strokeDasharray: Math.PI * 48 } })), SplashScreen = ({ onDismiss, duration = 6e3 }) => {
        let [progress, setProgress] = useState(-4e5 / duration), [visible, setVisible] = useState(!0), ready = progress >= 100, dismiss = useCallback(() => {
          setVisible(!1);
          let timeout = setTimeout(onDismiss, 1500);
          return () => clearTimeout(timeout);
        }, [onDismiss]);
        return useEffect(() => {
          if (!duration) return;
          let framelength = 1e3 / 50, increment = 100 / (duration / framelength), interval = setInterval(() => setProgress((prev) => prev + increment), framelength);
          return () => clearInterval(interval);
        }, [duration]), useEffect(() => {
          ready && dismiss();
        }, [ready, dismiss]), react_default.createElement(Wrapper3, { visible }, react_default.createElement(Backdrop, null), react_default.createElement(Content, { visible }, react_default.createElement("h1", null, "Meet your new frontend workshop"), react_default.createElement(Features, null, react_default.createElement("div", null, react_default.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: "33", height: "32" }, react_default.createElement("path", { d: "M4.06 0H32.5v28.44h-3.56V32H.5V3.56h3.56V0Zm21.33 7.11H4.06v21.33h21.33V7.11Z", fill: "currentColor" })), "Development"), react_default.createElement("div", null, react_default.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: "32", height: "32" }, react_default.createElement("path", { d: "M15.95 32c-1.85 0-3.1-1.55-3.1-3.54 0-1.1.45-2.78 1.35-5.03.9-2.3 1.35-4.51 1.35-6.81a22.21 22.21 0 0 0-5.1 3.67c-2.5 2.47-4.95 4.9-7.55 4.9-1.6 0-2.9-1.1-2.9-2.43 0-1.46 1.35-2.91 4.3-3.62 1.45-.36 3.1-.75 4.95-1.06 1.8-.31 3.8-1.02 5.9-2.08a23.77 23.77 0 0 0-6.1-2.12C5.3 13.18 2.3 12.6 1 11.28.35 10.6 0 9.9 0 9.14 0 7.82 1.2 6.8 2.95 6.8c2.65 0 5.75 3.1 7.95 5.3 1.1 1.1 2.65 2.21 4.65 3.27v-.57c0-1.77-.15-3.23-.55-4.3-.8-2.11-2.05-5.43-2.05-6.97 0-2.04 1.3-3.54 3.1-3.54 1.75 0 3.1 1.41 3.1 3.54 0 1.06-.45 2.78-1.35 5.12-.9 2.35-1.35 4.6-1.35 6.72 2.85-1.59 2.5-1.41 4.95-3.5 2.35-2.29 4-3.7 4.9-4.23.95-.58 1.9-.84 2.9-.84 1.6 0 2.8.97 2.8 2.34 0 1.5-1.25 2.78-4.15 3.62-1.4.4-3.05.75-4.9 1.1-1.9.36-3.9 1.07-6.1 2.13a23.3 23.3 0 0 0 5.95 2.08c3.65.7 6.75 1.32 8.15 2.6.7.67 1.05 1.33 1.05 2.08 0 1.33-1.2 2.43-2.95 2.43-2.95 0-6.75-4.15-8.2-5.61-.7-.7-2.2-1.72-4.4-2.96v.57c0 1.9.45 4.03 1.3 6.32.85 2.3 1.3 3.94 1.3 4.95 0 2.08-1.35 3.54-3.1 3.54Z", fill: "currentColor" })), "Testing"), react_default.createElement("div", null, react_default.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: "33", height: "32" }, react_default.createElement("path", { d: "M.5 16a16 16 0 1 1 32 0 16 16 0 0 1-32 0Zm16 12.44A12.44 12.44 0 0 1 4.3 13.53a8 8 0 1 0 9.73-9.73 12.44 12.44 0 1 1 2.47 24.64ZM12.06 16a4.44 4.44 0 1 1 0-8.89 4.44 4.44 0 0 1 0 8.89Z", fill: "currentColor", fillRule: "evenodd" })), "Documentation")), react_default.createElement(RadialButton, { onClick: dismiss }, react_default.createElement(ArrowIcon, null), react_default.createElement(ProgressCircle, { xmlns: "http://www.w3.org/2000/svg" }, react_default.createElement("circle", null)), react_default.createElement(ProgressCircle, { xmlns: "http://www.w3.org/2000/svg", progress: !0 }, react_default.createElement("circle", { strokeDashoffset: Math.PI * 48 * (1 - Math.max(0, Math.min(progress, 100)) / 100) })))));
      }, SpanHighlight = styled.span(({ theme: theme2 }) => ({ display: "inline-flex", borderRadius: 3, padding: "0 5px", marginBottom: -2, opacity: 0.8, fontFamily: theme2.typography.fonts.mono, fontSize: 11, border: theme2.base === "dark" ? theme2.color.darkest : theme2.color.lightest, color: theme2.base === "dark" ? theme2.color.lightest : theme2.color.darkest, backgroundColor: theme2.base === "dark" ? "black" : theme2.color.light, boxSizing: "border-box", lineHeight: "17px" })), CodeWrapper = styled.div(({ theme: theme2 }) => ({ background: theme2.background.content, borderRadius: 3, marginTop: 15, padding: 10, fontSize: theme2.typography.size.s1, ".linenumber": { opacity: 0.5 } })), theme = convert();
    }
  });

  // node_modules/.cache/storybook/1c3385a5d25e538d10b518b310c74d3ca2690b6aaffeadccd74da79736171f86/sb-manager/onboarding-3/manager-bundle.js
  init_define_module();
  init_define_process_env();
  init_define_process_env_NODE_PATH();

  // node_modules/@storybook/addon-onboarding/dist/manager.js
  init_define_module();
  init_define_process_env();
  init_define_process_env_NODE_PATH();
  init_react();
  init_react_dom();
  init_controls();
  init_core_events();

  // global-externals:storybook/manager-api
  init_define_module();
  init_define_process_env();
  init_define_process_env_NODE_PATH();
  var manager_api_default = __STORYBOOK_API__, { ActiveTabs, Consumer, ManagerContext, Provider, RequestResponseError, addons, combineParameters, controlOrMetaKey, controlOrMetaSymbol, eventMatchesShortcut, eventToShortcut, experimental_MockUniversalStore, experimental_UniversalStore, experimental_getStatusStore, experimental_getTestProviderStore, experimental_requestResponse, experimental_useStatusStore, experimental_useTestProviderStore, experimental_useUniversalStore, internal_fullStatusStore, internal_fullTestProviderStore, internal_universalStatusStore, internal_universalTestProviderStore, isMacLike, isShortcutTaken, keyToSymbol, merge, mockChannel, optionOrAltSymbol, shortcutMatchesShortcut, shortcutToHumanString, types, useAddonState, useArgTypes, useArgs, useChannel, useGlobalTypes, useGlobals, useParameter, useSharedState, useStoryPrepared, useStorybookApi, useStorybookState } = __STORYBOOK_API__;

  // node_modules/@storybook/addon-onboarding/dist/manager.js
  var Onboarding2 = lazy(() => Promise.resolve().then(() => (init_Onboarding_C2PY5T7U(), Onboarding_C2PY5T7U_exports)));
  addons.register("@storybook/addon-onboarding", async (api) => {
    let urlState = api.getUrlState(), isOnboarding = urlState.path === "/onboarding" || urlState.queryParams.onboarding === "true";
    api.once(STORY_SPECIFIED, () => {
      if (!(api.getData("example-button--primary") || document.getElementById("example-button--primary"))) {
        console.warn("[@storybook/addon-onboarding] It seems like you have finished the onboarding experience in Storybook! Therefore this addon is not necessary anymore and will not be loaded. You are free to remove it from your project. More info: https://github.com/storybookjs/storybook/tree/next/code/addons/onboarding#uninstalling");
        return;
      }
      if (!isOnboarding || window.innerWidth < 730) return;
      api.togglePanel(!0), api.togglePanelPosition("bottom"), api.setSelectedPanel(o);
      let domNode = document.createElement("div");
      domNode.id = "storybook-addon-onboarding", document.body.appendChild(domNode), react_dom_default.render(react_default.createElement(Suspense, { fallback: react_default.createElement("div", null) }, react_default.createElement(Onboarding2, { api })), domNode);
    });
  });
})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
