try{
(() => {
  var __defProp = Object.defineProperty;
  var __getOwnPropNames = Object.getOwnPropertyNames;
  var __esm = (fn, res) => function() {
    return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;
  };
  var __export = (target, all) => {
    for (var name in all)
      __defProp(target, name, { get: all[name], enumerable: !0 });
  };

  // <define:module>
  var init_define_module = __esm({
    "<define:module>"() {
    }
  });

  // <define:process.env>
  var init_define_process_env = __esm({
    "<define:process.env>"() {
    }
  });

  // <define:process.env.NODE_PATH>
  var init_define_process_env_NODE_PATH = __esm({
    "<define:process.env.NODE_PATH>"() {
    }
  });

  // global-externals:storybook/internal/components
  var components_exports = {};
  __export(components_exports, {
    A: () => A,
    ActionBar: () => ActionBar,
    AddonPanel: () => AddonPanel,
    Badge: () => Badge,
    Bar: () => Bar,
    Blockquote: () => Blockquote,
    Button: () => Button,
    Checkbox: () => Checkbox,
    ClipboardCode: () => ClipboardCode,
    Code: () => Code,
    DL: () => DL,
    Div: () => Div,
    DocumentWrapper: () => DocumentWrapper,
    EmptyTabContent: () => EmptyTabContent,
    ErrorFormatter: () => ErrorFormatter,
    FlexBar: () => FlexBar,
    Form: () => Form,
    H1: () => H1,
    H2: () => H2,
    H3: () => H3,
    H4: () => H4,
    H5: () => H5,
    H6: () => H6,
    HR: () => HR,
    IconButton: () => IconButton,
    Img: () => Img,
    LI: () => LI,
    Link: () => Link,
    ListItem: () => ListItem,
    Loader: () => Loader,
    Modal: () => Modal,
    OL: () => OL,
    P: () => P,
    Placeholder: () => Placeholder,
    Pre: () => Pre,
    ProgressSpinner: () => ProgressSpinner,
    ResetWrapper: () => ResetWrapper,
    ScrollArea: () => ScrollArea,
    Separator: () => Separator,
    Spaced: () => Spaced,
    Span: () => Span,
    StorybookIcon: () => StorybookIcon,
    StorybookLogo: () => StorybookLogo,
    SyntaxHighlighter: () => SyntaxHighlighter,
    TT: () => TT,
    TabBar: () => TabBar,
    TabButton: () => TabButton,
    TabWrapper: () => TabWrapper,
    Table: () => Table,
    Tabs: () => Tabs,
    TabsState: () => TabsState,
    TooltipLinkList: () => TooltipLinkList,
    TooltipMessage: () => TooltipMessage,
    TooltipNote: () => TooltipNote,
    UL: () => UL,
    WithTooltip: () => WithTooltip,
    WithTooltipPure: () => WithTooltipPure,
    Zoom: () => Zoom,
    codeCommon: () => codeCommon,
    components: () => components,
    createCopyToClipboardFunction: () => createCopyToClipboardFunction,
    default: () => components_default,
    getStoryHref: () => getStoryHref,
    interleaveSeparators: () => interleaveSeparators,
    nameSpaceClassNames: () => nameSpaceClassNames,
    resetComponents: () => resetComponents,
    withReset: () => withReset
  });
  var components_default, A, ActionBar, AddonPanel, Badge, Bar, Blockquote, Button, Checkbox, ClipboardCode, Code, DL, Div, DocumentWrapper, EmptyTabContent, ErrorFormatter, FlexBar, Form, H1, H2, H3, H4, H5, H6, HR, IconButton, Img, LI, Link, ListItem, Loader, Modal, OL, P, Placeholder, Pre, ProgressSpinner, ResetWrapper, ScrollArea, Separator, Spaced, Span, StorybookIcon, StorybookLogo, SyntaxHighlighter, TT, TabBar, TabButton, TabWrapper, Table, Tabs, TabsState, TooltipLinkList, TooltipMessage, TooltipNote, UL, WithTooltip, WithTooltipPure, Zoom, codeCommon, components, createCopyToClipboardFunction, getStoryHref, interleaveSeparators, nameSpaceClassNames, resetComponents, withReset, init_components = __esm({
    "global-externals:storybook/internal/components"() {
      init_define_module();
      init_define_process_env();
      init_define_process_env_NODE_PATH();
      components_default = __STORYBOOK_COMPONENTS__, { A, ActionBar, AddonPanel, Badge, Bar, Blockquote, Button, Checkbox, ClipboardCode, Code, DL, Div, DocumentWrapper, EmptyTabContent, ErrorFormatter, FlexBar, Form, H1, H2, H3, H4, H5, H6, HR, IconButton, Img, LI, Link, ListItem, Loader, Modal, OL, P, Placeholder, Pre, ProgressSpinner, ResetWrapper, ScrollArea, Separator, Spaced, Span, StorybookIcon, StorybookLogo, SyntaxHighlighter, TT, TabBar, TabButton, TabWrapper, Table, Tabs, TabsState, TooltipLinkList, TooltipMessage, TooltipNote, UL, WithTooltip, WithTooltipPure, Zoom, codeCommon, components, createCopyToClipboardFunction, getStoryHref, interleaveSeparators, nameSpaceClassNames, resetComponents, withReset } = __STORYBOOK_COMPONENTS__;
    }
  });

  // node_modules/.cache/storybook/1c3385a5d25e538d10b518b310c74d3ca2690b6aaffeadccd74da79736171f86/sb-manager/a11y-4/manager-bundle.js
  init_define_module();
  init_define_process_env();
  init_define_process_env_NODE_PATH();

  // node_modules/@storybook/addon-a11y/dist/manager.js
  init_define_module();
  init_define_process_env();
  init_define_process_env_NODE_PATH();

  // global-externals:react
  init_define_module();
  init_define_process_env();
  init_define_process_env_NODE_PATH();
  var react_default = __REACT__, { Children, Component, Fragment, Profiler, PureComponent, StrictMode, Suspense, __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED, act, cloneElement, createContext, createElement, createFactory, createRef, forwardRef, isValidElement, lazy, memo, startTransition, unstable_act, useCallback, useContext, useDebugValue, useDeferredValue, useEffect, useId, useImperativeHandle, useInsertionEffect, useLayoutEffect, useMemo, useReducer, useRef, useState, useSyncExternalStore, useTransition, version } = __REACT__;

  // node_modules/@storybook/addon-a11y/dist/manager.js
  init_components();

  // global-externals:storybook/manager-api
  init_define_module();
  init_define_process_env();
  init_define_process_env_NODE_PATH();
  var manager_api_default = __STORYBOOK_API__, { ActiveTabs, Consumer, ManagerContext, Provider, RequestResponseError, addons, combineParameters, controlOrMetaKey, controlOrMetaSymbol, eventMatchesShortcut, eventToShortcut, experimental_MockUniversalStore, experimental_UniversalStore, experimental_getStatusStore, experimental_getTestProviderStore, experimental_requestResponse, experimental_useStatusStore, experimental_useTestProviderStore, experimental_useUniversalStore, internal_fullStatusStore, internal_fullTestProviderStore, internal_universalStatusStore, internal_universalTestProviderStore, isMacLike, isShortcutTaken, keyToSymbol, merge, mockChannel, optionOrAltSymbol, shortcutMatchesShortcut, shortcutToHumanString, types, useAddonState, useArgTypes, useArgs, useChannel, useGlobalTypes, useGlobals, useParameter, useSharedState, useStoryPrepared, useStorybookApi, useStorybookState } = __STORYBOOK_API__;

  // global-externals:@storybook/icons
  init_define_module();
  init_define_process_env();
  init_define_process_env_NODE_PATH();
  var icons_default = __STORYBOOK_ICONS__, { AccessibilityAltIcon, AccessibilityIcon, AccessibilityIgnoredIcon, AddIcon, AdminIcon, AlertAltIcon, AlertIcon, AlignLeftIcon, AlignRightIcon, AppleIcon, ArrowBottomLeftIcon, ArrowBottomRightIcon, ArrowDownIcon, ArrowLeftIcon, ArrowRightIcon, ArrowSolidDownIcon, ArrowSolidLeftIcon, ArrowSolidRightIcon, ArrowSolidUpIcon, ArrowTopLeftIcon, ArrowTopRightIcon, ArrowUpIcon, AzureDevOpsIcon, BackIcon, BasketIcon, BatchAcceptIcon, BatchDenyIcon, BeakerIcon, BellIcon, BitbucketIcon, BoldIcon, BookIcon, BookmarkHollowIcon, BookmarkIcon, BottomBarIcon, BottomBarToggleIcon, BoxIcon, BranchIcon, BrowserIcon, ButtonIcon, CPUIcon, CalendarIcon, CameraIcon, CameraStabilizeIcon, CategoryIcon, CertificateIcon, ChangedIcon, ChatIcon, CheckIcon, ChevronDownIcon, ChevronLeftIcon, ChevronRightIcon, ChevronSmallDownIcon, ChevronSmallLeftIcon, ChevronSmallRightIcon, ChevronSmallUpIcon, ChevronUpIcon, ChromaticIcon, ChromeIcon, CircleHollowIcon, CircleIcon, ClearIcon, CloseAltIcon, CloseIcon, CloudHollowIcon, CloudIcon, CogIcon, CollapseIcon, CommandIcon, CommentAddIcon, CommentIcon, CommentsIcon, CommitIcon, CompassIcon, ComponentDrivenIcon, ComponentIcon, ContrastIcon, ContrastIgnoredIcon, ControlsIcon, CopyIcon, CreditIcon, CrossIcon, DashboardIcon, DatabaseIcon, DeleteIcon, DiamondIcon, DirectionIcon, DiscordIcon, DocChartIcon, DocListIcon, DocumentIcon, DownloadIcon, DragIcon, EditIcon, EllipsisIcon, EmailIcon, ExpandAltIcon, ExpandIcon, EyeCloseIcon, EyeIcon, FaceHappyIcon, FaceNeutralIcon, FaceSadIcon, FacebookIcon, FailedIcon, FastForwardIcon, FigmaIcon, FilterIcon, FlagIcon, FolderIcon, FormIcon, GDriveIcon, GithubIcon, GitlabIcon, GlobeIcon, GoogleIcon, GraphBarIcon, GraphLineIcon, GraphqlIcon, GridAltIcon, GridIcon, GrowIcon, HeartHollowIcon, HeartIcon, HomeIcon, HourglassIcon, InfoIcon, ItalicIcon, JumpToIcon, KeyIcon, LightningIcon, LightningOffIcon, LinkBrokenIcon, LinkIcon, LinkedinIcon, LinuxIcon, ListOrderedIcon, ListUnorderedIcon, LocationIcon, LockIcon, MarkdownIcon, MarkupIcon, MediumIcon, MemoryIcon, MenuIcon, MergeIcon, MirrorIcon, MobileIcon, MoonIcon, NutIcon, OutboxIcon, OutlineIcon, PaintBrushIcon, PaperClipIcon, ParagraphIcon, PassedIcon, PhoneIcon, PhotoDragIcon, PhotoIcon, PhotoStabilizeIcon, PinAltIcon, PinIcon, PlayAllHollowIcon, PlayBackIcon, PlayHollowIcon, PlayIcon, PlayNextIcon, PlusIcon, PointerDefaultIcon, PointerHandIcon, PowerIcon, PrintIcon, ProceedIcon, ProfileIcon, PullRequestIcon, QuestionIcon, RSSIcon, RedirectIcon, ReduxIcon, RefreshIcon, ReplyIcon, RepoIcon, RequestChangeIcon, RewindIcon, RulerIcon, SaveIcon, SearchIcon, ShareAltIcon, ShareIcon, ShieldIcon, SideBySideIcon, SidebarAltIcon, SidebarAltToggleIcon, SidebarIcon, SidebarToggleIcon, SpeakerIcon, StackedIcon, StarHollowIcon, StarIcon, StatusFailIcon, StatusIcon, StatusPassIcon, StatusWarnIcon, StickerIcon, StopAltHollowIcon, StopAltIcon, StopIcon, StorybookIcon: StorybookIcon2, StructureIcon, SubtractIcon, SunIcon, SupportIcon, SweepIcon, SwitchAltIcon, SyncIcon, TabletIcon, ThumbsUpIcon, TimeIcon, TimerIcon, TransferIcon, TrashIcon, TwitterIcon, TypeIcon, UbuntuIcon, UndoIcon, UnfoldIcon, UnlockIcon, UnpinIcon, UploadIcon, UserAddIcon, UserAltIcon, UserIcon, UsersIcon, VSCodeIcon, VerifiedIcon, VideoIcon, WandIcon, WatchIcon, WindowsIcon, WrenchIcon, XIcon, YoutubeIcon, ZoomIcon, ZoomOutIcon, ZoomResetIcon, iconList } = __STORYBOOK_ICONS__;

  // global-externals:storybook/theming
  init_define_module();
  init_define_process_env();
  init_define_process_env_NODE_PATH();
  var theming_default = __STORYBOOK_THEMING__, { CacheProvider, ClassNames, Global, ThemeProvider, background, color, convert, create, createCache, createGlobal, createReset, css, darken, ensure, ignoreSsrWarning, isPropValid, jsx, keyframes, lighten, styled, themes, typography, useTheme, withTheme } = __STORYBOOK_THEMING__;

  // global-externals:storybook/internal/core-events
  init_define_module();
  init_define_process_env();
  init_define_process_env_NODE_PATH();
  var core_events_default = __STORYBOOK_CORE_EVENTS__, { ARGTYPES_INFO_REQUEST, ARGTYPES_INFO_RESPONSE, CHANNEL_CREATED, CHANNEL_WS_DISCONNECT, CONFIG_ERROR, CREATE_NEW_STORYFILE_REQUEST, CREATE_NEW_STORYFILE_RESPONSE, CURRENT_STORY_WAS_SET, DOCS_PREPARED, DOCS_RENDERED, FILE_COMPONENT_SEARCH_REQUEST, FILE_COMPONENT_SEARCH_RESPONSE, FORCE_REMOUNT, FORCE_RE_RENDER, GLOBALS_UPDATED, NAVIGATE_URL, PLAY_FUNCTION_THREW_EXCEPTION, PRELOAD_ENTRIES, PREVIEW_BUILDER_PROGRESS, PREVIEW_KEYDOWN, REGISTER_SUBSCRIPTION, REQUEST_WHATS_NEW_DATA, RESET_STORY_ARGS, RESULT_WHATS_NEW_DATA, SAVE_STORY_REQUEST, SAVE_STORY_RESPONSE, SELECT_STORY, SET_CONFIG, SET_CURRENT_STORY, SET_FILTER, SET_GLOBALS, SET_INDEX, SET_STORIES, SET_WHATS_NEW_CACHE, SHARED_STATE_CHANGED, SHARED_STATE_SET, STORIES_COLLAPSE_ALL, STORIES_EXPAND_ALL, STORY_ARGS_UPDATED, STORY_CHANGED, STORY_ERRORED, STORY_FINISHED, STORY_HOT_UPDATED, STORY_INDEX_INVALIDATED, STORY_MISSING, STORY_PREPARED, STORY_RENDERED, STORY_RENDER_PHASE_CHANGED, STORY_SPECIFIED, STORY_THREW_EXCEPTION, STORY_UNCHANGED, TELEMETRY_ERROR, TOGGLE_WHATS_NEW_NOTIFICATIONS, UNHANDLED_ERRORS_WHILE_PLAYING, UPDATE_GLOBALS, UPDATE_QUERY_PARAMS, UPDATE_STORY_ARGS } = __STORYBOOK_CORE_EVENTS__;

  // node_modules/storybook/dist/highlight/index.js
  init_define_module();
  init_define_process_env();
  init_define_process_env_NODE_PATH();
  var t = "storybook/highlight", o = `${t}/add`, e = `${t}/remove`, H = `${t}/reset`, I = `${t}/scroll-into-view`;

  // global-externals:react-dom
  init_define_module();
  init_define_process_env();
  init_define_process_env_NODE_PATH();
  var react_dom_default = __REACT_DOM__, { __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED2, createPortal, createRoot, findDOMNode, flushSync, hydrate, hydrateRoot, render, unmountComponentAtNode, unstable_batchedUpdates, unstable_renderSubtreeIntoContainer, version: version2 } = __REACT_DOM__;

  // node_modules/@storybook/addon-a11y/dist/manager.js
  var RuleType = { VIOLATION: "violations", PASS: "passes", INCOMPLETION: "incomplete" }, axeRuleMapping_wcag_2_0_a_aa = { "area-alt": { title: "<area> alt text", axeSummary: "Ensure <area> elements of image maps have alternative text", friendlySummary: "Add alt text to all <area> elements of image maps." }, "aria-allowed-attr": { title: "Supported ARIA attributes", axeSummary: "Ensure an element's role supports its ARIA attributes", friendlySummary: "Only use ARIA attributes that are permitted for the element's role." }, "aria-braille-equivalent": { title: "Braille equivalent", axeSummary: "Ensure aria-braillelabel and aria-brailleroledescription have a non-braille equivalent", friendlySummary: "If you use braille ARIA labels, also provide a matching non-braille label." }, "aria-command-name": { title: "ARIA command name", axeSummary: "Ensure every ARIA button, link and menuitem has an accessible name", friendlySummary: "Every ARIA button, link, or menuitem needs a label or accessible name." }, "aria-conditional-attr": { title: "ARIA attribute valid for role", axeSummary: "Ensure ARIA attributes are used as described in the specification of the element's role", friendlySummary: "Follow the element role's specification when using ARIA attributes." }, "aria-deprecated-role": { title: "Deprecated ARIA role", axeSummary: "Ensure elements do not use deprecated roles", friendlySummary: "Don't use deprecated ARIA roles on elements." }, "aria-hidden-body": { title: "Hidden body", axeSummary: 'Ensure aria-hidden="true" is not present on the document <body>', friendlySummary: 'Never set aria-hidden="true" on the <body> element.' }, "aria-hidden-focus": { title: "Hidden element focus", axeSummary: "Ensure aria-hidden elements are not focusable nor contain focusable elements", friendlySummary: "Elements marked hidden (aria-hidden) should not be focusable or contain focusable items." }, "aria-input-field-name": { title: "ARIA input field name", axeSummary: "Ensure every ARIA input field has an accessible name", friendlySummary: "Give each ARIA text input or field a label or accessible name." }, "aria-meter-name": { title: "ARIA meter name", axeSummary: "Ensure every ARIA meter node has an accessible name", friendlySummary: 'Give each element with role="meter" a label or accessible name.' }, "aria-progressbar-name": { title: "ARIA progressbar name", axeSummary: "Ensure every ARIA progressbar node has an accessible name", friendlySummary: 'Give each element with role="progressbar" a label or accessible name.' }, "aria-prohibited-attr": { title: "ARIA prohibited attributes", axeSummary: "Ensure ARIA attributes are not prohibited for an element's role", friendlySummary: "Don't use ARIA attributes that are forbidden for that element's role." }, "aria-required-attr": { title: "ARIA required attributes", axeSummary: "Ensure elements with ARIA roles have all required ARIA attributes", friendlySummary: "Include all required ARIA attributes for elements with that ARIA role." }, "aria-required-children": { title: "ARIA required children", axeSummary: "Ensure elements with an ARIA role that require child roles contain them", friendlySummary: "If an ARIA role requires specific child roles, include those child elements." }, "aria-required-parent": { title: "ARIA required parent", axeSummary: "Ensure elements with an ARIA role that require parent roles are contained by them", friendlySummary: "Place elements with certain ARIA roles inside the required parent role element." }, "aria-roles": { title: "ARIA role value", axeSummary: "Ensure all elements with a role attribute use a valid value", friendlySummary: "Use only valid values in the role attribute (no typos or invalid roles)." }, "aria-toggle-field-name": { title: "ARIA toggle field name", axeSummary: "Ensure every ARIA toggle field has an accessible name", friendlySummary: "Every ARIA toggle field (elements with the checkbox, radio, or switch roles) needs an accessible name." }, "aria-tooltip-name": { title: "ARIA tooltip name", axeSummary: "Ensure every ARIA tooltip node has an accessible name", friendlySummary: 'Give each element with role="tooltip" a descriptive accessible name.' }, "aria-valid-attr-value": { title: "ARIA attribute values valid", axeSummary: "Ensure all ARIA attributes have valid values", friendlySummary: "Use only valid values for ARIA attributes (no typos or invalid values)." }, "aria-valid-attr": { title: "ARIA attribute valid", axeSummary: "Ensure attributes that begin with aria- are valid ARIA attributes", friendlySummary: "Use only valid aria-* attributes (make sure the attribute name is correct)." }, blink: { title: "<blink> element", axeSummary: "Ensure <blink> elements are not used", friendlySummary: "Don't use the deprecated <blink> element." }, "button-name": { title: "Button name", axeSummary: "Ensure buttons have discernible text", friendlySummary: "Every <button> needs a visible label or accessible name." }, bypass: { title: "Navigation bypass", axeSummary: "Ensure each page has at least one mechanism to bypass navigation and jump to content", friendlySummary: 'Provide a way to skip repetitive navigation (e.g. a "Skip to content" link).' }, "color-contrast": { title: "Color contrast", axeSummary: "Ensure the contrast between foreground and background text meets WCAG 2 AA minimum thresholds", friendlySummary: "The color contrast between text and its background meets WCAG AA contrast ratio." }, "definition-list": { title: "Definition list structure", axeSummary: "Ensure <dl> elements are structured correctly", friendlySummary: "Definition lists (<dl>) should directly contain <dt> and <dd> elements in order." }, dlitem: { title: "Definition list items", axeSummary: "Ensure <dt> and <dd> elements are contained by a <dl>", friendlySummary: "Ensure <dt> and <dd> elements are contained by a <dl>" }, "document-title": { title: "Document title", axeSummary: "Ensure each HTML document contains a non-empty <title> element", friendlySummary: "Include a non-empty <title> element for every page." }, "duplicate-id-aria": { title: "Unique id", axeSummary: "Ensure every id attribute value used in ARIA and in labels is unique", friendlySummary: "Every id used for ARIA or form labels should be unique on the page." }, "form-field-multiple-labels": { title: "Multiple form field labels", axeSummary: "Ensure a form field does not have multiple <label> elements", friendlySummary: "Don't give a single form field more than one <label>." }, "frame-focusable-content": { title: "Focusable frames", axeSummary: 'Ensure <frame> and <iframe> with focusable content do not have tabindex="-1"', friendlySummary: `Don't set tabindex="-1" on a <frame> or <iframe> that contains focusable elements.` }, "frame-title-unique": { title: "Unique frame title", axeSummary: "Ensure <iframe> and <frame> elements contain a unique title attribute", friendlySummary: "Use a unique title attribute for each <frame> or <iframe> on the page." }, "frame-title": { title: "Frame title", axeSummary: "Ensure <iframe> and <frame> elements have an accessible name", friendlySummary: "Every <frame> and <iframe> needs a title or accessible name." }, "html-has-lang": { title: "<html> has lang", axeSummary: "Ensure every HTML document has a lang attribute", friendlySummary: "Add a lang attribute to the <html> element." }, "html-lang-valid": { title: "<html> lang valid", axeSummary: "Ensure the <html lang> attribute has a valid value", friendlySummary: "Use a valid language code in the <html lang> attribute." }, "html-xml-lang-mismatch": { title: "HTML and XML lang mismatch", axeSummary: "Ensure that HTML elements with both lang and xml:lang agree on the page's language", friendlySummary: "If using both lang and xml:lang on <html>, make sure they are the same language." }, "image-alt": { title: "Image alt text", axeSummary: "Ensure <img> elements have alternative text or a role of none/presentation", friendlySummary: 'Give every image alt text or mark it as decorative with alt="".' }, "input-button-name": { title: "Input button name", axeSummary: "Ensure input buttons have discernible text", friendlySummary: 'Give each <input type="button"> or similar a clear label (text or aria-label).' }, "input-image-alt": { title: "Input image alt", axeSummary: 'Ensure <input type="image"> elements have alternative text', friendlySummary: '<input type="image"> must have alt text describing its image.' }, label: { title: "Form label", axeSummary: "Ensure every form element has a label", friendlySummary: "Every form field needs an associated label." }, "link-in-text-block": { title: "Identifiable links", axeSummary: "Ensure links are distinguishable from surrounding text without relying on color", friendlySummary: "Make sure links are obviously identifiable without relying only on color." }, "link-name": { title: "Link name", axeSummary: "Ensure links have discernible text", friendlySummary: "Give each link meaningful text or an aria-label so its purpose is clear." }, list: { title: "List structure", axeSummary: "Ensure that lists are structured correctly", friendlySummary: "Use proper list structure. Only use <li> inside <ul> or <ol>." }, listitem: { title: "List item", axeSummary: "Ensure <li> elements are used semantically", friendlySummary: "Only use <li> tags inside <ul> or <ol> lists." }, marquee: { title: "<marquee> element", axeSummary: "Ensure <marquee> elements are not used", friendlySummary: "Don't use the deprecated <marquee> element." }, "meta-refresh": { title: "<meta> refresh", axeSummary: 'Ensure <meta http-equiv="refresh"> is not used for delayed refresh', friendlySummary: 'Avoid auto-refreshing or redirecting pages using <meta http-equiv="refresh">.' }, "meta-viewport": { title: "<meta> viewport scaling", axeSummary: 'Ensure <meta name="viewport"> does not disable text scaling and zooming', friendlySummary: `Don't disable user zooming in <meta name="viewport"> to allow scaling.` }, "nested-interactive": { title: "Nested interactive controls", axeSummary: "Ensure interactive controls are not nested (nesting causes screen reader/focus issues)", friendlySummary: "Do not nest interactive elements; it can confuse screen readers and keyboard focus." }, "no-autoplay-audio": { title: "Autoplaying video", axeSummary: "Ensure <video> or <audio> do not autoplay audio > 3 seconds without a control to stop/mute", friendlySummary: "Don't autoplay audio for more than 3 seconds without providing a way to stop or mute it." }, "object-alt": { title: "<object> alt text", axeSummary: "Ensure <object> elements have alternative text", friendlySummary: "Provide alternative text or content for <object> elements." }, "role-img-alt": { title: 'role="img" alt text', axeSummary: 'Ensure elements with role="img" have alternative text', friendlySummary: 'Any element with role="img" needs alt text.' }, "scrollable-region-focusable": { title: "Scrollable element focusable", axeSummary: "Ensure elements with scrollable content are keyboard-accessible", friendlySummary: "If an area can scroll, ensure it can be focused and scrolled via keyboard." }, "select-name": { title: "<select> name", axeSummary: "Ensure <select> elements have an accessible name", friendlySummary: "Give each <select> field a label or other accessible name." }, "server-side-image-map": { title: "Server-side image map", axeSummary: "Ensure that server-side image maps are not used", friendlySummary: "Don't use server-side image maps." }, "svg-img-alt": { title: "SVG image alt text", axeSummary: "Ensure <svg> images/graphics have accessible text", friendlySummary: 'SVG images with role="img" or similar need a text description.' }, "td-headers-attr": { title: "Table headers attribute", axeSummary: "Ensure each cell in a table using headers only refers to <th> in that table", friendlySummary: "In tables using the headers attribute, only reference other cells in the same table." }, "th-has-data-cells": { title: "<th> has data cell", axeSummary: "Ensure <th> (or header role) elements have data cells they describe", friendlySummary: "Every table header (<th> or header role) should correspond to at least one data cell." }, "valid-lang": { title: "Valid lang", axeSummary: "Ensure lang attributes have valid values", friendlySummary: "Use valid language codes in all lang attributes." }, "video-caption": { title: "<video> captions", axeSummary: "Ensure <video> elements have captions", friendlySummary: "Provide captions for all <video> content." } }, axeRuleMapping_wcag_2_1_a_aa = { "autocomplete-valid": { title: "autocomplete attribute valid", axeSummary: "Ensure the autocomplete attribute is correct and suitable for the form field", friendlySummary: "Use valid autocomplete values that match the form field's purpose." }, "avoid-inline-spacing": { title: "Forced inline spacing", axeSummary: "Ensure that text spacing set via inline styles can be adjusted with custom CSS", friendlySummary: "Don't lock in text spacing with forced (!important) inline styles\u2014allow user CSS to adjust text spacing." } }, axeRuleMapping_wcag_2_2_a_aa = { "target-size": { title: "Touch target size", axeSummary: "Ensure touch targets have sufficient size and space", friendlySummary: "Make sure interactive elements are big enough and not too close together for touch." } }, axeRuleMapping_best_practices = { accesskeys: { title: "Unique accesskey", axeSummary: "Ensure every accesskey attribute value is unique", friendlySummary: "Use unique values for all accesskey attributes." }, "aria-allowed-role": { title: "Appropriate role value", axeSummary: "Ensure the role attribute has an appropriate value for the element", friendlySummary: "ARIA roles should have a valid value for the element." }, "aria-dialog-name": { title: "ARIA dialog name", axeSummary: "Ensure every ARIA dialog and alertdialog has an accessible name", friendlySummary: "Give each ARIA dialog or alertdialog a title or accessible name." }, "aria-text": { title: 'ARIA role="text"', axeSummary: 'Ensure role="text" is used on elements with no focusable descendants', friendlySummary: `Only use role="text" on elements that don't contain focusable elements.` }, "aria-treeitem-name": { title: "ARIA treeitem name", axeSummary: "Ensure every ARIA treeitem node has an accessible name", friendlySummary: "Give each ARIA treeitem a label or accessible name." }, "empty-heading": { title: "Empty heading", axeSummary: "Ensure headings have discernible text", friendlySummary: "Don't leave heading elements empty or hide them." }, "empty-table-header": { title: "Empty table header", axeSummary: "Ensure table headers have discernible text", friendlySummary: "Make sure table header cells have visible text." }, "frame-tested": { title: "Test all frames", axeSummary: "Ensure <iframe> and <frame> elements contain the axe-core script", friendlySummary: "Make sure axe-core is injected into all frames or iframes so they are tested." }, "heading-order": { title: "Heading order", axeSummary: "Ensure the order of headings is semantically correct (no skipping levels)", friendlySummary: "Use proper heading order (don't skip heading levels)." }, "image-redundant-alt": { title: "Redundant image alt text", axeSummary: "Ensure image alternative text is not repeated as nearby text", friendlySummary: "Avoid repeating the same information in both an image's alt text and nearby text." }, "label-title-only": { title: "Visible form element label", axeSummary: "Ensure each form element has a visible label (not only title/ARIA)", friendlySummary: "Every form input needs a visible label (not only a title attribute or hidden text)." }, "landmark-banner-is-top-level": { title: "Top-level landmark banner", axeSummary: "Ensure the banner landmark is at top level (not nested)", friendlySummary: "Use the banner landmark (e.g. site header) only at the top level of the page, not inside another landmark." }, "landmark-complementary-is-top-level": { title: "Top-level <aside>", axeSummary: "Ensure the complementary landmark (<aside>) is top level", friendlySummary: 'The complementary landmark <aside> or role="complementary" should be a top-level region, not nested in another landmark.' }, "landmark-contentinfo-is-top-level": { title: "Top-level contentinfo", axeSummary: "Ensure the contentinfo landmark (footer) is top level", friendlySummary: "Make sure the contentinfo landmark (footer) is at the top level of the page and not contained in another landmark." }, "landmark-main-is-top-level": { title: "Top-level main", axeSummary: "Ensure the main landmark is at top level", friendlySummary: "The main landmark should be a top-level element and not nested inside another landmark." }, "landmark-no-duplicate-banner": { title: "Duplicate banner landmark", axeSummary: "Ensure the document has at most one banner landmark", friendlySummary: 'Have only one role="banner" or <header> on a page.' }, "landmark-no-duplicate-contentinfo": { title: "Duplicate contentinfo", axeSummary: "Ensure the document has at most one contentinfo landmark", friendlySummary: 'Have only one role="contentinfo" or <footer> on a page.' }, "landmark-no-duplicate-main": { title: "Duplicate main", axeSummary: "Ensure the document has at most one main landmark", friendlySummary: 'Have only one role="main" or <main> on a page.' }, "landmark-one-main": { title: "main landmark", axeSummary: "Ensure the document has a main landmark", friendlySummary: 'Include a main landmark on each page using a <main> region or role="main".' }, "landmark-unique": { title: "Unique landmark", axeSummary: "Ensure landmarks have a unique role or role/label combination", friendlySummary: "If you use multiple landmarks of the same type, give them unique labels (names)." }, "meta-viewport-large": { title: "Significant viewport scaling", axeSummary: 'Ensure <meta name="viewport"> can scale a significant amount (e.g. 500%)', friendlySummary: '<meta name="viewport"> should allow users to significantly scale content.' }, "page-has-heading-one": { title: "Has <h1>", axeSummary: "Ensure the page (or at least one frame) contains a level-one heading", friendlySummary: "Every page or frame should have at least one <h1> heading." }, "presentation-role-conflict": { title: "Presentational content", axeSummary: 'Ensure elements with role="presentation"/"none" have no ARIA or tabindex', friendlySummary: `Don't give elements with role="none"/"presentation" any ARIA attributes or a tabindex.` }, region: { title: "Landmark regions", axeSummary: "Ensure all page content is contained by landmarks", friendlySummary: "Wrap all page content in appropriate landmark regions (<header>, <main>, <footer>, etc.)." }, "scope-attr-valid": { title: "scope attribute", axeSummary: "Ensure the scope attribute is used correctly on tables", friendlySummary: "Use the scope attribute only on <th> elements, with proper values (col, row, etc.)." }, "skip-link": { title: "Skip link", axeSummary: 'Ensure all "skip" links have a focusable target', friendlySummary: 'Make sure any "skip to content" link targets an existing, focusable element.' }, tabindex: { title: "tabindex values", axeSummary: "Ensure tabindex attribute values are not greater than 0", friendlySummary: "Don't use tabindex values greater than 0." }, "table-duplicate-name": { title: "Duplicate names for table", axeSummary: "Ensure the <caption> does not duplicate the summary attribute text", friendlySummary: "Don't use the same text in both a table's <caption> and its summary attribute." } }, axeRuleMapping_wcag_2_x_aaa = { "color-contrast-enhanced": { title: "Enhanced color contrast", axeSummary: "Ensure contrast between text and background meets WCAG 2 AAA enhanced contrast thresholds", friendlySummary: "Use extra-high contrast for text and background to meet WCAG AAA level." }, "identical-links-same-purpose": { title: "Same link name, same purpose", axeSummary: "Ensure links with the same accessible name serve a similar purpose", friendlySummary: "If two links have the same text, they should do the same thing (lead to the same content)." }, "meta-refresh-no-exceptions": { title: 'No <meta http-equiv="refresh">', axeSummary: 'Ensure <meta http-equiv="refresh"> is not used for delayed refresh (no exceptions)', friendlySummary: `Don't auto-refresh or redirect pages using <meta http-equiv="refresh"> even with a delay.` } }, axeRuleMapping_experimental = { "css-orientation-lock": { title: "CSS orientation lock", axeSummary: "Ensure content is not locked to a specific display orientation (works in all orientations)", friendlySummary: "Don't lock content to one screen orientation; support both portrait and landscape modes." }, "focus-order-semantics": { title: "Focus order semantic role", axeSummary: "Ensure elements in the tab order have a role appropriate for interactive content", friendlySummary: "Ensure elements in the tab order have a role appropriate for interactive content" }, "hidden-content": { title: "Hidden content", axeSummary: "Informs users about hidden content", friendlySummary: "Display hidden content on the page for test analysis." }, "label-content-name-mismatch": { title: "Content name mismatch", axeSummary: "Ensure elements labeled by their content include that text in their accessible name", friendlySummary: "If an element's visible text serves as its label, include that text in its accessible name." }, "p-as-heading": { title: "No <p> headings", axeSummary: "Ensure <p> elements aren't styled to look like headings (use real headings)", friendlySummary: "Don't just style a <p> to look like a heading \u2013 use an actual heading tag for headings." }, "table-fake-caption": { title: "Table caption", axeSummary: "Ensure that tables with a caption use the <caption> element", friendlySummary: "Use a <caption> element for table captions instead of just styled text." }, "td-has-header": { title: "<td> has header", axeSummary: "Ensure each non-empty data cell in large tables (3\xD73+) has one or more headers", friendlySummary: "Every data cell in large tables should be associated with at least one header cell." } }, axeRuleMapping_deprecated = { "aria-roledescription": { title: "aria-roledescription", axeSummary: "Ensure aria-roledescription is only used on elements with an implicit or explicit role", friendlySummary: "Only use aria-roledescription on elements that already have a defined role." } }, combinedRulesMap = { ...axeRuleMapping_wcag_2_0_a_aa, ...axeRuleMapping_wcag_2_1_a_aa, ...axeRuleMapping_wcag_2_2_a_aa, ...axeRuleMapping_wcag_2_x_aaa, ...axeRuleMapping_best_practices, ...axeRuleMapping_experimental, ...axeRuleMapping_deprecated }, getTitleForAxeResult = (axeResult) => combinedRulesMap[axeResult.id]?.title || axeResult.id, getFriendlySummaryForAxeResult = (axeResult) => combinedRulesMap[axeResult.id]?.friendlySummary || axeResult.description, ADDON_ID = "storybook/a11y", PANEL_ID = `${ADDON_ID}/panel`, PARAM_KEY = "a11y", RESULT = `${ADDON_ID}/result`, REQUEST = `${ADDON_ID}/request`, RUNNING = `${ADDON_ID}/running`, ERROR = `${ADDON_ID}/error`, MANUAL = `${ADDON_ID}/manual`, SELECT = `${ADDON_ID}/select`, DOCUMENTATION_LINK = "writing-tests/accessibility-testing", DOCUMENTATION_DISCREPANCY_LINK = `${DOCUMENTATION_LINK}#why-are-my-tests-failing-in-different-environments`, EVENTS = { RESULT, REQUEST, RUNNING, ERROR, MANUAL, SELECT }, STATUS_TYPE_ID_COMPONENT_TEST = "storybook/component-test", STATUS_TYPE_ID_A11Y = "storybook/a11y", unhighlightedSelectors = ["html", "body", "main"], theme = convert(themes.light), colorsByType = { [RuleType.VIOLATION]: theme.color.negative, [RuleType.PASS]: theme.color.positive, [RuleType.INCOMPLETION]: theme.color.warning }, A11yContext = createContext({ parameters: {}, results: void 0, highlighted: !1, toggleHighlight: () => {
  }, tab: RuleType.VIOLATION, handleCopyLink: () => {
  }, setTab: () => {
  }, setStatus: () => {
  }, status: "initial", error: void 0, handleManual: () => {
  }, discrepancy: null, selectedItems: /* @__PURE__ */ new Map(), allExpanded: !1, toggleOpen: () => {
  }, handleCollapseAll: () => {
  }, handleExpandAll: () => {
  }, handleJumpToElement: () => {
  }, handleSelectionChange: () => {
  } }), A11yContextProvider = (props) => {
    let parameters = useParameter("a11y", {}), [globals] = useGlobals() ?? [], api = useStorybookApi(), getInitialStatus = useCallback((manual2 = !1) => manual2 ? "manual" : "initial", []), manual = useMemo(() => globals?.a11y?.manual ?? !1, [globals?.a11y?.manual]), a11ySelection = useMemo(() => {
      let value = api.getQueryParam("a11ySelection");
      return value && api.setQueryParams({ a11ySelection: "" }), value;
    }, [api]), [results, setResults] = useAddonState(ADDON_ID), [tab, setTab] = useState(() => {
      let [type] = a11ySelection?.split(".") ?? [];
      return type && Object.values(RuleType).includes(type) ? type : RuleType.VIOLATION;
    }), [error, setError] = useState(void 0), [status, setStatus] = useState(getInitialStatus(manual)), [highlighted, setHighlighted] = useState(!!a11ySelection), { storyId } = useStorybookState(), currentStoryA11yStatusValue = experimental_useStatusStore((allStatuses) => allStatuses[storyId]?.[STATUS_TYPE_ID_A11Y]?.value);
    useEffect(() => experimental_getStatusStore("storybook/component-test").onAllStatusChange((statuses, previousStatuses) => {
      let current = statuses[storyId]?.[STATUS_TYPE_ID_COMPONENT_TEST], previous = previousStatuses[storyId]?.[STATUS_TYPE_ID_COMPONENT_TEST];
      current?.value === "status-value:error" && previous?.value !== "status-value:error" && setStatus("component-test-error");
    }), [storyId]);
    let handleToggleHighlight = useCallback(() => setHighlighted((prevHighlighted) => !prevHighlighted), []), [selectedItems, setSelectedItems] = useState(() => {
      let initialValue = /* @__PURE__ */ new Map();
      if (a11ySelection && /^[a-z]+.[a-z-]+.[0-9]+$/.test(a11ySelection)) {
        let [type, id] = a11ySelection.split(".");
        initialValue.set(`${type}.${id}`, a11ySelection);
      }
      return initialValue;
    }), allExpanded = useMemo(() => results?.[tab]?.every((result) => selectedItems.has(`${tab}.${result.id}`)) ?? !1, [results, selectedItems, tab]), toggleOpen = useCallback((event, type, item) => {
      event.stopPropagation();
      let key = `${type}.${item.id}`;
      setSelectedItems((prev) => new Map(prev.delete(key) ? prev : prev.set(key, `${key}.1`)));
    }, []), handleCollapseAll = useCallback(() => {
      setSelectedItems(/* @__PURE__ */ new Map());
    }, []), handleExpandAll = useCallback(() => {
      setSelectedItems((prev) => new Map(results?.[tab]?.map((result) => {
        let key = `${tab}.${result.id}`;
        return [key, prev.get(key) ?? `${key}.1`];
      }) ?? []));
    }, [results, tab]), handleSelectionChange = useCallback((key) => {
      let [type, id] = key.split(".");
      setSelectedItems((prev) => new Map(prev.set(`${type}.${id}`, key)));
    }, []), handleError = useCallback((err) => {
      setStatus("error"), setError(err);
    }, []), handleResult = useCallback((axeResults, id) => {
      storyId === id && (setStatus("ran"), setResults(axeResults), setTimeout(() => {
        if (status === "ran" && setStatus("ready"), selectedItems.size === 1) {
          let [key] = selectedItems.values();
          document.getElementById(key)?.scrollIntoView({ behavior: "smooth", block: "center" });
        }
      }, 900));
    }, [setResults, status, storyId, selectedItems]), handleSelect = useCallback((itemId, details) => {
      let [type, id] = itemId.split("."), { helpUrl, nodes } = results?.[type]?.find((r) => r.id === id) || {}, openedWindow = helpUrl && window.open(helpUrl, "_blank", "noopener,noreferrer");
      if (nodes && !openedWindow) {
        let index = nodes.findIndex((n) => details.selectors.some((s) => s === String(n.target))) ?? -1;
        if (index !== -1) {
          let key = `${type}.${id}.${index + 1}`;
          setSelectedItems(/* @__PURE__ */ new Map([[`${type}.${id}`, key]])), setTimeout(() => {
            document.getElementById(key)?.scrollIntoView({ behavior: "smooth", block: "center" });
          }, 100);
        }
      }
    }, [results]), handleReport = useCallback(({ reporters }) => {
      let a11yReport = reporters.find((r) => r.type === "a11y");
      a11yReport && ("error" in a11yReport.result ? handleError(a11yReport.result.error) : handleResult(a11yReport.result, storyId));
    }, [handleError, handleResult, storyId]), handleReset = useCallback(({ newPhase }) => {
      newPhase === "loading" && (setResults(void 0), setStatus(manual ? "manual" : "initial")), newPhase === "afterEach" && !manual && setStatus("running");
    }, [manual, setResults]), emit = useChannel({ [EVENTS.RESULT]: handleResult, [EVENTS.ERROR]: handleError, [EVENTS.SELECT]: handleSelect, [STORY_CHANGED]: () => setSelectedItems(/* @__PURE__ */ new Map()), [STORY_RENDER_PHASE_CHANGED]: handleReset, [STORY_FINISHED]: handleReport, [STORY_HOT_UPDATED]: () => {
      setStatus("running"), emit(EVENTS.MANUAL, storyId, parameters);
    } }, [handleReset, handleReport, handleSelect, handleError, handleResult, parameters, storyId]), handleManual = useCallback(() => {
      setStatus("running"), emit(EVENTS.MANUAL, storyId, parameters);
    }, [emit, parameters, storyId]), handleCopyLink = useCallback(async (linkPath) => {
      let { createCopyToClipboardFunction: createCopyToClipboardFunction2 } = await Promise.resolve().then(() => (init_components(), components_exports));
      await createCopyToClipboardFunction2()(`${window.location.origin}${linkPath}`);
    }, []), handleJumpToElement = useCallback((target) => emit(I, target), [emit]);
    useEffect(() => {
      setStatus(getInitialStatus(manual));
    }, [getInitialStatus, manual]), useEffect(() => {
      if (emit(e, `${ADDON_ID}/selected`), emit(e, `${ADDON_ID}/others`), !highlighted) return;
      let selected = Array.from(selectedItems.values()).flatMap((key) => {
        let [type, id, number] = key.split(".");
        if (type !== tab) return [];
        let target = results?.[type]?.find((r) => r.id === id)?.nodes[Number(number) - 1]?.target;
        return target ? [String(target)] : [];
      });
      emit(o, { id: `${ADDON_ID}/selected`, priority: 1, selectors: selected, styles: { outline: `1px solid color-mix(in srgb, ${colorsByType[tab]}, transparent 30%)`, backgroundColor: "transparent" }, hoverStyles: { outlineWidth: "2px" }, focusStyles: { backgroundColor: "transparent" }, menu: results?.[tab].map((result) => {
        let selectors = result.nodes.flatMap((n) => n.target).map(String).filter((e2) => selected.includes(e2));
        return [{ id: `${tab}.${result.id}:info`, title: getTitleForAxeResult(result), description: getFriendlySummaryForAxeResult(result), selectors }, { id: `${tab}.${result.id}`, iconLeft: "info", iconRight: "shareAlt", title: "Learn how to resolve this violation", clickEvent: EVENTS.SELECT, selectors }];
      }) });
      let others = results?.[tab].flatMap((r) => r.nodes.flatMap((n) => n.target).map(String)).filter((e2) => ![...unhighlightedSelectors, ...selected].includes(e2));
      emit(o, { id: `${ADDON_ID}/others`, selectors: others, styles: { outline: `1px solid color-mix(in srgb, ${colorsByType[tab]}, transparent 30%)`, backgroundColor: `color-mix(in srgb, ${colorsByType[tab]}, transparent 60%)` }, hoverStyles: { outlineWidth: "2px" }, focusStyles: { backgroundColor: "transparent" }, menu: results?.[tab].map((result) => {
        let selectors = result.nodes.flatMap((n) => n.target).map(String).filter((e2) => !selected.includes(e2));
        return [{ id: `${tab}.${result.id}:info`, title: getTitleForAxeResult(result), description: getFriendlySummaryForAxeResult(result), selectors }, { id: `${tab}.${result.id}`, iconLeft: "info", iconRight: "shareAlt", title: "Learn how to resolve this violation", clickEvent: EVENTS.SELECT, selectors }];
      }) });
    }, [emit, highlighted, results, tab, selectedItems]);
    let discrepancy = useMemo(() => {
      if (!currentStoryA11yStatusValue) return null;
      if (currentStoryA11yStatusValue === "status-value:success" && results?.violations.length) return "cliPassedBrowserFailed";
      if (currentStoryA11yStatusValue === "status-value:error" && !results?.violations.length) {
        if (status === "ready" || status === "ran") return "browserPassedCliFailed";
        if (status === "manual") return "cliFailedButModeManual";
      }
      return null;
    }, [results?.violations.length, status, currentStoryA11yStatusValue]);
    return react_default.createElement(A11yContext.Provider, { value: { parameters, results, highlighted, toggleHighlight: handleToggleHighlight, tab, setTab, handleCopyLink, status, setStatus, error, handleManual, discrepancy, selectedItems, toggleOpen, allExpanded, handleCollapseAll, handleExpandAll, handleJumpToElement, handleSelectionChange }, ...props });
  }, useA11yContext = () => useContext(A11yContext);
  function _extends() {
    return _extends = Object.assign ? Object.assign.bind() : function(n) {
      for (var e2 = 1; e2 < arguments.length; e2++) {
        var t2 = arguments[e2];
        for (var r in t2) ({}).hasOwnProperty.call(t2, r) && (n[r] = t2[r]);
      }
      return n;
    }, _extends.apply(null, arguments);
  }
  function $e42e1063c40fb3ef$export$b9ecd428b558ff10(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = !0 } = {}) {
    return function(event) {
      if (originalEventHandler?.(event), checkForDefaultPrevented === !1 || !event.defaultPrevented) return ourEventHandler?.(event);
    };
  }
  function $c512c27ab02ef895$export$50c7b4e9d9f19c1(scopeName, createContextScopeDeps = []) {
    let defaultContexts = [];
    function $c512c27ab02ef895$export$fd42f52fd3ae1109(rootComponentName, defaultContext) {
      let BaseContext = createContext(defaultContext), index = defaultContexts.length;
      defaultContexts = [...defaultContexts, defaultContext];
      function Provider2(props) {
        let { scope, children, ...context } = props, Context = scope?.[scopeName][index] || BaseContext, value = useMemo(() => context, Object.values(context));
        return createElement(Context.Provider, { value }, children);
      }
      function useContext2(consumerName, scope) {
        let Context = scope?.[scopeName][index] || BaseContext, context = useContext(Context);
        if (context) return context;
        if (defaultContext !== void 0) return defaultContext;
        throw new Error(`\`${consumerName}\` must be used within \`${rootComponentName}\``);
      }
      return Provider2.displayName = rootComponentName + "Provider", [Provider2, useContext2];
    }
    let createScope = () => {
      let scopeContexts = defaultContexts.map((defaultContext) => createContext(defaultContext));
      return function(scope) {
        let contexts = scope?.[scopeName] || scopeContexts;
        return useMemo(() => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }), [scope, contexts]);
      };
    };
    return createScope.scopeName = scopeName, [$c512c27ab02ef895$export$fd42f52fd3ae1109, $c512c27ab02ef895$var$composeContextScopes(createScope, ...createContextScopeDeps)];
  }
  function $c512c27ab02ef895$var$composeContextScopes(...scopes) {
    let baseScope = scopes[0];
    if (scopes.length === 1) return baseScope;
    let createScope1 = () => {
      let scopeHooks = scopes.map((createScope) => ({ useScope: createScope(), scopeName: createScope.scopeName }));
      return function(overrideScopes) {
        let nextScopes1 = scopeHooks.reduce((nextScopes, { useScope, scopeName }) => {
          let currentScope = useScope(overrideScopes)[`__scope${scopeName}`];
          return { ...nextScopes, ...currentScope };
        }, {});
        return useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes1 }), [nextScopes1]);
      };
    };
    return createScope1.scopeName = baseScope.scopeName, createScope1;
  }
  function $6ed0406888f73fc4$var$setRef(ref, value) {
    typeof ref == "function" ? ref(value) : ref != null && (ref.current = value);
  }
  function $6ed0406888f73fc4$export$43e446d32b3d21af(...refs) {
    return (node) => refs.forEach((ref) => $6ed0406888f73fc4$var$setRef(ref, node));
  }
  function $6ed0406888f73fc4$export$c7b2cbe3552a0d05(...refs) {
    return useCallback($6ed0406888f73fc4$export$43e446d32b3d21af(...refs), refs);
  }
  var $5e63c961fc1ce211$export$8c6ed5c666ac1360 = forwardRef((props, forwardedRef) => {
    let { children, ...slotProps } = props, childrenArray = Children.toArray(children), slottable = childrenArray.find($5e63c961fc1ce211$var$isSlottable);
    if (slottable) {
      let newElement = slottable.props.children, newChildren = childrenArray.map((child) => child === slottable ? Children.count(newElement) > 1 ? Children.only(null) : isValidElement(newElement) ? newElement.props.children : null : child);
      return createElement($5e63c961fc1ce211$var$SlotClone, _extends({}, slotProps, { ref: forwardedRef }), isValidElement(newElement) ? cloneElement(newElement, void 0, newChildren) : null);
    }
    return createElement($5e63c961fc1ce211$var$SlotClone, _extends({}, slotProps, { ref: forwardedRef }), children);
  });
  $5e63c961fc1ce211$export$8c6ed5c666ac1360.displayName = "Slot";
  var $5e63c961fc1ce211$var$SlotClone = forwardRef((props, forwardedRef) => {
    let { children, ...slotProps } = props;
    return isValidElement(children) ? cloneElement(children, { ...$5e63c961fc1ce211$var$mergeProps(slotProps, children.props), ref: forwardedRef ? $6ed0406888f73fc4$export$43e446d32b3d21af(forwardedRef, children.ref) : children.ref }) : Children.count(children) > 1 ? Children.only(null) : null;
  });
  $5e63c961fc1ce211$var$SlotClone.displayName = "SlotClone";
  var $5e63c961fc1ce211$export$d9f1ccf0bdb05d45 = ({ children }) => createElement(Fragment, null, children);
  function $5e63c961fc1ce211$var$isSlottable(child) {
    return isValidElement(child) && child.type === $5e63c961fc1ce211$export$d9f1ccf0bdb05d45;
  }
  function $5e63c961fc1ce211$var$mergeProps(slotProps, childProps) {
    let overrideProps = { ...childProps };
    for (let propName in childProps) {
      let slotPropValue = slotProps[propName], childPropValue = childProps[propName];
      /^on[A-Z]/.test(propName) ? slotPropValue && childPropValue ? overrideProps[propName] = (...args) => {
        childPropValue(...args), slotPropValue(...args);
      } : slotPropValue && (overrideProps[propName] = slotPropValue) : propName === "style" ? overrideProps[propName] = { ...slotPropValue, ...childPropValue } : propName === "className" && (overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(" "));
    }
    return { ...slotProps, ...overrideProps };
  }
  function $e02a7d9cb1dc128c$export$c74125a8e3af6bb2(name) {
    let PROVIDER_NAME = name + "CollectionProvider", [createCollectionContext, createCollectionScope] = $c512c27ab02ef895$export$50c7b4e9d9f19c1(PROVIDER_NAME), [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, { collectionRef: { current: null }, itemMap: /* @__PURE__ */ new Map() }), CollectionProvider = (props) => {
      let { scope, children } = props, ref = react_default.useRef(null), itemMap = react_default.useRef(/* @__PURE__ */ new Map()).current;
      return react_default.createElement(CollectionProviderImpl, { scope, itemMap, collectionRef: ref }, children);
    }, COLLECTION_SLOT_NAME = name + "CollectionSlot", CollectionSlot = react_default.forwardRef((props, forwardedRef) => {
      let { scope, children } = props, context = useCollectionContext(COLLECTION_SLOT_NAME, scope), composedRefs = $6ed0406888f73fc4$export$c7b2cbe3552a0d05(forwardedRef, context.collectionRef);
      return react_default.createElement($5e63c961fc1ce211$export$8c6ed5c666ac1360, { ref: composedRefs }, children);
    }), ITEM_SLOT_NAME = name + "CollectionItemSlot", ITEM_DATA_ATTR = "data-radix-collection-item", CollectionItemSlot = react_default.forwardRef((props, forwardedRef) => {
      let { scope, children, ...itemData } = props, ref = react_default.useRef(null), composedRefs = $6ed0406888f73fc4$export$c7b2cbe3552a0d05(forwardedRef, ref), context = useCollectionContext(ITEM_SLOT_NAME, scope);
      return react_default.useEffect(() => (context.itemMap.set(ref, { ref, ...itemData }), () => void context.itemMap.delete(ref))), react_default.createElement($5e63c961fc1ce211$export$8c6ed5c666ac1360, { [ITEM_DATA_ATTR]: "", ref: composedRefs }, children);
    });
    function useCollection(scope) {
      let context = useCollectionContext(name + "CollectionConsumer", scope);
      return react_default.useCallback(() => {
        let collectionNode = context.collectionRef.current;
        if (!collectionNode) return [];
        let orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));
        return Array.from(context.itemMap.values()).sort((a, b) => orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current));
      }, [context.collectionRef, context.itemMap]);
    }
    return [{ Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot }, useCollection, createCollectionScope];
  }
  function $6ed0406888f73fc4$var$setRef2(ref, value) {
    typeof ref == "function" ? ref(value) : ref != null && (ref.current = value);
  }
  function $6ed0406888f73fc4$export$43e446d32b3d21af2(...refs) {
    return (node) => refs.forEach((ref) => $6ed0406888f73fc4$var$setRef2(ref, node));
  }
  function $6ed0406888f73fc4$export$c7b2cbe3552a0d052(...refs) {
    return useCallback($6ed0406888f73fc4$export$43e446d32b3d21af2(...refs), refs);
  }
  var $9f79659886946c16$export$e5c5a5f917a5871c = globalThis?.document ? useLayoutEffect : () => {
  }, $1746a345f3d73bb7$var$useReactId = useId || (() => {
  }), $1746a345f3d73bb7$var$count = 0;
  function $1746a345f3d73bb7$export$f680877a34711e37(deterministicId) {
    let [id, setId] = useState($1746a345f3d73bb7$var$useReactId());
    return $9f79659886946c16$export$e5c5a5f917a5871c(() => {
      deterministicId || setId((reactId) => reactId ?? String($1746a345f3d73bb7$var$count++));
    }, [deterministicId]), deterministicId || (id ? `radix-${id}` : "");
  }
  var $5e63c961fc1ce211$export$8c6ed5c666ac13602 = forwardRef((props, forwardedRef) => {
    let { children, ...slotProps } = props, childrenArray = Children.toArray(children), slottable = childrenArray.find($5e63c961fc1ce211$var$isSlottable2);
    if (slottable) {
      let newElement = slottable.props.children, newChildren = childrenArray.map((child) => child === slottable ? Children.count(newElement) > 1 ? Children.only(null) : isValidElement(newElement) ? newElement.props.children : null : child);
      return createElement($5e63c961fc1ce211$var$SlotClone2, _extends({}, slotProps, { ref: forwardedRef }), isValidElement(newElement) ? cloneElement(newElement, void 0, newChildren) : null);
    }
    return createElement($5e63c961fc1ce211$var$SlotClone2, _extends({}, slotProps, { ref: forwardedRef }), children);
  });
  $5e63c961fc1ce211$export$8c6ed5c666ac13602.displayName = "Slot";
  var $5e63c961fc1ce211$var$SlotClone2 = forwardRef((props, forwardedRef) => {
    let { children, ...slotProps } = props;
    return isValidElement(children) ? cloneElement(children, { ...$5e63c961fc1ce211$var$mergeProps2(slotProps, children.props), ref: forwardedRef ? $6ed0406888f73fc4$export$43e446d32b3d21af2(forwardedRef, children.ref) : children.ref }) : Children.count(children) > 1 ? Children.only(null) : null;
  });
  $5e63c961fc1ce211$var$SlotClone2.displayName = "SlotClone";
  var $5e63c961fc1ce211$export$d9f1ccf0bdb05d452 = ({ children }) => createElement(Fragment, null, children);
  function $5e63c961fc1ce211$var$isSlottable2(child) {
    return isValidElement(child) && child.type === $5e63c961fc1ce211$export$d9f1ccf0bdb05d452;
  }
  function $5e63c961fc1ce211$var$mergeProps2(slotProps, childProps) {
    let overrideProps = { ...childProps };
    for (let propName in childProps) {
      let slotPropValue = slotProps[propName], childPropValue = childProps[propName];
      /^on[A-Z]/.test(propName) ? slotPropValue && childPropValue ? overrideProps[propName] = (...args) => {
        childPropValue(...args), slotPropValue(...args);
      } : slotPropValue && (overrideProps[propName] = slotPropValue) : propName === "style" ? overrideProps[propName] = { ...slotPropValue, ...childPropValue } : propName === "className" && (overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(" "));
    }
    return { ...slotProps, ...overrideProps };
  }
  var $8927f6f2acc4f386$var$NODES = ["a", "button", "div", "form", "h2", "h3", "img", "input", "label", "li", "nav", "ol", "p", "span", "svg", "ul"], $8927f6f2acc4f386$export$250ffa63cdc0d034 = $8927f6f2acc4f386$var$NODES.reduce((primitive, node) => {
    let Node = forwardRef((props, forwardedRef) => {
      let { asChild, ...primitiveProps } = props, Comp = asChild ? $5e63c961fc1ce211$export$8c6ed5c666ac13602 : node;
      return useEffect(() => {
        window[Symbol.for("radix-ui")] = !0;
      }, []), createElement(Comp, _extends({}, primitiveProps, { ref: forwardedRef }));
    });
    return Node.displayName = `Primitive.${node}`, { ...primitive, [node]: Node };
  }, {});
  function $b1b2314f5f9a1d84$export$25bec8c6f54ee79a(callback) {
    let callbackRef = useRef(callback);
    return useEffect(() => {
      callbackRef.current = callback;
    }), useMemo(() => (...args) => {
      var _callbackRef$current;
      return (_callbackRef$current = callbackRef.current) === null || _callbackRef$current === void 0 ? void 0 : _callbackRef$current.call(callbackRef, ...args);
    }, []);
  }
  function $b1b2314f5f9a1d84$export$25bec8c6f54ee79a2(callback) {
    let callbackRef = useRef(callback);
    return useEffect(() => {
      callbackRef.current = callback;
    }), useMemo(() => (...args) => {
      var _callbackRef$current;
      return (_callbackRef$current = callbackRef.current) === null || _callbackRef$current === void 0 ? void 0 : _callbackRef$current.call(callbackRef, ...args);
    }, []);
  }
  function $71cd76cc60e0454e$export$6f32135080cb4c3({ prop, defaultProp, onChange = () => {
  } }) {
    let [uncontrolledProp, setUncontrolledProp] = $71cd76cc60e0454e$var$useUncontrolledState({ defaultProp, onChange }), isControlled = prop !== void 0, value1 = isControlled ? prop : uncontrolledProp, handleChange = $b1b2314f5f9a1d84$export$25bec8c6f54ee79a2(onChange), setValue = useCallback((nextValue) => {
      if (isControlled) {
        let value = typeof nextValue == "function" ? nextValue(prop) : nextValue;
        value !== prop && handleChange(value);
      } else setUncontrolledProp(nextValue);
    }, [isControlled, prop, setUncontrolledProp, handleChange]);
    return [value1, setValue];
  }
  function $71cd76cc60e0454e$var$useUncontrolledState({ defaultProp, onChange }) {
    let uncontrolledState = useState(defaultProp), [value] = uncontrolledState, prevValueRef = useRef(value), handleChange = $b1b2314f5f9a1d84$export$25bec8c6f54ee79a2(onChange);
    return useEffect(() => {
      prevValueRef.current !== value && (handleChange(value), prevValueRef.current = value);
    }, [value, prevValueRef, handleChange]), uncontrolledState;
  }
  var $f631663db3294ace$var$DirectionContext = createContext(void 0);
  function $f631663db3294ace$export$b39126d51d94e6f3(localDir) {
    let globalDir = useContext($f631663db3294ace$var$DirectionContext);
    return localDir || globalDir || "ltr";
  }
  var $d7bdfb9eb0fdf311$var$ENTRY_FOCUS = "rovingFocusGroup.onEntryFocus", $d7bdfb9eb0fdf311$var$EVENT_OPTIONS = { bubbles: !1, cancelable: !0 }, $d7bdfb9eb0fdf311$var$GROUP_NAME = "RovingFocusGroup", [$d7bdfb9eb0fdf311$var$Collection, $d7bdfb9eb0fdf311$var$useCollection, $d7bdfb9eb0fdf311$var$createCollectionScope] = $e02a7d9cb1dc128c$export$c74125a8e3af6bb2($d7bdfb9eb0fdf311$var$GROUP_NAME), [$d7bdfb9eb0fdf311$var$createRovingFocusGroupContext, $d7bdfb9eb0fdf311$export$c7109489551a4f4] = $c512c27ab02ef895$export$50c7b4e9d9f19c1($d7bdfb9eb0fdf311$var$GROUP_NAME, [$d7bdfb9eb0fdf311$var$createCollectionScope]), [$d7bdfb9eb0fdf311$var$RovingFocusProvider, $d7bdfb9eb0fdf311$var$useRovingFocusContext] = $d7bdfb9eb0fdf311$var$createRovingFocusGroupContext($d7bdfb9eb0fdf311$var$GROUP_NAME), $d7bdfb9eb0fdf311$export$8699f7c8af148338 = forwardRef((props, forwardedRef) => createElement($d7bdfb9eb0fdf311$var$Collection.Provider, { scope: props.__scopeRovingFocusGroup }, createElement($d7bdfb9eb0fdf311$var$Collection.Slot, { scope: props.__scopeRovingFocusGroup }, createElement($d7bdfb9eb0fdf311$var$RovingFocusGroupImpl, _extends({}, props, { ref: forwardedRef }))))), $d7bdfb9eb0fdf311$var$RovingFocusGroupImpl = forwardRef((props, forwardedRef) => {
    let { __scopeRovingFocusGroup, orientation, loop = !1, dir, currentTabStopId: currentTabStopIdProp, defaultCurrentTabStopId, onCurrentTabStopIdChange, onEntryFocus, ...groupProps } = props, ref = useRef(null), composedRefs = $6ed0406888f73fc4$export$c7b2cbe3552a0d052(forwardedRef, ref), direction = $f631663db3294ace$export$b39126d51d94e6f3(dir), [currentTabStopId = null, setCurrentTabStopId] = $71cd76cc60e0454e$export$6f32135080cb4c3({ prop: currentTabStopIdProp, defaultProp: defaultCurrentTabStopId, onChange: onCurrentTabStopIdChange }), [isTabbingBackOut, setIsTabbingBackOut] = useState(!1), handleEntryFocus = $b1b2314f5f9a1d84$export$25bec8c6f54ee79a(onEntryFocus), getItems = $d7bdfb9eb0fdf311$var$useCollection(__scopeRovingFocusGroup), isClickFocusRef = useRef(!1), [focusableItemsCount, setFocusableItemsCount] = useState(0);
    return useEffect(() => {
      let node = ref.current;
      if (node) return node.addEventListener($d7bdfb9eb0fdf311$var$ENTRY_FOCUS, handleEntryFocus), () => node.removeEventListener($d7bdfb9eb0fdf311$var$ENTRY_FOCUS, handleEntryFocus);
    }, [handleEntryFocus]), createElement($d7bdfb9eb0fdf311$var$RovingFocusProvider, { scope: __scopeRovingFocusGroup, orientation, dir: direction, loop, currentTabStopId, onItemFocus: useCallback((tabStopId) => setCurrentTabStopId(tabStopId), [setCurrentTabStopId]), onItemShiftTab: useCallback(() => setIsTabbingBackOut(!0), []), onFocusableItemAdd: useCallback(() => setFocusableItemsCount((prevCount) => prevCount + 1), []), onFocusableItemRemove: useCallback(() => setFocusableItemsCount((prevCount) => prevCount - 1), []) }, createElement($8927f6f2acc4f386$export$250ffa63cdc0d034.div, _extends({ tabIndex: isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0, "data-orientation": orientation }, groupProps, { ref: composedRefs, style: { outline: "none", ...props.style }, onMouseDown: $e42e1063c40fb3ef$export$b9ecd428b558ff10(props.onMouseDown, () => {
      isClickFocusRef.current = !0;
    }), onFocus: $e42e1063c40fb3ef$export$b9ecd428b558ff10(props.onFocus, (event) => {
      let isKeyboardFocus = !isClickFocusRef.current;
      if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {
        let entryFocusEvent = new CustomEvent($d7bdfb9eb0fdf311$var$ENTRY_FOCUS, $d7bdfb9eb0fdf311$var$EVENT_OPTIONS);
        if (event.currentTarget.dispatchEvent(entryFocusEvent), !entryFocusEvent.defaultPrevented) {
          let items = getItems().filter((item) => item.focusable), activeItem = items.find((item) => item.active), currentItem = items.find((item) => item.id === currentTabStopId), candidateNodes = [activeItem, currentItem, ...items].filter(Boolean).map((item) => item.ref.current);
          $d7bdfb9eb0fdf311$var$focusFirst(candidateNodes);
        }
      }
      isClickFocusRef.current = !1;
    }), onBlur: $e42e1063c40fb3ef$export$b9ecd428b558ff10(props.onBlur, () => setIsTabbingBackOut(!1)) })));
  }), $d7bdfb9eb0fdf311$var$ITEM_NAME = "RovingFocusGroupItem", $d7bdfb9eb0fdf311$export$ab9df7c53fe8454 = forwardRef((props, forwardedRef) => {
    let { __scopeRovingFocusGroup, focusable = !0, active = !1, tabStopId, ...itemProps } = props, autoId = $1746a345f3d73bb7$export$f680877a34711e37(), id = tabStopId || autoId, context = $d7bdfb9eb0fdf311$var$useRovingFocusContext($d7bdfb9eb0fdf311$var$ITEM_NAME, __scopeRovingFocusGroup), isCurrentTabStop = context.currentTabStopId === id, getItems = $d7bdfb9eb0fdf311$var$useCollection(__scopeRovingFocusGroup), { onFocusableItemAdd, onFocusableItemRemove } = context;
    return useEffect(() => {
      if (focusable) return onFocusableItemAdd(), () => onFocusableItemRemove();
    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]), createElement($d7bdfb9eb0fdf311$var$Collection.ItemSlot, { scope: __scopeRovingFocusGroup, id, focusable, active }, createElement($8927f6f2acc4f386$export$250ffa63cdc0d034.span, _extends({ tabIndex: isCurrentTabStop ? 0 : -1, "data-orientation": context.orientation }, itemProps, { ref: forwardedRef, onMouseDown: $e42e1063c40fb3ef$export$b9ecd428b558ff10(props.onMouseDown, (event) => {
      focusable ? context.onItemFocus(id) : event.preventDefault();
    }), onFocus: $e42e1063c40fb3ef$export$b9ecd428b558ff10(props.onFocus, () => context.onItemFocus(id)), onKeyDown: $e42e1063c40fb3ef$export$b9ecd428b558ff10(props.onKeyDown, (event) => {
      if (event.key === "Tab" && event.shiftKey) {
        context.onItemShiftTab();
        return;
      }
      if (event.target !== event.currentTarget) return;
      let focusIntent = $d7bdfb9eb0fdf311$var$getFocusIntent(event, context.orientation, context.dir);
      if (focusIntent !== void 0) {
        event.preventDefault();
        let candidateNodes = getItems().filter((item) => item.focusable).map((item) => item.ref.current);
        if (focusIntent === "last") candidateNodes.reverse();
        else if (focusIntent === "prev" || focusIntent === "next") {
          focusIntent === "prev" && candidateNodes.reverse();
          let currentIndex = candidateNodes.indexOf(event.currentTarget);
          candidateNodes = context.loop ? $d7bdfb9eb0fdf311$var$wrapArray(candidateNodes, currentIndex + 1) : candidateNodes.slice(currentIndex + 1);
        }
        setTimeout(() => $d7bdfb9eb0fdf311$var$focusFirst(candidateNodes));
      }
    }) })));
  }), $d7bdfb9eb0fdf311$var$MAP_KEY_TO_FOCUS_INTENT = { ArrowLeft: "prev", ArrowUp: "prev", ArrowRight: "next", ArrowDown: "next", PageUp: "first", Home: "first", PageDown: "last", End: "last" };
  function $d7bdfb9eb0fdf311$var$getDirectionAwareKey(key, dir) {
    return dir !== "rtl" ? key : key === "ArrowLeft" ? "ArrowRight" : key === "ArrowRight" ? "ArrowLeft" : key;
  }
  function $d7bdfb9eb0fdf311$var$getFocusIntent(event, orientation, dir) {
    let key = $d7bdfb9eb0fdf311$var$getDirectionAwareKey(event.key, dir);
    if (!(orientation === "vertical" && ["ArrowLeft", "ArrowRight"].includes(key)) && !(orientation === "horizontal" && ["ArrowUp", "ArrowDown"].includes(key))) return $d7bdfb9eb0fdf311$var$MAP_KEY_TO_FOCUS_INTENT[key];
  }
  function $d7bdfb9eb0fdf311$var$focusFirst(candidates) {
    let PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;
    for (let candidate of candidates) if (candidate === PREVIOUSLY_FOCUSED_ELEMENT || (candidate.focus(), document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT)) return;
  }
  function $d7bdfb9eb0fdf311$var$wrapArray(array, startIndex) {
    return array.map((_, index) => array[(startIndex + index) % array.length]);
  }
  var $d7bdfb9eb0fdf311$export$be92b6f5f03c0fe9 = $d7bdfb9eb0fdf311$export$8699f7c8af148338, $d7bdfb9eb0fdf311$export$6d08773d2e66f8f2 = $d7bdfb9eb0fdf311$export$ab9df7c53fe8454;
  function $6ed0406888f73fc4$var$setRef3(ref, value) {
    typeof ref == "function" ? ref(value) : ref != null && (ref.current = value);
  }
  function $6ed0406888f73fc4$export$43e446d32b3d21af3(...refs) {
    return (node) => refs.forEach((ref) => $6ed0406888f73fc4$var$setRef3(ref, node));
  }
  function $6ed0406888f73fc4$export$c7b2cbe3552a0d053(...refs) {
    return useCallback($6ed0406888f73fc4$export$43e446d32b3d21af3(...refs), refs);
  }
  var $9f79659886946c16$export$e5c5a5f917a5871c2 = globalThis?.document ? useLayoutEffect : () => {
  };
  function $fe963b355347cc68$export$3e6543de14f8614f(initialState, machine) {
    return useReducer((state, event) => machine[state][event] ?? state, initialState);
  }
  var $921a889cee6df7e8$export$99c2b779aa4e8b8b = (props) => {
    let { present, children } = props, presence = $921a889cee6df7e8$var$usePresence(present), child = typeof children == "function" ? children({ present: presence.isPresent }) : Children.only(children), ref = $6ed0406888f73fc4$export$c7b2cbe3552a0d053(presence.ref, child.ref);
    return typeof children == "function" || presence.isPresent ? cloneElement(child, { ref }) : null;
  };
  $921a889cee6df7e8$export$99c2b779aa4e8b8b.displayName = "Presence";
  function $921a889cee6df7e8$var$usePresence(present) {
    let [node1, setNode] = useState(), stylesRef = useRef({}), prevPresentRef = useRef(present), prevAnimationNameRef = useRef("none"), initialState = present ? "mounted" : "unmounted", [state, send] = $fe963b355347cc68$export$3e6543de14f8614f(initialState, { mounted: { UNMOUNT: "unmounted", ANIMATION_OUT: "unmountSuspended" }, unmountSuspended: { MOUNT: "mounted", ANIMATION_END: "unmounted" }, unmounted: { MOUNT: "mounted" } });
    return useEffect(() => {
      let currentAnimationName = $921a889cee6df7e8$var$getAnimationName(stylesRef.current);
      prevAnimationNameRef.current = state === "mounted" ? currentAnimationName : "none";
    }, [state]), $9f79659886946c16$export$e5c5a5f917a5871c2(() => {
      let styles = stylesRef.current, wasPresent = prevPresentRef.current;
      if (wasPresent !== present) {
        let prevAnimationName = prevAnimationNameRef.current, currentAnimationName = $921a889cee6df7e8$var$getAnimationName(styles);
        present ? send("MOUNT") : currentAnimationName === "none" || styles?.display === "none" ? send("UNMOUNT") : send(wasPresent && prevAnimationName !== currentAnimationName ? "ANIMATION_OUT" : "UNMOUNT"), prevPresentRef.current = present;
      }
    }, [present, send]), $9f79659886946c16$export$e5c5a5f917a5871c2(() => {
      if (node1) {
        let handleAnimationEnd = (event) => {
          let isCurrentAnimation = $921a889cee6df7e8$var$getAnimationName(stylesRef.current).includes(event.animationName);
          event.target === node1 && isCurrentAnimation && flushSync(() => send("ANIMATION_END"));
        }, handleAnimationStart = (event) => {
          event.target === node1 && (prevAnimationNameRef.current = $921a889cee6df7e8$var$getAnimationName(stylesRef.current));
        };
        return node1.addEventListener("animationstart", handleAnimationStart), node1.addEventListener("animationcancel", handleAnimationEnd), node1.addEventListener("animationend", handleAnimationEnd), () => {
          node1.removeEventListener("animationstart", handleAnimationStart), node1.removeEventListener("animationcancel", handleAnimationEnd), node1.removeEventListener("animationend", handleAnimationEnd);
        };
      } else send("ANIMATION_END");
    }, [node1, send]), { isPresent: ["mounted", "unmountSuspended"].includes(state), ref: useCallback((node) => {
      node && (stylesRef.current = getComputedStyle(node)), setNode(node);
    }, []) };
  }
  function $921a889cee6df7e8$var$getAnimationName(styles) {
    return styles?.animationName || "none";
  }
  function $6ed0406888f73fc4$var$setRef4(ref, value) {
    typeof ref == "function" ? ref(value) : ref != null && (ref.current = value);
  }
  function $6ed0406888f73fc4$export$43e446d32b3d21af4(...refs) {
    return (node) => refs.forEach((ref) => $6ed0406888f73fc4$var$setRef4(ref, node));
  }
  var $5e63c961fc1ce211$export$8c6ed5c666ac13603 = forwardRef((props, forwardedRef) => {
    let { children, ...slotProps } = props, childrenArray = Children.toArray(children), slottable = childrenArray.find($5e63c961fc1ce211$var$isSlottable3);
    if (slottable) {
      let newElement = slottable.props.children, newChildren = childrenArray.map((child) => child === slottable ? Children.count(newElement) > 1 ? Children.only(null) : isValidElement(newElement) ? newElement.props.children : null : child);
      return createElement($5e63c961fc1ce211$var$SlotClone3, _extends({}, slotProps, { ref: forwardedRef }), isValidElement(newElement) ? cloneElement(newElement, void 0, newChildren) : null);
    }
    return createElement($5e63c961fc1ce211$var$SlotClone3, _extends({}, slotProps, { ref: forwardedRef }), children);
  });
  $5e63c961fc1ce211$export$8c6ed5c666ac13603.displayName = "Slot";
  var $5e63c961fc1ce211$var$SlotClone3 = forwardRef((props, forwardedRef) => {
    let { children, ...slotProps } = props;
    return isValidElement(children) ? cloneElement(children, { ...$5e63c961fc1ce211$var$mergeProps3(slotProps, children.props), ref: forwardedRef ? $6ed0406888f73fc4$export$43e446d32b3d21af4(forwardedRef, children.ref) : children.ref }) : Children.count(children) > 1 ? Children.only(null) : null;
  });
  $5e63c961fc1ce211$var$SlotClone3.displayName = "SlotClone";
  var $5e63c961fc1ce211$export$d9f1ccf0bdb05d453 = ({ children }) => createElement(Fragment, null, children);
  function $5e63c961fc1ce211$var$isSlottable3(child) {
    return isValidElement(child) && child.type === $5e63c961fc1ce211$export$d9f1ccf0bdb05d453;
  }
  function $5e63c961fc1ce211$var$mergeProps3(slotProps, childProps) {
    let overrideProps = { ...childProps };
    for (let propName in childProps) {
      let slotPropValue = slotProps[propName], childPropValue = childProps[propName];
      /^on[A-Z]/.test(propName) ? slotPropValue && childPropValue ? overrideProps[propName] = (...args) => {
        childPropValue(...args), slotPropValue(...args);
      } : slotPropValue && (overrideProps[propName] = slotPropValue) : propName === "style" ? overrideProps[propName] = { ...slotPropValue, ...childPropValue } : propName === "className" && (overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(" "));
    }
    return { ...slotProps, ...overrideProps };
  }
  var $8927f6f2acc4f386$var$NODES2 = ["a", "button", "div", "form", "h2", "h3", "img", "input", "label", "li", "nav", "ol", "p", "span", "svg", "ul"], $8927f6f2acc4f386$export$250ffa63cdc0d0342 = $8927f6f2acc4f386$var$NODES2.reduce((primitive, node) => {
    let Node = forwardRef((props, forwardedRef) => {
      let { asChild, ...primitiveProps } = props, Comp = asChild ? $5e63c961fc1ce211$export$8c6ed5c666ac13603 : node;
      return useEffect(() => {
        window[Symbol.for("radix-ui")] = !0;
      }, []), createElement(Comp, _extends({}, primitiveProps, { ref: forwardedRef }));
    });
    return Node.displayName = `Primitive.${node}`, { ...primitive, [node]: Node };
  }, {}), $69cb30bb0017df05$var$TABS_NAME = "Tabs", [$69cb30bb0017df05$var$createTabsContext, $69cb30bb0017df05$export$355f5bd209d7b13a] = $c512c27ab02ef895$export$50c7b4e9d9f19c1($69cb30bb0017df05$var$TABS_NAME, [$d7bdfb9eb0fdf311$export$c7109489551a4f4]), $69cb30bb0017df05$var$useRovingFocusGroupScope = $d7bdfb9eb0fdf311$export$c7109489551a4f4(), [$69cb30bb0017df05$var$TabsProvider, $69cb30bb0017df05$var$useTabsContext] = $69cb30bb0017df05$var$createTabsContext($69cb30bb0017df05$var$TABS_NAME), $69cb30bb0017df05$export$b2539bed5023c21c = forwardRef((props, forwardedRef) => {
    let { __scopeTabs, value: valueProp, onValueChange, defaultValue, orientation = "horizontal", dir, activationMode = "automatic", ...tabsProps } = props, direction = $f631663db3294ace$export$b39126d51d94e6f3(dir), [value, setValue] = $71cd76cc60e0454e$export$6f32135080cb4c3({ prop: valueProp, onChange: onValueChange, defaultProp: defaultValue });
    return createElement($69cb30bb0017df05$var$TabsProvider, { scope: __scopeTabs, baseId: $1746a345f3d73bb7$export$f680877a34711e37(), value, onValueChange: setValue, orientation, dir: direction, activationMode }, createElement($8927f6f2acc4f386$export$250ffa63cdc0d0342.div, _extends({ dir: direction, "data-orientation": orientation }, tabsProps, { ref: forwardedRef })));
  }), $69cb30bb0017df05$var$TAB_LIST_NAME = "TabsList", $69cb30bb0017df05$export$9712d22edc0d78c1 = forwardRef((props, forwardedRef) => {
    let { __scopeTabs, loop = !0, ...listProps } = props, context = $69cb30bb0017df05$var$useTabsContext($69cb30bb0017df05$var$TAB_LIST_NAME, __scopeTabs), rovingFocusGroupScope = $69cb30bb0017df05$var$useRovingFocusGroupScope(__scopeTabs);
    return createElement($d7bdfb9eb0fdf311$export$be92b6f5f03c0fe9, _extends({ asChild: !0 }, rovingFocusGroupScope, { orientation: context.orientation, dir: context.dir, loop }), createElement($8927f6f2acc4f386$export$250ffa63cdc0d0342.div, _extends({ role: "tablist", "aria-orientation": context.orientation }, listProps, { ref: forwardedRef })));
  }), $69cb30bb0017df05$var$TRIGGER_NAME = "TabsTrigger", $69cb30bb0017df05$export$8114b9fdfdf9f3ba = forwardRef((props, forwardedRef) => {
    let { __scopeTabs, value, disabled = !1, ...triggerProps } = props, context = $69cb30bb0017df05$var$useTabsContext($69cb30bb0017df05$var$TRIGGER_NAME, __scopeTabs), rovingFocusGroupScope = $69cb30bb0017df05$var$useRovingFocusGroupScope(__scopeTabs), triggerId = $69cb30bb0017df05$var$makeTriggerId(context.baseId, value), contentId = $69cb30bb0017df05$var$makeContentId(context.baseId, value), isSelected = value === context.value;
    return createElement($d7bdfb9eb0fdf311$export$6d08773d2e66f8f2, _extends({ asChild: !0 }, rovingFocusGroupScope, { focusable: !disabled, active: isSelected }), createElement($8927f6f2acc4f386$export$250ffa63cdc0d0342.button, _extends({ type: "button", role: "tab", "aria-selected": isSelected, "aria-controls": contentId, "data-state": isSelected ? "active" : "inactive", "data-disabled": disabled ? "" : void 0, disabled, id: triggerId }, triggerProps, { ref: forwardedRef, onMouseDown: $e42e1063c40fb3ef$export$b9ecd428b558ff10(props.onMouseDown, (event) => {
      !disabled && event.button === 0 && event.ctrlKey === !1 ? context.onValueChange(value) : event.preventDefault();
    }), onKeyDown: $e42e1063c40fb3ef$export$b9ecd428b558ff10(props.onKeyDown, (event) => {
      [" ", "Enter"].includes(event.key) && context.onValueChange(value);
    }), onFocus: $e42e1063c40fb3ef$export$b9ecd428b558ff10(props.onFocus, () => {
      let isAutomaticActivation = context.activationMode !== "manual";
      !isSelected && !disabled && isAutomaticActivation && context.onValueChange(value);
    }) })));
  }), $69cb30bb0017df05$var$CONTENT_NAME = "TabsContent", $69cb30bb0017df05$export$bd905d70e8fd2ebb = forwardRef((props, forwardedRef) => {
    let { __scopeTabs, value, forceMount, children, ...contentProps } = props, context = $69cb30bb0017df05$var$useTabsContext($69cb30bb0017df05$var$CONTENT_NAME, __scopeTabs), triggerId = $69cb30bb0017df05$var$makeTriggerId(context.baseId, value), contentId = $69cb30bb0017df05$var$makeContentId(context.baseId, value), isSelected = value === context.value, isMountAnimationPreventedRef = useRef(isSelected);
    return useEffect(() => {
      let rAF = requestAnimationFrame(() => isMountAnimationPreventedRef.current = !1);
      return () => cancelAnimationFrame(rAF);
    }, []), createElement($921a889cee6df7e8$export$99c2b779aa4e8b8b, { present: forceMount || isSelected }, ({ present }) => createElement($8927f6f2acc4f386$export$250ffa63cdc0d0342.div, _extends({ "data-state": isSelected ? "active" : "inactive", "data-orientation": context.orientation, role: "tabpanel", "aria-labelledby": triggerId, hidden: !present, id: contentId, tabIndex: 0 }, contentProps, { ref: forwardedRef, style: { ...props.style, animationDuration: isMountAnimationPreventedRef.current ? "0s" : void 0 } }), present && children));
  });
  function $69cb30bb0017df05$var$makeTriggerId(baseId, value) {
    return `${baseId}-trigger-${value}`;
  }
  function $69cb30bb0017df05$var$makeContentId(baseId, value) {
    return `${baseId}-content-${value}`;
  }
  var $69cb30bb0017df05$export$be92b6f5f03c0fe9 = $69cb30bb0017df05$export$b2539bed5023c21c, $69cb30bb0017df05$export$54c2e3dc7acea9f5 = $69cb30bb0017df05$export$9712d22edc0d78c1, $69cb30bb0017df05$export$41fb9f06171c75f4 = $69cb30bb0017df05$export$8114b9fdfdf9f3ba, $69cb30bb0017df05$export$7c6e2c02157bb7d2 = $69cb30bb0017df05$export$bd905d70e8fd2ebb, StyledSyntaxHighlighter = styled(SyntaxHighlighter)(({ theme: theme2 }) => ({ fontSize: theme2.typography.size.s1 }), ({ language }) => language === "css" && { ".selector ~ span:nth-last-of-type(-n+3)": { display: "none" } }), Info = styled.div({ display: "flex", flexDirection: "column" }), RuleId = styled.div(({ theme: theme2 }) => ({ display: "block", color: theme2.textMutedColor, fontFamily: theme2.typography.fonts.mono, fontSize: theme2.typography.size.s1, marginTop: -8, marginBottom: 12, "@container (min-width: 800px)": { display: "none" } })), Description = styled.p({ margin: 0 }), Wrapper = styled.div({ display: "flex", flexDirection: "column", padding: "0 15px 20px 15px", gap: 20 }), Columns = styled.div({ gap: 15, "@container (min-width: 800px)": { display: "grid", gridTemplateColumns: "50% 50%" } }), Content = styled.div(({ theme: theme2, side }) => ({ display: side === "left" ? "flex" : "none", flexDirection: "column", gap: 15, margin: side === "left" ? "15px 0" : 0, padding: side === "left" ? "0 15px" : 0, borderLeft: side === "left" ? `1px solid ${theme2.color.border}` : "none", "&:focus-visible": { outline: "none", borderRadius: 4, boxShadow: `0 0 0 1px inset ${theme2.color.secondary}` }, "@container (min-width: 800px)": { display: side === "left" ? "none" : "flex" } })), Item = styled(Button)(({ theme: theme2 }) => ({ fontFamily: theme2.typography.fonts.mono, fontWeight: theme2.typography.weight.regular, color: theme2.textMutedColor, height: 40, overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap", display: "block", width: "100%", textAlign: "left", padding: "0 12px", '&[data-state="active"]': { color: theme2.color.secondary, backgroundColor: theme2.background.hoverable } })), Messages = styled.div({ display: "flex", flexDirection: "column", gap: 10 }), Actions = styled.div({ display: "flex", gap: 10 }), CopyButton = ({ onClick }) => {
    let [copied, setCopied] = useState(!1), handleClick = useCallback(() => {
      onClick(), setCopied(!0);
      let timeout = setTimeout(() => setCopied(!1), 2e3);
      return () => clearTimeout(timeout);
    }, [onClick]);
    return react_default.createElement(Button, { onClick: handleClick }, copied ? react_default.createElement(CheckIcon, null) : react_default.createElement(CopyIcon, null), " ", copied ? "Copied" : "Copy link");
  }, Details = ({ id, item, type, selection, handleSelectionChange }) => react_default.createElement(Wrapper, { id }, react_default.createElement(Info, null, react_default.createElement(RuleId, null, item.id), react_default.createElement(Description, null, getFriendlySummaryForAxeResult(item), " ", react_default.createElement(Link, { href: item.helpUrl, target: "_blank", rel: "noopener noreferrer", withArrow: !0 }, "Learn how to resolve this violation"))), react_default.createElement($69cb30bb0017df05$export$be92b6f5f03c0fe9, { defaultValue: selection, orientation: "vertical", value: selection, onValueChange: handleSelectionChange, asChild: !0 }, react_default.createElement(Columns, null, react_default.createElement($69cb30bb0017df05$export$54c2e3dc7acea9f5, { "aria-label": type }, item.nodes.map((node, index) => {
    let key = `${type}.${item.id}.${index + 1}`;
    return react_default.createElement(Fragment, { key }, react_default.createElement($69cb30bb0017df05$export$41fb9f06171c75f4, { value: key, asChild: !0 }, react_default.createElement(Item, { variant: "ghost", size: "medium", id: key }, index + 1, ". ", node.html)), react_default.createElement($69cb30bb0017df05$export$7c6e2c02157bb7d2, { value: key, asChild: !0 }, react_default.createElement(Content, { side: "left" }, getContent(node))));
  })), item.nodes.map((node, index) => {
    let key = `${type}.${item.id}.${index + 1}`;
    return react_default.createElement($69cb30bb0017df05$export$7c6e2c02157bb7d2, { key, value: key, asChild: !0 }, react_default.createElement(Content, { side: "right" }, getContent(node)));
  }))));
  function getContent(node) {
    let { handleCopyLink, handleJumpToElement } = useA11yContext(), { any, all, none, html, target } = node, rules = [...any, ...all, ...none];
    return react_default.createElement(react_default.Fragment, null, react_default.createElement(Messages, null, rules.map((rule) => react_default.createElement("div", { key: rule.id }, `${rule.message}${/(\.|: [^.]+\.*)$/.test(rule.message) ? "" : "."}`))), react_default.createElement(Actions, null, react_default.createElement(Button, { onClick: () => handleJumpToElement(node.target.toString()) }, react_default.createElement(LocationIcon, null), " Jump to element"), react_default.createElement(CopyButton, { onClick: () => handleCopyLink(node.linkPath) })), react_default.createElement(StyledSyntaxHighlighter, { language: "jsx", wrapLongLines: !0 }, `/* element */
${html}`), react_default.createElement(StyledSyntaxHighlighter, { language: "css", wrapLongLines: !0 }, `/* selector */
${target} {}`));
  }
  var impactStatus = { minor: "neutral", moderate: "warning", serious: "negative", critical: "critical" }, impactLabels = { minor: "Minor", moderate: "Moderate", serious: "Serious", critical: "Critical" }, Wrapper2 = styled.div(({ theme: theme2 }) => ({ display: "flex", flexDirection: "column", width: "100%", borderBottom: `1px solid ${theme2.appBorderColor}`, containerType: "inline-size", fontSize: theme2.typography.size.s2 })), Icon = styled(ChevronSmallDownIcon)({ transition: "transform 0.1s ease-in-out" }), HeaderBar = styled.div(({ theme: theme2 }) => ({ display: "flex", justifyContent: "space-between", alignItems: "center", gap: 6, padding: "6px 10px 6px 15px", minHeight: 40, background: "none", color: "inherit", textAlign: "left", cursor: "pointer", width: "100%", "&:hover": { color: theme2.color.secondary } })), Title = styled.div(({ theme: theme2 }) => ({ display: "flex", alignItems: "baseline", flexGrow: 1, fontSize: theme2.typography.size.s2, gap: 8 })), RuleId2 = styled.div(({ theme: theme2 }) => ({ display: "none", color: theme2.textMutedColor, fontFamily: theme2.typography.fonts.mono, fontSize: theme2.typography.size.s1, "@container (min-width: 800px)": { display: "block" } })), Count = styled.div(({ theme: theme2 }) => ({ display: "flex", alignItems: "center", justifyContent: "center", color: theme2.textMutedColor, width: 28, height: 28 })), Report = ({ items, empty, type, handleSelectionChange, selectedItems, toggleOpen }) => react_default.createElement(react_default.Fragment, null, items && items.length ? items.map((item) => {
    let id = `${type}.${item.id}`, detailsId = `details:${id}`, selection = selectedItems.get(id), title = getTitleForAxeResult(item);
    return react_default.createElement(Wrapper2, { key: id }, react_default.createElement(HeaderBar, { onClick: (event) => toggleOpen(event, type, item), "data-active": !!selection }, react_default.createElement(Title, null, react_default.createElement("strong", null, title), react_default.createElement(RuleId2, null, item.id)), item.impact && react_default.createElement(Badge, { status: type === RuleType.PASS ? "neutral" : impactStatus[item.impact] }, impactLabels[item.impact]), react_default.createElement(Count, null, item.nodes.length), react_default.createElement(IconButton, { onClick: (event) => toggleOpen(event, type, item), "aria-label": `${selection ? "Collapse" : "Expand"} details for ${title}`, "aria-expanded": !!selection, "aria-controls": detailsId }, react_default.createElement(Icon, { style: { transform: `rotate(${selection ? -180 : 0}deg)` } }))), selection ? react_default.createElement(Details, { id: detailsId, item, type, selection, handleSelectionChange }) : react_default.createElement("div", { id: detailsId }));
  }) : react_default.createElement(EmptyTabContent, { title: empty })), extendStatics = function(d, b) {
    return extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
      d2.__proto__ = b2;
    } || function(d2, b2) {
      for (var p in b2) Object.prototype.hasOwnProperty.call(b2, p) && (d2[p] = b2[p]);
    }, extendStatics(d, b);
  };
  function __extends(d, b) {
    if (typeof b != "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
    extendStatics(d, b);
    function __() {
      this.constructor = d;
    }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  }
  var __assign = function() {
    return __assign = Object.assign || function(t2) {
      for (var s, i = 1, n = arguments.length; i < n; i++) {
        s = arguments[i];
        for (var p in s) Object.prototype.hasOwnProperty.call(s, p) && (t2[p] = s[p]);
      }
      return t2;
    }, __assign.apply(this, arguments);
  };
  function __rest(s, e2) {
    var t2 = {};
    for (var p in s) Object.prototype.hasOwnProperty.call(s, p) && e2.indexOf(p) < 0 && (t2[p] = s[p]);
    if (s != null && typeof Object.getOwnPropertySymbols == "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) e2.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]) && (t2[p[i]] = s[p[i]]);
    return t2;
  }
  var commonjsGlobal = typeof globalThis < "u" ? globalThis : typeof window < "u" || typeof window < "u" ? window : typeof self < "u" ? self : {};
  function isObject$3(value) {
    var type = typeof value;
    return value != null && (type == "object" || type == "function");
  }
  var isObject_1 = isObject$3, freeGlobal$1 = typeof commonjsGlobal == "object" && commonjsGlobal && commonjsGlobal.Object === Object && commonjsGlobal, _freeGlobal = freeGlobal$1, freeGlobal = _freeGlobal, freeSelf = typeof self == "object" && self && self.Object === Object && self, root$2 = freeGlobal || freeSelf || Function("return this")(), _root = root$2, root$1 = _root, now$1 = function() {
    return root$1.Date.now();
  }, now_1 = now$1, reWhitespace = /\s/;
  function trimmedEndIndex$1(string) {
    for (var index = string.length; index-- && reWhitespace.test(string.charAt(index)); ) ;
    return index;
  }
  var _trimmedEndIndex = trimmedEndIndex$1, trimmedEndIndex = _trimmedEndIndex, reTrimStart = /^\s+/;
  function baseTrim$1(string) {
    return string && string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, "");
  }
  var _baseTrim = baseTrim$1, root = _root, Symbol$2 = root.Symbol, _Symbol = Symbol$2, Symbol$1 = _Symbol, objectProto$1 = Object.prototype, hasOwnProperty = objectProto$1.hasOwnProperty, nativeObjectToString$1 = objectProto$1.toString, symToStringTag$1 = Symbol$1 ? Symbol$1.toStringTag : void 0;
  function getRawTag$1(value) {
    var isOwn = hasOwnProperty.call(value, symToStringTag$1), tag = value[symToStringTag$1];
    try {
      value[symToStringTag$1] = void 0;
      var unmasked = !0;
    } catch {
    }
    var result = nativeObjectToString$1.call(value);
    return unmasked && (isOwn ? value[symToStringTag$1] = tag : delete value[symToStringTag$1]), result;
  }
  var _getRawTag = getRawTag$1, objectProto = Object.prototype, nativeObjectToString = objectProto.toString;
  function objectToString$1(value) {
    return nativeObjectToString.call(value);
  }
  var _objectToString = objectToString$1, Symbol2 = _Symbol, getRawTag = _getRawTag, objectToString = _objectToString, nullTag = "[object Null]", undefinedTag = "[object Undefined]", symToStringTag = Symbol2 ? Symbol2.toStringTag : void 0;
  function baseGetTag$1(value) {
    return value == null ? value === void 0 ? undefinedTag : nullTag : symToStringTag && symToStringTag in Object(value) ? getRawTag(value) : objectToString(value);
  }
  var _baseGetTag = baseGetTag$1;
  function isObjectLike$1(value) {
    return value != null && typeof value == "object";
  }
  var isObjectLike_1 = isObjectLike$1, baseGetTag = _baseGetTag, isObjectLike = isObjectLike_1, symbolTag = "[object Symbol]";
  function isSymbol$1(value) {
    return typeof value == "symbol" || isObjectLike(value) && baseGetTag(value) == symbolTag;
  }
  var isSymbol_1 = isSymbol$1, baseTrim = _baseTrim, isObject$2 = isObject_1, isSymbol = isSymbol_1, NAN = NaN, reIsBadHex = /^[-+]0x[0-9a-f]+$/i, reIsBinary = /^0b[01]+$/i, reIsOctal = /^0o[0-7]+$/i, freeParseInt = parseInt;
  function toNumber$1(value) {
    if (typeof value == "number") return value;
    if (isSymbol(value)) return NAN;
    if (isObject$2(value)) {
      var other = typeof value.valueOf == "function" ? value.valueOf() : value;
      value = isObject$2(other) ? other + "" : other;
    }
    if (typeof value != "string") return value === 0 ? value : +value;
    value = baseTrim(value);
    var isBinary = reIsBinary.test(value);
    return isBinary || reIsOctal.test(value) ? freeParseInt(value.slice(2), isBinary ? 2 : 8) : reIsBadHex.test(value) ? NAN : +value;
  }
  var toNumber_1 = toNumber$1, isObject$1 = isObject_1, now = now_1, toNumber = toNumber_1, FUNC_ERROR_TEXT$1 = "Expected a function", nativeMax = Math.max, nativeMin = Math.min;
  function debounce$1(func, wait, options) {
    var lastArgs, lastThis, maxWait, result, timerId, lastCallTime, lastInvokeTime = 0, leading = !1, maxing = !1, trailing = !0;
    if (typeof func != "function") throw new TypeError(FUNC_ERROR_TEXT$1);
    wait = toNumber(wait) || 0, isObject$1(options) && (leading = !!options.leading, maxing = "maxWait" in options, maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait, trailing = "trailing" in options ? !!options.trailing : trailing);
    function invokeFunc(time) {
      var args = lastArgs, thisArg = lastThis;
      return lastArgs = lastThis = void 0, lastInvokeTime = time, result = func.apply(thisArg, args), result;
    }
    function leadingEdge(time) {
      return lastInvokeTime = time, timerId = setTimeout(timerExpired, wait), leading ? invokeFunc(time) : result;
    }
    function remainingWait(time) {
      var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime, timeWaiting = wait - timeSinceLastCall;
      return maxing ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke) : timeWaiting;
    }
    function shouldInvoke(time) {
      var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime;
      return lastCallTime === void 0 || timeSinceLastCall >= wait || timeSinceLastCall < 0 || maxing && timeSinceLastInvoke >= maxWait;
    }
    function timerExpired() {
      var time = now();
      if (shouldInvoke(time)) return trailingEdge(time);
      timerId = setTimeout(timerExpired, remainingWait(time));
    }
    function trailingEdge(time) {
      return timerId = void 0, trailing && lastArgs ? invokeFunc(time) : (lastArgs = lastThis = void 0, result);
    }
    function cancel() {
      timerId !== void 0 && clearTimeout(timerId), lastInvokeTime = 0, lastArgs = lastCallTime = lastThis = timerId = void 0;
    }
    function flush() {
      return timerId === void 0 ? result : trailingEdge(now());
    }
    function debounced() {
      var time = now(), isInvoking = shouldInvoke(time);
      if (lastArgs = arguments, lastThis = this, lastCallTime = time, isInvoking) {
        if (timerId === void 0) return leadingEdge(lastCallTime);
        if (maxing) return clearTimeout(timerId), timerId = setTimeout(timerExpired, wait), invokeFunc(lastCallTime);
      }
      return timerId === void 0 && (timerId = setTimeout(timerExpired, wait)), result;
    }
    return debounced.cancel = cancel, debounced.flush = flush, debounced;
  }
  var debounce_1 = debounce$1, debounce = debounce_1, isObject = isObject_1, FUNC_ERROR_TEXT = "Expected a function";
  function throttle(func, wait, options) {
    var leading = !0, trailing = !0;
    if (typeof func != "function") throw new TypeError(FUNC_ERROR_TEXT);
    return isObject(options) && (leading = "leading" in options ? !!options.leading : leading, trailing = "trailing" in options ? !!options.trailing : trailing), debounce(func, wait, { leading, maxWait: wait, trailing });
  }
  var throttle_1 = throttle, patchResizeHandler = function(resizeCallback, refreshMode, refreshRate, refreshOptions) {
    switch (refreshMode) {
      case "debounce":
        return debounce_1(resizeCallback, refreshRate, refreshOptions);
      case "throttle":
        return throttle_1(resizeCallback, refreshRate, refreshOptions);
      default:
        return resizeCallback;
    }
  }, isFunction = function(fn) {
    return typeof fn == "function";
  }, isSSR = function() {
    return typeof window > "u";
  }, isDOMElement = function(element) {
    return element instanceof Element || element instanceof HTMLDocument;
  }, createNotifier = function(onResize, setSize, handleWidth, handleHeight) {
    return function(_a) {
      var width = _a.width, height = _a.height;
      setSize(function(prev) {
        return prev.width === width && prev.height === height || prev.width === width && !handleHeight || prev.height === height && !handleWidth ? prev : (onResize && isFunction(onResize) && onResize(width, height), { width, height });
      });
    };
  };
  (function(_super) {
    __extends(ResizeDetector2, _super);
    function ResizeDetector2(props) {
      var _this = _super.call(this, props) || this;
      _this.cancelHandler = function() {
        _this.resizeHandler && _this.resizeHandler.cancel && (_this.resizeHandler.cancel(), _this.resizeHandler = null);
      }, _this.attachObserver = function() {
        var _a2 = _this.props, targetRef = _a2.targetRef, observerOptions = _a2.observerOptions;
        if (!isSSR()) {
          targetRef && targetRef.current && (_this.targetRef.current = targetRef.current);
          var element = _this.getElement();
          element && (_this.observableElement && _this.observableElement === element || (_this.observableElement = element, _this.resizeObserver.observe(element, observerOptions)));
        }
      }, _this.getElement = function() {
        var _a2 = _this.props, querySelector = _a2.querySelector, targetDomEl = _a2.targetDomEl;
        if (isSSR()) return null;
        if (querySelector) return document.querySelector(querySelector);
        if (targetDomEl && isDOMElement(targetDomEl)) return targetDomEl;
        if (_this.targetRef && isDOMElement(_this.targetRef.current)) return _this.targetRef.current;
        var currentElement = findDOMNode(_this);
        if (!currentElement) return null;
        var renderType = _this.getRenderType();
        switch (renderType) {
          case "renderProp":
            return currentElement;
          case "childFunction":
            return currentElement;
          case "child":
            return currentElement;
          case "childArray":
            return currentElement;
          default:
            return currentElement.parentElement;
        }
      }, _this.createResizeHandler = function(entries) {
        var _a2 = _this.props, _b = _a2.handleWidth, handleWidth = _b === void 0 ? !0 : _b, _c = _a2.handleHeight, handleHeight = _c === void 0 ? !0 : _c, onResize = _a2.onResize;
        if (!(!handleWidth && !handleHeight)) {
          var notifyResize = createNotifier(onResize, _this.setState.bind(_this), handleWidth, handleHeight);
          entries.forEach(function(entry) {
            var _a3 = entry && entry.contentRect || {}, width = _a3.width, height = _a3.height, shouldSetSize = !_this.skipOnMount && !isSSR();
            shouldSetSize && notifyResize({ width, height }), _this.skipOnMount = !1;
          });
        }
      }, _this.getRenderType = function() {
        var _a2 = _this.props, render2 = _a2.render, children = _a2.children;
        return isFunction(render2) ? "renderProp" : isFunction(children) ? "childFunction" : isValidElement(children) ? "child" : Array.isArray(children) ? "childArray" : "parent";
      };
      var skipOnMount = props.skipOnMount, refreshMode = props.refreshMode, _a = props.refreshRate, refreshRate = _a === void 0 ? 1e3 : _a, refreshOptions = props.refreshOptions;
      return _this.state = { width: void 0, height: void 0 }, _this.skipOnMount = skipOnMount, _this.targetRef = createRef(), _this.observableElement = null, isSSR() || (_this.resizeHandler = patchResizeHandler(_this.createResizeHandler, refreshMode, refreshRate, refreshOptions), _this.resizeObserver = new window.ResizeObserver(_this.resizeHandler)), _this;
    }
    return ResizeDetector2.prototype.componentDidMount = function() {
      this.attachObserver();
    }, ResizeDetector2.prototype.componentDidUpdate = function() {
      this.attachObserver();
    }, ResizeDetector2.prototype.componentWillUnmount = function() {
      isSSR() || (this.observableElement = null, this.resizeObserver.disconnect(), this.cancelHandler());
    }, ResizeDetector2.prototype.render = function() {
      var _a = this.props, render2 = _a.render, children = _a.children, _b = _a.nodeType, WrapperTag = _b === void 0 ? "div" : _b, _c = this.state, width = _c.width, height = _c.height, childProps = { width, height, targetRef: this.targetRef }, renderType = this.getRenderType(), typedChildren;
      switch (renderType) {
        case "renderProp":
          return render2 && render2(childProps);
        case "childFunction":
          return typedChildren = children, typedChildren(childProps);
        case "child":
          if (typedChildren = children, typedChildren.type && typeof typedChildren.type == "string") {
            childProps.targetRef;
            var nativeProps = __rest(childProps, ["targetRef"]);
            return cloneElement(typedChildren, nativeProps);
          }
          return cloneElement(typedChildren, childProps);
        case "childArray":
          return typedChildren = children, typedChildren.map(function(el) {
            return !!el && cloneElement(el, childProps);
          });
        default:
          return createElement(WrapperTag, null);
      }
    }, ResizeDetector2;
  })(PureComponent);
  var useEnhancedEffect = isSSR() ? useEffect : useLayoutEffect;
  function useResizeDetector(props) {
    props === void 0 && (props = {});
    var _a = props.skipOnMount, skipOnMount = _a === void 0 ? !1 : _a, refreshMode = props.refreshMode, _b = props.refreshRate, refreshRate = _b === void 0 ? 1e3 : _b, refreshOptions = props.refreshOptions, _c = props.handleWidth, handleWidth = _c === void 0 ? !0 : _c, _d = props.handleHeight, handleHeight = _d === void 0 ? !0 : _d, targetRef = props.targetRef, observerOptions = props.observerOptions, onResize = props.onResize, skipResize = useRef(skipOnMount), localRef = useRef(null), ref = targetRef ?? localRef, resizeHandler = useRef(), _e = useState({ width: void 0, height: void 0 }), size = _e[0], setSize = _e[1];
    return useEnhancedEffect(function() {
      if (!isSSR()) {
        var notifyResize = createNotifier(onResize, setSize, handleWidth, handleHeight), resizeCallback = function(entries) {
          !handleWidth && !handleHeight || entries.forEach(function(entry) {
            var _a2 = entry && entry.contentRect || {}, width = _a2.width, height = _a2.height, shouldSetSize = !skipResize.current && !isSSR();
            shouldSetSize && notifyResize({ width, height }), skipResize.current = !1;
          });
        };
        resizeHandler.current = patchResizeHandler(resizeCallback, refreshMode, refreshRate, refreshOptions);
        var resizeObserver = new window.ResizeObserver(resizeHandler.current);
        return ref.current && resizeObserver.observe(ref.current, observerOptions), function() {
          resizeObserver.disconnect();
          var patchedResizeHandler = resizeHandler.current;
          patchedResizeHandler && patchedResizeHandler.cancel && patchedResizeHandler.cancel();
        };
      }
    }, [refreshMode, refreshRate, refreshOptions, handleWidth, handleHeight, onResize, observerOptions, ref.current]), __assign({ ref }, size);
  }
  var Container = styled.div({ width: "100%", position: "relative", minHeight: "100%" }), Item2 = styled.button(({ theme: theme2 }) => ({ textDecoration: "none", padding: "10px 15px", cursor: "pointer", color: theme2.textMutedColor, fontWeight: theme2.typography.weight.bold, fontSize: theme2.typography.size.s2 - 1, lineHeight: 1, height: 40, border: "none", borderBottom: "3px solid transparent", background: "transparent", "&:focus": { outline: "0 none", borderColor: theme2.color.secondary } }), ({ active, theme: theme2 }) => active ? { opacity: 1, color: theme2.color.secondary, borderColor: theme2.color.secondary } : {}), Subnav = styled.div(({ theme: theme2 }) => ({ boxShadow: `${theme2.appBorderColor} 0 -1px 0 0 inset`, background: theme2.background.app, position: "sticky", top: 0, zIndex: 1, display: "flex", alignItems: "center", whiteSpace: "nowrap", overflow: "auto", paddingRight: 10, gap: 6, scrollbarColor: `${theme2.barTextColor} ${theme2.background.app}`, scrollbarWidth: "thin" })), TabsWrapper = styled.div({}), ActionsWrapper = styled.div({ display: "flex", flexBasis: "100%", justifyContent: "flex-end", containerType: "inline-size", minWidth: 96, gap: 6 }), ToggleButton = styled(IconButton)({ "@container (max-width: 193px)": { span: { display: "none" } } }), Tabs2 = ({ tabs }) => {
    let { ref } = useResizeDetector({ refreshMode: "debounce", handleHeight: !1, handleWidth: !0 }), { tab: activeTab, setTab, toggleHighlight, highlighted, handleManual, allExpanded, handleCollapseAll, handleExpandAll } = useA11yContext(), handleToggle = useCallback((event) => {
      setTab(event.currentTarget.getAttribute("data-type"));
    }, [setTab]);
    return createElement(Container, { ref }, createElement(Subnav, null, createElement(TabsWrapper, { role: "tablist" }, tabs.map((tab, index) => createElement(Item2, { role: "tab", key: index, "data-type": tab.type, "data-active": activeTab === tab.type, "aria-selected": activeTab === tab.type, active: activeTab === tab.type, onClick: handleToggle }, tab.label))), createElement(ActionsWrapper, null, createElement(WithTooltip, { as: "div", hasChrome: !1, placement: "top", tooltip: createElement(TooltipNote, { note: "Highlight elements with accessibility violations" }), trigger: "hover" }, createElement(ToggleButton, { onClick: toggleHighlight, active: highlighted }, highlighted ? createElement(EyeCloseIcon, null) : createElement(EyeIcon, null), createElement("span", null, highlighted ? "Hide highlights" : "Show highlights"))), createElement(WithTooltip, { as: "div", hasChrome: !1, placement: "top", tooltip: createElement(TooltipNote, { note: allExpanded ? "Collapse all" : "Expand all" }), trigger: "hover" }, createElement(IconButton, { onClick: allExpanded ? handleCollapseAll : handleExpandAll, "aria-label": allExpanded ? "Collapse all" : "Expand all" }, allExpanded ? createElement(CollapseIcon, null) : createElement(ExpandAltIcon, null))), createElement(WithTooltip, { as: "div", hasChrome: !1, placement: "top", tooltip: createElement(TooltipNote, { note: "Rerun the accessibility scan" }), trigger: "hover" }, createElement(IconButton, { onClick: handleManual, "aria-label": "Rerun accessibility scan" }, createElement(SyncIcon, null))))), createElement(ScrollArea, { vertical: !0, horizontal: !0 }, tabs.find((t2) => t2.type === activeTab)?.panel));
  }, Wrapper3 = styled.div(({ theme: { color: color2, typography: typography2, background: background2 } }) => ({ textAlign: "start", padding: "11px 15px", fontSize: `${typography2.size.s2}px`, fontWeight: typography2.weight.regular, lineHeight: "1rem", background: background2.app, borderBottom: `1px solid ${color2.border}`, color: color2.defaultText, backgroundClip: "padding-box", position: "relative", code: { fontSize: `${typography2.size.s1 - 1}px`, color: "inherit", margin: "0 0.2em", padding: "0 0.2em", background: "rgba(255, 255, 255, 0.8)", borderRadius: "2px", boxShadow: "0 0 0 1px rgba(0, 0, 0, 0.1)" } })), TestDiscrepancyMessage = ({ discrepancy }) => {
    let docsUrl = useStorybookApi().getDocsUrl({ subpath: DOCUMENTATION_DISCREPANCY_LINK, versioned: !0, renderer: !0 }), message = useMemo(() => {
      switch (discrepancy) {
        case "browserPassedCliFailed":
          return "Accessibility checks passed in this browser but failed in the CLI.";
        case "cliPassedBrowserFailed":
          return "Accessibility checks passed in the CLI but failed in this browser.";
        case "cliFailedButModeManual":
          return "Accessibility checks failed in the CLI. Run the tests manually to see the results.";
        default:
          return null;
      }
    }, [discrepancy]);
    return message ? react_default.createElement(Wrapper3, null, message, " ", react_default.createElement(Link, { href: docsUrl, target: "_blank", withArrow: !0 }, "Learn what could cause this")) : null;
  }, RotatingIcon = styled(SyncIcon)(({ theme: theme2 }) => ({ animation: `${theme2.animation.rotate360} 1s linear infinite;`, margin: 4 })), Tab = styled.div({ display: "flex", alignItems: "center", gap: 6 }), Centered = styled.span(({ theme: theme2 }) => ({ display: "flex", flexDirection: "column", alignItems: "center", justifyContent: "center", textAlign: "center", fontSize: theme2.typography.size.s2, height: "100%", gap: 24, div: { display: "flex", flexDirection: "column", alignItems: "center", gap: 8 }, p: { margin: 0, color: theme2.textMutedColor }, code: { display: "inline-block", fontSize: theme2.typography.size.s2 - 1, backgroundColor: theme2.background.app, border: `1px solid ${theme2.color.border}`, borderRadius: 4, padding: "2px 3px" } })), A11YPanel = () => {
    let { parameters, tab, results, status, handleManual, error, discrepancy, handleSelectionChange, selectedItems, toggleOpen } = useA11yContext(), tabs = useMemo(() => {
      let { passes, incomplete, violations } = results ?? { passes: [], incomplete: [], violations: [] };
      return [{ label: react_default.createElement(Tab, null, "Violations", react_default.createElement(Badge, { compact: !0, status: tab === "violations" ? "active" : "neutral" }, violations.length)), panel: react_default.createElement(Report, { items: violations, type: RuleType.VIOLATION, empty: "No accessibility violations found.", handleSelectionChange, selectedItems, toggleOpen }), items: violations, type: RuleType.VIOLATION }, { label: react_default.createElement(Tab, null, "Passes", react_default.createElement(Badge, { compact: !0, status: tab === "passes" ? "active" : "neutral" }, passes.length)), panel: react_default.createElement(Report, { items: passes, type: RuleType.PASS, empty: "No passing accessibility checks found.", handleSelectionChange, selectedItems, toggleOpen }), items: passes, type: RuleType.PASS }, { label: react_default.createElement(Tab, null, "Inconclusive", react_default.createElement(Badge, { compact: !0, status: tab === "incomplete" ? "active" : "neutral" }, incomplete.length)), panel: react_default.createElement(Report, { items: incomplete, type: RuleType.INCOMPLETION, empty: "No inconclusive accessibility checks found.", handleSelectionChange, selectedItems, toggleOpen }), items: incomplete, type: RuleType.INCOMPLETION }];
    }, [tab, results, handleSelectionChange, selectedItems, toggleOpen]);
    return parameters.disable || parameters.test === "off" ? react_default.createElement(Centered, null, react_default.createElement("div", null, react_default.createElement("strong", null, "Accessibility tests are disabled for this story"), react_default.createElement("p", null, "Update", " ", react_default.createElement("code", null, parameters.disable ? "parameters.a11y.disable" : "parameters.a11y.test"), " ", "to enable accessibility tests."))) : react_default.createElement(react_default.Fragment, null, discrepancy && react_default.createElement(TestDiscrepancyMessage, { discrepancy }), status === "ready" || status === "ran" ? react_default.createElement(Tabs2, { key: "tabs", tabs }) : react_default.createElement(Centered, { style: { marginTop: discrepancy ? "1em" : 0 } }, status === "initial" && react_default.createElement("div", null, react_default.createElement(RotatingIcon, { size: 12 }), react_default.createElement("strong", null, "Preparing accessibility scan"), react_default.createElement("p", null, "Please wait while the addon is initializing...")), status === "manual" && react_default.createElement(react_default.Fragment, null, react_default.createElement("div", null, react_default.createElement("strong", null, "Accessibility tests run manually for this story"), react_default.createElement("p", null, "Results will not show when using the testing module. You can still run accessibility tests manually.")), react_default.createElement(Button, { size: "medium", onClick: handleManual }, "Run accessibility scan"), react_default.createElement("p", null, "Update ", react_default.createElement("code", null, "globals.a11y.manual"), " to disable manual mode.")), status === "running" && react_default.createElement("div", null, react_default.createElement(RotatingIcon, { size: 12 }), react_default.createElement("strong", null, "Accessibility scan in progress"), react_default.createElement("p", null, "Please wait while the accessibility scan is running...")), status === "error" && react_default.createElement(react_default.Fragment, null, react_default.createElement("div", null, react_default.createElement("strong", null, "The accessibility scan encountered an error"), react_default.createElement("p", null, typeof error == "string" ? error : error instanceof Error ? error.toString() : JSON.stringify(error, null, 2))), react_default.createElement(Button, { size: "medium", onClick: handleManual }, "Rerun accessibility scan")), status === "component-test-error" && react_default.createElement(react_default.Fragment, null, react_default.createElement("div", null, react_default.createElement("strong", null, "This story's component tests failed"), react_default.createElement("p", null, "Automated accessibility tests will not run until this is resolved. You can still test manually.")), react_default.createElement(Button, { size: "medium", onClick: handleManual }, "Run accessibility scan"))));
  }, Filters = (props) => createElement("svg", { ...props }, createElement("defs", null, createElement("filter", { id: "protanopia" }, createElement("feColorMatrix", { in: "SourceGraphic", type: "matrix", values: "0.567, 0.433, 0, 0, 0 0.558, 0.442, 0, 0, 0 0, 0.242, 0.758, 0, 0 0, 0, 0, 1, 0" })), createElement("filter", { id: "protanomaly" }, createElement("feColorMatrix", { in: "SourceGraphic", type: "matrix", values: "0.817, 0.183, 0, 0, 0 0.333, 0.667, 0, 0, 0 0, 0.125, 0.875, 0, 0 0, 0, 0, 1, 0" })), createElement("filter", { id: "deuteranopia" }, createElement("feColorMatrix", { in: "SourceGraphic", type: "matrix", values: "0.625, 0.375, 0, 0, 0 0.7, 0.3, 0, 0, 0 0, 0.3, 0.7, 0, 0 0, 0, 0, 1, 0" })), createElement("filter", { id: "deuteranomaly" }, createElement("feColorMatrix", { in: "SourceGraphic", type: "matrix", values: "0.8, 0.2, 0, 0, 0 0.258, 0.742, 0, 0, 0 0, 0.142, 0.858, 0, 0 0, 0, 0, 1, 0" })), createElement("filter", { id: "tritanopia" }, createElement("feColorMatrix", { in: "SourceGraphic", type: "matrix", values: "0.95, 0.05,  0, 0, 0 0,  0.433, 0.567, 0, 0 0,  0.475, 0.525, 0, 0 0,  0, 0, 1, 0" })), createElement("filter", { id: "tritanomaly" }, createElement("feColorMatrix", { in: "SourceGraphic", type: "matrix", values: "0.967, 0.033, 0, 0, 0 0, 0.733, 0.267, 0, 0 0, 0.183, 0.817, 0, 0 0, 0, 0, 1, 0" })), createElement("filter", { id: "achromatopsia" }, createElement("feColorMatrix", { in: "SourceGraphic", type: "matrix", values: "0.299, 0.587, 0.114, 0, 0 0.299, 0.587, 0.114, 0, 0 0.299, 0.587, 0.114, 0, 0 0, 0, 0, 1, 0" })))), iframeId = "storybook-preview-iframe", baseList = [{ name: "blurred vision", percentage: 22.9 }, { name: "deuteranomaly", percentage: 2.7 }, { name: "deuteranopia", percentage: 0.56 }, { name: "protanomaly", percentage: 0.66 }, { name: "protanopia", percentage: 0.59 }, { name: "tritanomaly", percentage: 0.01 }, { name: "tritanopia", percentage: 0.016 }, { name: "achromatopsia", percentage: 1e-4 }, { name: "grayscale" }], getFilter = (filterName) => filterName ? filterName === "blurred vision" ? "blur(2px)" : filterName === "grayscale" ? "grayscale(100%)" : `url('#${filterName}')` : "none", Hidden = styled.div({ "&, & svg": { position: "absolute", width: 0, height: 0 } }), ColorIcon = styled.span({ background: "linear-gradient(to right, #F44336, #FF9800, #FFEB3B, #8BC34A, #2196F3, #9C27B0)", borderRadius: "1rem", display: "block", height: "1rem", width: "1rem" }, ({ filter }) => ({ filter: getFilter(filter) }), ({ theme: theme2 }) => ({ boxShadow: `${theme2.appBorderColor} 0 0 0 1px inset` })), Column = styled.span({ display: "flex", flexDirection: "column" }), Title2 = styled.span({ textTransform: "capitalize" }), Description2 = styled.span(({ theme: theme2 }) => ({ fontSize: 11, color: theme2.textMutedColor })), getColorList = (active, set) => [...active !== null ? [{ id: "reset", title: "Reset color filter", onClick: () => {
    set(null);
  }, right: void 0, active: !1 }] : [], ...baseList.map((i) => {
    let description = i.percentage !== void 0 ? `${i.percentage}% of users` : void 0;
    return { id: i.name, title: react_default.createElement(Column, null, react_default.createElement(Title2, null, i.name), description && react_default.createElement(Description2, null, description)), onClick: () => {
      set(i);
    }, right: react_default.createElement(ColorIcon, { filter: i.name }), active: active === i };
  })], VisionSimulator = () => {
    let [filter, setFilter] = useState(null);
    return react_default.createElement(react_default.Fragment, null, filter && react_default.createElement(Global, { styles: { [`#${iframeId}`]: { filter: getFilter(filter.name) } } }), react_default.createElement(WithTooltip, { placement: "top", tooltip: ({ onHide }) => {
      let colorList = getColorList(filter, (i) => {
        setFilter(i), onHide();
      });
      return react_default.createElement(TooltipLinkList, { links: colorList });
    }, closeOnOutsideClick: !0, onDoubleClick: () => setFilter(null) }, react_default.createElement(IconButton, { key: "filter", active: !!filter, title: "Vision simulator" }, react_default.createElement(AccessibilityIcon, null))), react_default.createElement(Hidden, null, react_default.createElement(Filters, null)));
  }, Title3 = () => {
    let selectedPanel = useStorybookApi().getSelectedPanel(), [addonState] = useAddonState(ADDON_ID), violationsNb = addonState?.violations?.length || 0, incompleteNb = addonState?.incomplete?.length || 0, count = violationsNb + incompleteNb;
    return react_default.createElement("div", { style: { display: "flex", alignItems: "center", gap: 6 } }, react_default.createElement("span", null, "Accessibility"), count === 0 ? null : react_default.createElement(Badge, { compact: !0, status: selectedPanel === PANEL_ID ? "active" : "neutral" }, count));
  };
  addons.register(ADDON_ID, (api) => {
    addons.add(PANEL_ID, { title: "", type: types.TOOL, match: ({ viewMode, tabId }) => viewMode === "story" && !tabId, render: () => react_default.createElement(VisionSimulator, null) }), addons.add(PANEL_ID, { title: Title3, type: types.PANEL, render: ({ active = !0 }) => react_default.createElement(A11yContextProvider, null, active ? react_default.createElement(A11YPanel, null) : null), paramKey: PARAM_KEY });
  });
})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
