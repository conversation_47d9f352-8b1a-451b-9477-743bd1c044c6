import * as next_dist_shared_lib_get_img_props from 'next/dist/shared/lib/get-img-props';
import * as _NextImage from 'next/image';
import React__default from 'next/dist/compiled/react';

declare const MockedNextImage: React__default.ForwardRefExoticComponent<Omit<React__default.DetailedHTMLProps<React__default.ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement>, "src" | "srcSet" | "ref" | "alt" | "width" | "height" | "loading"> & {
    src: string | next_dist_shared_lib_get_img_props.StaticImport;
    alt: string;
    width?: number | `${number}`;
    height?: number | `${number}`;
    fill?: boolean;
    loader?: _NextImage.ImageLoader;
    quality?: number | `${number}`;
    priority?: boolean;
    loading?: "lazy" | "eager" | undefined;
    placeholder?: next_dist_shared_lib_get_img_props.PlaceholderValue;
    blurDataURL?: string;
    unoptimized?: boolean;
    overrideSrc?: string;
    onLoadingComplete?: next_dist_shared_lib_get_img_props.OnLoadingComplete;
    layout?: string;
    objectFit?: string;
    objectPosition?: string;
    lazyBoundary?: string;
    lazyRoot?: string;
} & React__default.RefAttributes<HTMLImageElement>>;
declare const getImageProps: (props: _NextImage.ImageProps) => {
    props: next_dist_shared_lib_get_img_props.ImgProps;
};

export { MockedNextImage as default, getImageProps };
