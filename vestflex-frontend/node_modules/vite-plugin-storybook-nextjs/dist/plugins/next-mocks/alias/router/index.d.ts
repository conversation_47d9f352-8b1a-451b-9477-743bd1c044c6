import { NextComponentType, NextPageContext } from 'next';
import singletonRouter from 'next/dist/client/router.js';
export { default } from 'next/dist/client/router.js';
import { WithRouterProps, ExcludeRouterProps } from 'next/dist/client/with-router';
import { NextRouter } from 'next/router.js';
import { ComponentType } from 'react';
import { Mock } from 'storybook/test';
export * from 'next/dist/client/router';

/**
 * Creates a next/router router API mock. Used internally.
 * @ignore
 * @internal
 * */
declare const createRouter: (overrides: Partial<NextRouter>) => NextRouter;
declare const getRouter: () => {
    push: Mock;
    replace: Mock;
    reload: Mock;
    back: Mock;
    forward: Mock;
    prefetch: Mock;
    beforePopState: Mock;
    events: {
        on: Mock;
        off: Mock;
        emit: Mock;
    };
} & {
    route: string;
    asPath: string;
    basePath: string;
    pathname: string;
    query: {};
    isFallback: boolean;
    isLocaleDomain: boolean;
    isReady: boolean;
    isPreview: boolean;
};

declare const useRouter: Mock<() => NextRouter>;
declare const withRouter: Mock<(ComposedComponent: NextComponentType<NextPageContext, any, WithRouterProps>) => ComponentType<ExcludeRouterProps<WithRouterProps>>>;

export { createRouter, getRouter, useRouter, withRouter };
