'use strict';

var child_process = require('child_process');
var nodeLogger = require('storybook/internal/node-logger');

var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __require=(x=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(x,{get:(a,b)=>(typeof require<"u"?require:a)[b]}):x)(function(x){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+x+'" is not supported')});var __commonJS=(cb,mod)=>function(){return mod||(0, cb[__getOwnPropNames(cb)[0]])((mod={exports:{}}).exports,mod),mod.exports};var __copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod));var require_tree_kill=__commonJS({"../../node_modules/tree-kill/index.js"(exports,module){var childProcess=__require("child_process"),spawn2=childProcess.spawn,exec=childProcess.exec;module.exports=function(pid,signal,callback){if(typeof signal=="function"&&callback===void 0&&(callback=signal,signal=void 0),pid=parseInt(pid),Number.isNaN(pid)){if(callback)return callback(new Error("pid must be a number"));throw new Error("pid must be a number")}var tree={},pidsToProcess={};switch(tree[pid]=[],pidsToProcess[pid]=1,process.platform){case"win32":exec("taskkill /pid "+pid+" /T /F",callback);break;case"darwin":buildProcessTree(pid,tree,pidsToProcess,function(parentPid){return spawn2("pgrep",["-P",parentPid])},function(){killAll(tree,signal,callback);});break;default:buildProcessTree(pid,tree,pidsToProcess,function(parentPid){return spawn2("ps",["-o","pid","--no-headers","--ppid",parentPid])},function(){killAll(tree,signal,callback);});break}};function killAll(tree,signal,callback){var killed={};try{Object.keys(tree).forEach(function(pid){tree[pid].forEach(function(pidpid){killed[pidpid]||(killPid(pidpid,signal),killed[pidpid]=1);}),killed[pid]||(killPid(pid,signal),killed[pid]=1);});}catch(err){if(callback)return callback(err);throw err}if(callback)return callback()}function killPid(pid,signal){try{process.kill(parseInt(pid,10),signal);}catch(err){if(err.code!=="ESRCH")throw err}}function buildProcessTree(parentPid,tree,pidsToProcess,spawnChildProcessesList,cb){var ps=spawnChildProcessesList(parentPid),allData="";ps.stdout.on("data",function(data){var data=data.toString("ascii");allData+=data;});var onClose=function(code){if(delete pidsToProcess[parentPid],code!=0){Object.keys(pidsToProcess).length==0&&cb();return}allData.match(/\d+/g).forEach(function(pid){pid=parseInt(pid,10),tree[parentPid].push(pid),tree[pid]=[],pidsToProcess[pid]=1,buildProcessTree(pid,tree,pidsToProcess,spawnChildProcessesList,cb);});};ps.on("close",onClose);}}});var import_tree_kill=__toESM(require_tree_kill());var storybookProcess=null,getIsVitestStandaloneRun=()=>{try{return (undefined||process?.env).STORYBOOK!=="true"}catch{return !1}},isVitestStandaloneRun=getIsVitestStandaloneRun(),checkStorybookRunning=async storybookUrl=>{try{return (await fetch(`${storybookUrl}/iframe.html`,{method:"HEAD"})).ok}catch{return !1}},startStorybookIfNotRunning=async()=>{let storybookScript=process.env.__STORYBOOK_SCRIPT__,storybookUrl=process.env.__STORYBOOK_URL__;if(await checkStorybookRunning(storybookUrl)){nodeLogger.logger.verbose("Storybook is already running");return}nodeLogger.logger.verbose(`Starting Storybook with command: ${storybookScript}`);try{storybookProcess=child_process.spawn(storybookScript,[],{stdio:process.env.DEBUG==="storybook"?"pipe":"ignore",cwd:process.cwd(),shell:!0}),storybookProcess.on("error",error=>{throw nodeLogger.logger.verbose("Failed to start Storybook:"+error.message),error});}catch(error){throw nodeLogger.logger.verbose("Failed to start Storybook:"+error.message),error}},setup=async({config})=>{config.watch&&isVitestStandaloneRun&&await startStorybookIfNotRunning();},teardown=async()=>{storybookProcess&&(nodeLogger.logger.verbose("Stopping Storybook process"),await new Promise((resolve,reject)=>{storybookProcess?.pid&&(0, import_tree_kill.default)(storybookProcess.pid,"SIGTERM",error=>{if(error){nodeLogger.logger.error("Failed to stop Storybook process:"),reject(error);return}resolve();});}));};

exports.setup = setup;
exports.teardown = teardown;
