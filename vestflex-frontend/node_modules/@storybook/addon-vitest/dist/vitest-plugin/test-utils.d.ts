import { TestContext } from 'vitest';
import { ComposedStoryFn, ComponentAnnotations } from 'storybook/internal/types';

declare module '@vitest/browser/context' {
    interface BrowserCommands {
        getInitialGlobals: () => Promise<Record<string, any>>;
    }
}
/**
 * Converts a file URL to a file path, handling URL encoding
 *
 * @param url The file URL to convert (e.g. file:///path/to/file.js)
 * @returns The decoded file path
 */
declare const convertToFilePath: (url: string) => string;
declare const testStory: (exportName: string, story: ComposedStoryFn, meta: ComponentAnnotations, skipTags: string[]) => (context: TestContext & {
    story: ComposedStoryFn;
}) => Promise<void>;

export { convertToFilePath, testStory };
