'use strict';

var vitest = require('vitest');
var channels = require('storybook/internal/channels');

var ADDON_ID="storybook/interactions",PANEL_ID=`${ADDON_ID}/panel`;var transport={setHandler:vitest.vi.fn(),send:vitest.vi.fn()};globalThis.__STORYBOOK_ADDONS_CHANNEL__??=new channels.Channel({transport});var modifyErrorMessage=({task})=>{let meta=task.meta;if(task.type==="test"&&task.result?.state==="fail"&&meta.storyId&&task.result.errors?.[0]){let currentError=task.result.errors[0],storyUrl=`${undefined.__STORYBOOK_URL__}/?path=/story/${meta.storyId}&addonPanel=${PANEL_ID}`;currentError.message=`
\x1B[34mClick to debug the error directly in Storybook: ${storyUrl}\x1B[39m

${currentError.message}`;}};vitest.beforeAll(()=>{if(globalThis.globalProjectAnnotations)return globalThis.globalProjectAnnotations.beforeAll()});vitest.afterEach(modifyErrorMessage);

exports.modifyErrorMessage = modifyErrorMessage;
