import { vi, beforeAll, afterEach } from 'vitest';
import { Channel } from 'storybook/internal/channels';

var ADDON_ID="storybook/interactions",PANEL_ID=`${ADDON_ID}/panel`;var transport={setHandler:vi.fn(),send:vi.fn()};globalThis.__STORYBOOK_ADDONS_CHANNEL__??=new Channel({transport});var modifyErrorMessage=({task})=>{let meta=task.meta;if(task.type==="test"&&task.result?.state==="fail"&&meta.storyId&&task.result.errors?.[0]){let currentError=task.result.errors[0],storyUrl=`${import.meta.env.__STORYBOOK_URL__}/?path=/story/${meta.storyId}&addonPanel=${PANEL_ID}`;currentError.message=`
\x1B[34mClick to debug the error directly in Storybook: ${storyUrl}\x1B[39m

${currentError.message}`;}};beforeAll(()=>{if(globalThis.globalProjectAnnotations)return globalThis.globalProjectAnnotations.beforeAll()});afterEach(modifyErrorMessage);

export { modifyErrorMessage };
