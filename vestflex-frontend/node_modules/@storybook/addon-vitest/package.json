{"name": "@storybook/addon-vitest", "version": "9.0.15", "description": "Storybook addon for testing components", "keywords": ["storybook-addons", "addon-vitest", "vitest", "testing", "test"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/vitest", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/vitest"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "imports": {"#manager-store": {"storybook": "./src/manager-store.mock.ts", "default": "./src/manager-store.ts"}}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./vitest-plugin": {"types": "./dist/vitest-plugin/index.d.ts", "import": "./dist/vitest-plugin/index.mjs", "require": "./dist/vitest-plugin/index.js"}, "./internal/global-setup": {"types": "./dist/vitest-plugin/global-setup.d.ts", "import": "./dist/vitest-plugin/global-setup.mjs", "require": "./dist/vitest-plugin/global-setup.js"}, "./internal/setup-file": {"types": "./dist/vitest-plugin/setup-file.d.ts", "import": "./dist/vitest-plugin/setup-file.mjs"}, "./internal/test-utils": {"types": "./dist/vitest-plugin/test-utils.d.ts", "import": "./dist/vitest-plugin/test-utils.mjs", "require": "./dist/vitest-plugin/test-utils.js"}, "./internal/coverage-reporter": {"types": "./dist/node/coverage-reporter.d.ts", "import": "./dist/node/coverage-reporter.mjs", "require": "./dist/node/coverage-reporter.js"}, "./manager": "./dist/manager.js", "./preset": "./dist/preset.js", "./postinstall": "./dist/postinstall.js", "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/index.d.ts"]}}, "files": ["dist/**/*", "templates/**/*", "!dist/dummy.*", "README.md", "*.mjs", "*.js", "*.cjs", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/addon-bundle.ts"}, "dependencies": {"@storybook/global": "^5.0.0", "@storybook/icons": "^1.4.0", "prompts": "^2.4.0", "ts-dedent": "^2.2.0"}, "devDependencies": {"@types/istanbul-lib-report": "^3.0.3", "@types/micromatch": "^4.0.0", "@types/node": "^22.0.0", "@types/semver": "^7", "@vitest/browser": "^3.2.4", "@vitest/runner": "^3.2.4", "boxen": "^8.0.1", "es-toolkit": "^1.36.0", "execa": "^8.0.1", "find-up": "^7.0.0", "istanbul-lib-report": "^3.0.1", "micromatch": "^4.0.8", "pathe": "^1.1.2", "picocolors": "^1.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "semver": "^7.6.3", "sirv": "^2.0.4", "slash": "^5.0.0", "tinyglobby": "^0.2.10", "tree-kill": "^1.2.2", "ts-dedent": "^2.2.0", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "peerDependencies": {"@vitest/browser": "^3.0.0", "@vitest/runner": "^3.0.0", "storybook": "^9.0.15", "vitest": "^3.0.0"}, "peerDependenciesMeta": {"@vitest/browser": {"optional": true}, "@vitest/runner": {"optional": true}, "vitest": {"optional": true}}, "publishConfig": {"access": "public"}, "bundler": {"exportEntries": ["./src/index.ts", "./src/vitest-plugin/test-utils.ts", "./src/vitest-plugin/setup-file.ts"], "managerEntries": ["./src/manager.tsx"], "nodeEntries": ["./src/preset.ts", "./src/postinstall.ts", {"file": "./src/dummy.ts", "formats": ["esm"]}, {"file": "./src/node/vitest.ts", "formats": ["esm", "cjs"]}, {"file": "./src/node/coverage-reporter.ts", "formats": ["esm", "cjs"]}, {"file": "./src/vitest-plugin/index.ts", "formats": ["cjs", "esm"]}, {"file": "./src/vitest-plugin/global-setup.ts", "formats": ["cjs", "esm"]}]}, "storybook": {"displayName": "Test", "unsupportedFrameworks": ["react-native"], "icon": "https://user-images.githubusercontent.com/263385/101991666-479cc600-3c7c-11eb-837b-be4e5ffa1bb8.png"}}