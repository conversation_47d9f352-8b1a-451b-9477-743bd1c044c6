import { __export } from './chunk-CEH6MNVV.mjs';
import { addons, waitForAnimations, definePreview } from 'storybook/preview-api';
import { expect } from 'storybook/test';
import { ElementA11yParameterError } from 'storybook/internal/preview-errors';
import { global } from '@storybook/global';

var preview_exports={};__export(preview_exports,{afterEach:()=>afterEach,initialGlobals:()=>initialGlobals,parameters:()=>parameters});var ADDON_ID="storybook/a11y",PANEL_ID=`${ADDON_ID}/panel`,PARAM_KEY="a11y",RESULT=`${ADDON_ID}/result`,REQUEST=`${ADDON_ID}/request`,RUNNING=`${ADDON_ID}/running`,ERROR=`${ADDON_ID}/error`,MANUAL=`${ADDON_ID}/manual`,SELECT=`${ADDON_ID}/select`;var EVENTS={RESULT,REQUEST,RUNNING,ERROR,MANUAL,SELECT};var{document}=global,withLinkPaths=(results,storyId)=>{let pathname=document.location.pathname.replace(/iframe\.html$/,""),enhancedResults={...results};return ["incomplete","passes","violations"].forEach(key=>{Array.isArray(results[key])&&(enhancedResults[key]=results[key].map(result=>({...result,nodes:result.nodes.map((node,index)=>{let id=`${key}.${result.id}.${index+1}`,linkPath=`${pathname}?path=/story/${storyId}&addonPanel=${PANEL_ID}&a11ySelection=${id}`;return {id,...node,linkPath}})})));}),enhancedResults};var{document:document2}=global,channel=addons.getChannel(),DEFAULT_PARAMETERS={config:{},options:{}},DISABLED_RULES=["region"],queue=[],isRunning=!1,runNext=async()=>{if(queue.length===0){isRunning=!1;return}isRunning=!0;let next=queue.shift();next&&await next(),runNext();},run=async(input=DEFAULT_PARAMETERS,storyId)=>{let axe=(await import('axe-core'))?.default||globalThis.axe,{config={},options={}}=input;if(input.element)throw new ElementA11yParameterError;let context={include:document2?.body,exclude:[".sb-wrapper","#storybook-docs","#storybook-highlights-root"]};if(input.context){let hasInclude=typeof input.context=="object"&&"include"in input.context&&input.context.include!==void 0,hasExclude=typeof input.context=="object"&&"exclude"in input.context&&input.context.exclude!==void 0;hasInclude?context.include=input.context.include:!hasInclude&&!hasExclude&&(context.include=input.context),hasExclude&&(context.exclude=context.exclude.concat(input.context.exclude));}axe.reset();let configWithDefault={...config,rules:[...DISABLED_RULES.map(id=>({id,enabled:!1})),...config?.rules??[]]};return axe.configure(configWithDefault),new Promise((resolve,reject)=>{let highlightsRoot=document2?.getElementById("storybook-highlights-root");highlightsRoot&&(highlightsRoot.style.display="none");let task=async()=>{try{let result=await axe.run(context,options),resultWithLinks=withLinkPaths(result,storyId);resolve(resultWithLinks);}catch(error){reject(error);}};queue.push(task),isRunning||runNext(),highlightsRoot&&(highlightsRoot.style.display="");})};channel.on(EVENTS.MANUAL,async(storyId,input=DEFAULT_PARAMETERS)=>{try{await waitForAnimations();let result=await run(input,storyId),resultJson=JSON.parse(JSON.stringify(result));channel.emit(EVENTS.RESULT,resultJson,storyId);}catch(error){channel.emit(EVENTS.ERROR,error);}});function getIsVitestStandaloneRun(){try{return import.meta.env.VITEST_STORYBOOK==="false"}catch{return !1}}var vitestMatchersExtended=!1,afterEach=async({id:storyId,reporting,parameters:parameters2,globals,viewMode})=>{let a11yParameter=parameters2.a11y,a11yGlobals=globals.a11y,shouldRunEnvironmentIndependent=a11yParameter?.disable!==!0&&a11yParameter?.test!=="off"&&a11yGlobals?.manual!==!0,getMode=()=>{switch(a11yParameter?.test){case"todo":return "warning";case"error":default:return "failed"}};if(shouldRunEnvironmentIndependent&&viewMode==="story")try{let result=await run(a11yParameter,storyId);if(result){let hasViolations=(result?.violations.length??0)>0;if(reporting.addReport({type:"a11y",version:1,result,status:hasViolations?getMode():"passed"}),getIsVitestStandaloneRun()&&hasViolations&&getMode()==="failed"){if(!vitestMatchersExtended){let{toHaveNoViolations}=await import('./matchers-TIWVOQAP.mjs');expect.extend({toHaveNoViolations}),vitestMatchersExtended=!0;}expect(result).toHaveNoViolations();}}}catch(e){if(reporting.addReport({type:"a11y",version:1,result:{error:e},status:"failed"}),getIsVitestStandaloneRun())throw e}},initialGlobals={a11y:{manual:!1}},parameters={a11y:{test:"todo"}};var index_default=()=>definePreview(preview_exports);

export { PARAM_KEY, index_default as default };
