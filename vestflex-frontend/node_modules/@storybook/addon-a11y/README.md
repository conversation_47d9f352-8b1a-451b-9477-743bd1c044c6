# Storybook Accessibility Addon

The @storybook/addon-a11y package provides accessibility testing for Storybook stories. It uses axe-core to run the tests.

## Getting Started

### Add the addon to an existing Storybook

```bash
npx storybook add @storybook/addon-a11y
```

[More on getting started with the accessibility addon](https://storybook.js.org/docs/writing-tests/accessibility-testing#accessibility-checks-with-a11y-addon)
