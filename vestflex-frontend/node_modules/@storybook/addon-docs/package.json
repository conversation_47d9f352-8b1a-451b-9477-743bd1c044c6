{"name": "@storybook/addon-docs", "version": "9.0.15", "description": "Document component usage and properties in Markdown", "keywords": ["addon", "notes", "documentation", "storybook", "essentials", "organize"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/docs", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/docs"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./preview": {"types": "./dist/preview.d.ts", "import": "./dist/preview.mjs", "require": "./dist/preview.js"}, "./preset": "./dist/preset.js", "./blocks": {"types": "./dist/blocks.d.ts", "import": "./dist/blocks.mjs", "require": "./dist/blocks.js"}, "./dist/preview": {"types": "./dist/preview.d.ts", "import": "./dist/preview.mjs", "require": "./dist/preview.js"}, "./dist/preset": "./dist/preset.js", "./dist/shims/mdx-react-shim": {"types": "./dist/shims/mdx-react-shim.d.ts", "import": "./dist/shims/mdx-react-shim.mjs", "require": "./dist/shims/mdx-react-shim.js"}, "./mdx-react-shim": {"types": "./dist/shims/mdx-react-shim.d.ts", "import": "./dist/shims/mdx-react-shim.mjs", "require": "./dist/shims/mdx-react-shim.js"}, "./mdx-loader": "./dist/mdx-loader.js", "./svelte/HOC.svelte": "./svelte/HOC.svelte", "./ember": "./ember/index.js", "./ember/index.js": "./ember/index.js", "./angular": "./angular/index.js", "./angular/index.js": "./angular/index.js", "./web-components/index.js": "./web-components/index.js", "./package.json": "./package.json", "./manager": "./dist/manager.js"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/index.d.ts"], "angular": ["angular/index.d.ts"], "blocks": ["dist/blocks.d.ts"], "ember": ["ember/index.d.ts"], "preview": ["dist/preview.d.ts"]}}, "files": ["dist/**/*", "angular/**/*", "common/**/*", "ember/**/*", "html/**/*", "svelte/**/*", "react/**/*", "vue/**/*", "web-components/**/*", "lit/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/addon-bundle.ts"}, "dependencies": {"@mdx-js/react": "^3.0.0", "@storybook/csf-plugin": "9.0.15", "@storybook/icons": "^1.2.12", "@storybook/react-dom-shim": "9.0.15", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "ts-dedent": "^2.0.0"}, "devDependencies": {"@mdx-js/mdx": "^3.0.0", "@rollup/pluginutils": "^5.0.2", "@types/color-convert": "^2.0.0", "@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "color-convert": "^2.0.1", "es-toolkit": "^1.36.0", "github-slugger": "^2.0.0", "markdown-to-jsx": "^7.7.2", "memoizerific": "^1.11.3", "polished": "^4.2.2", "react": "^18.2.0", "react-colorful": "^5.1.2", "react-dom": "^18.2.0", "rehype-external-links": "^3.0.0", "rehype-slug": "^6.0.0", "telejson": "8.0.0", "tocbot": "^4.20.1", "typescript": "^5.8.3", "vite": "^6.2.5"}, "peerDependencies": {"storybook": "^9.0.15"}, "publishConfig": {"access": "public"}, "bundler": {"previewEntries": ["./src/preview.ts"], "exportEntries": ["./src/index.ts", "./src/blocks.ts", "./src/shims/mdx-react-shim.ts"], "nodeEntries": ["./src/mdx-loader.ts", "./src/preset.ts"], "managerEntries": ["./src/manager.tsx"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae16", "storybook": {"displayName": "Docs", "icon": "https://user-images.githubusercontent.com/263385/101991672-48355c80-3c7c-11eb-82d9-95fa12438f64.png", "unsupportedFrameworks": ["react-native"]}}