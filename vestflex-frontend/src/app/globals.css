@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Variáveis CSS para temas - Vestflex Design System */
:root {
  /* Cores do tema claro */
  --background: 250 250 250; /* neutral-50 */
  --foreground: 23 23 23; /* neutral-900 */
  --card: 245 245 245; /* neutral-100 */
  --card-foreground: 23 23 23; /* neutral-900 */
  --popover: 250 250 250; /* neutral-50 */
  --popover-foreground: 23 23 23; /* neutral-900 */
  --primary: 14 165 233; /* primary-500 */
  --primary-foreground: 250 250 250; /* neutral-50 */
  --secondary: 243 115 22; /* secondary-500 */
  --secondary-foreground: 250 250 250; /* neutral-50 */
  --muted: 229 229 229; /* neutral-200 */
  --muted-foreground: 82 82 82; /* neutral-600 */
  --accent: 34 197 94; /* accent-500 */
  --accent-foreground: 250 250 250; /* neutral-50 */
  --destructive: 239 68 68; /* error-500 */
  --destructive-foreground: 250 250 250; /* neutral-50 */
  --border: 212 212 212; /* neutral-300 */
  --input: 212 212 212; /* neutral-300 */
  --ring: 14 165 233; /* primary-500 */
  --radius: 0.5rem;
}

.dark {
  /* Cores do tema escuro */
  --background: 10 10 10; /* neutral-950 */
  --foreground: 250 250 250; /* neutral-50 */
  --card: 23 23 23; /* neutral-900 */
  --card-foreground: 250 250 250; /* neutral-50 */
  --popover: 10 10 10; /* neutral-950 */
  --popover-foreground: 250 250 250; /* neutral-50 */
  --primary: 56 189 248; /* primary-400 */
  --primary-foreground: 23 23 23; /* neutral-900 */
  --secondary: 251 146 60; /* secondary-400 */
  --secondary-foreground: 23 23 23; /* neutral-900 */
  --muted: 38 38 38; /* neutral-800 */
  --muted-foreground: 163 163 163; /* neutral-400 */
  --accent: 74 222 128; /* accent-400 */
  --accent-foreground: 23 23 23; /* neutral-900 */
  --destructive: 248 113 113; /* error-400 */
  --destructive-foreground: 23 23 23; /* neutral-900 */
  --border: 64 64 64; /* neutral-700 */
  --input: 64 64 64; /* neutral-700 */
  --ring: 56 189 248; /* primary-400 */
}

/* Reset e estilos base */
* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: 'Inter', system-ui, sans-serif;
  font-feature-settings: 'cv11', 'ss01';
  font-variation-settings: 'opsz' 32;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar customizada */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* Seleção de texto */
::selection {
  background-color: hsl(var(--primary) / 0.2);
  color: hsl(var(--primary-foreground));
}

/* Focus styles */
.focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Animações globais */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Utilitários para acessibilidade */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Estilos para elementos de formulário */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
textarea,
select {
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="search"]:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

/* Estilos para botões */
button {
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Estilos para links */
a {
  color: hsl(var(--primary));
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

a:hover {
  color: hsl(var(--primary) / 0.8);
  text-decoration: underline;
}

/* Estilos para código */
code {
  font-family: 'JetBrains Mono', monospace;
  background-color: hsl(var(--muted));
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

pre {
  background-color: hsl(var(--muted));
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
}

pre code {
  background-color: transparent;
  padding: 0;
}

/* Estilos para tabelas */
table {
  border-collapse: collapse;
  width: 100%;
}

th,
td {
  border: 1px solid hsl(var(--border));
  padding: 0.5rem;
  text-align: left;
}

th {
  background-color: hsl(var(--muted));
  font-weight: 600;
}

/* Estilos para imagens */
img {
  max-width: 100%;
  height: auto;
}

/* Estilos para vídeos */
video {
  max-width: 100%;
  height: auto;
}
