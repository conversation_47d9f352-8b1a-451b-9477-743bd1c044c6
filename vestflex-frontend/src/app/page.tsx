import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, T<PERSON>dingU<PERSON>, <PERSON>, ChevronRight } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export default function Home() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-primary-50 via-secondary-50 to-accent-50 dark:from-primary-950 dark:via-secondary-950 dark:to-accent-950">
        <div className="container px-4 py-24 mx-auto max-w-7xl">
          <div className="grid gap-12 lg:grid-cols-2 lg:gap-8 items-center">
            <div className="flex flex-col justify-center space-y-8">
              <div className="space-y-4">
                <h1 className="text-4xl font-bold tracking-tight text-foreground sm:text-5xl xl:text-6xl">
                  Sua jornada para o
                  <span className="text-transparent bg-clip-text bg-gradient-primary"> vestibular </span>
                  começa aqui
                </h1>
                <p className="max-w-[600px] text-muted-foreground md:text-xl">
                  A plataforma que combina o melhor do YouTube, TikTok e Instagram para criar uma experiência única de aprendizado. Conteúdo personalizado com IA para sua aprovação.
                </p>
              </div>
              <div className="flex flex-col gap-4 sm:flex-row">
                <Button size="lg" className="text-base">
                  <Play className="w-5 h-5 mr-2" />
                  Começar agora
                </Button>
                <Button variant="outline" size="lg" className="text-base">
                  <BookOpen className="w-5 h-5 mr-2" />
                  Explorar conteúdos
                </Button>
              </div>
              <div className="flex items-center gap-8 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span>+50k estudantes</span>
                </div>
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4 fill-current text-warning-500" />
                  <span>4.9/5 avaliação</span>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="relative z-10 grid gap-4 grid-cols-2">
                <Card className="transform rotate-3 hover:rotate-6 transition-transform">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">Matemática</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">PF</AvatarFallback>
                      </Avatar>
                      <span className="text-xs text-muted-foreground">Prof. Felipe</span>
                    </div>
                  </CardContent>
                </Card>
                <Card className="transform -rotate-2 hover:-rotate-4 transition-transform mt-8">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">Física</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">AM</AvatarFallback>
                      </Avatar>
                      <span className="text-xs text-muted-foreground">Ana Maria</span>
                    </div>
                  </CardContent>
                </Card>
                <Card className="transform rotate-1 hover:rotate-3 transition-transform -mt-4">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">Português</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">CS</AvatarFallback>
                      </Avatar>
                      <span className="text-xs text-muted-foreground">Carlos Silva</span>
                    </div>
                  </CardContent>
                </Card>
                <Card className="transform -rotate-1 hover:-rotate-2 transition-transform">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">História</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">MR</AvatarFallback>
                      </Avatar>
                      <span className="text-xs text-muted-foreground">Maria Rosa</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-background">
        <div className="container px-4 mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl mb-4">
              Por que escolher a Vestflex?
            </h2>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Combinamos tecnologia de ponta com metodologia pedagógica comprovada para acelerar seu aprendizado.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <Card className="border-2 hover:border-primary-200 dark:hover:border-primary-800 transition-colors">
              <CardHeader>
                <div className="w-12 h-12 rounded-lg bg-primary-100 dark:bg-primary-900 flex items-center justify-center mb-4">
                  <TrendingUp className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                </div>
                <CardTitle>IA Personalizada</CardTitle>
                <CardDescription>
                  Algoritmos inteligentes analisam seus gaps de conhecimento e criam um plano de estudos único para você.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-2 hover:border-secondary-200 dark:hover:border-secondary-800 transition-colors">
              <CardHeader>
                <div className="w-12 h-12 rounded-lg bg-secondary-100 dark:bg-secondary-900 flex items-center justify-center mb-4">
                  <Play className="w-6 h-6 text-secondary-600 dark:text-secondary-400" />
                </div>
                <CardTitle>Conteúdo Dinâmico</CardTitle>
                <CardDescription>
                  Vídeos, shorts e posts interativos criados por professores especialistas e estudantes aprovados.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-2 hover:border-accent-200 dark:hover:border-accent-800 transition-colors">
              <CardHeader>
                <div className="w-12 h-12 rounded-lg bg-accent-100 dark:bg-accent-900 flex items-center justify-center mb-4">
                  <BookOpen className="w-6 h-6 text-accent-600 dark:text-accent-400" />
                </div>
                <CardTitle>BNCC Completa</CardTitle>
                <CardDescription>
                  Todo o conteúdo organizado seguindo a Base Nacional Comum Curricular para uma preparação completa.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-r from-primary-600 to-secondary-600 text-white">
        <div className="container px-4 mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
            Pronto para transformar seus estudos?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Junte-se a milhares de estudantes que já estão usando a Vestflex para alcançar seus objetivos.
          </p>
          <Button size="lg" variant="secondary" className="text-base">
            Começar gratuitamente
            <ChevronRight className="w-5 h-5 ml-2" />
          </Button>
        </div>
      </section>
    </div>
  );
}
