import * as React from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"

interface LogoProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "sm" | "md" | "lg" | "xl"
  showText?: boolean
  variant?: "default" | "white" | "dark"
}

const sizeClasses = {
  sm: "h-6 w-6",
  md: "h-8 w-8", 
  lg: "h-12 w-12",
  xl: "h-16 w-16"
}

const textSizeClasses = {
  sm: "text-lg",
  md: "text-xl",
  lg: "text-2xl", 
  xl: "text-3xl"
}

const Logo = React.forwardRef<HTMLDivElement, LogoProps>(
  ({ className, size = "md", showText = true, variant = "default", ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("flex items-center gap-2", className)}
        {...props}
      >
        <div className={cn("relative", sizeClasses[size])}>
          <Image
            src="/logo.png"
            alt="Vestflex"
            fill
            className="object-contain"
            priority
          />
        </div>
        {showText && (
          <span 
            className={cn(
              "font-bold tracking-tight",
              textSizeClasses[size],
              variant === "white" && "text-white",
              variant === "dark" && "text-neutral-900",
              variant === "default" && "text-primary-600 dark:text-primary-400"
            )}
          >
            Vestflex
          </span>
        )}
      </div>
    )
  }
)

Logo.displayName = "Logo"

export { Logo }
