import type { Config } from "tailwindcss";
import { colors, typography, spacing, borderRadius, shadows, screens } from "./src/lib/design-tokens";

const config: Config = {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/stories/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // Cores customizadas da Vestflex
        primary: colors.primary,
        secondary: colors.secondary,
        accent: colors.accent,
        neutral: colors.neutral,
        success: colors.success,
        warning: colors.warning,
        error: colors.error,
        info: colors.info,
        
        // Cores semânticas para temas
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
      },
      
      fontFamily: typography.fontFamily,
      fontSize: typography.fontSize,
      fontWeight: typography.fontWeight,
      
      spacing: spacing,
      borderRadius: borderRadius,
      boxShadow: shadows,
      screens: screens,
      
      // Animações customizadas
      animation: {
        "fade-in": "fadeIn 0.5s ease-in-out",
        "slide-up": "slideUp 0.3s ease-out",
        "slide-down": "slideDown 0.3s ease-out",
        "scale-in": "scaleIn 0.2s ease-out",
        "bounce-gentle": "bounceGentle 0.6s ease-out",
      },
      
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        slideUp: {
          "0%": { transform: "translateY(10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        slideDown: {
          "0%": { transform: "translateY(-10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        scaleIn: {
          "0%": { transform: "scale(0.95)", opacity: "0" },
          "100%": { transform: "scale(1)", opacity: "1" },
        },
        bounceGentle: {
          "0%, 20%, 53%, 80%, 100%": { transform: "translate3d(0,0,0)" },
          "40%, 43%": { transform: "translate3d(0,-8px,0)" },
          "70%": { transform: "translate3d(0,-4px,0)" },
          "90%": { transform: "translate3d(0,-2px,0)" },
        },
      },
      
      // Gradientes customizados
      backgroundImage: {
        "gradient-primary": `linear-gradient(135deg, ${colors.primary[500]} 0%, ${colors.primary[600]} 100%)`,
        "gradient-secondary": `linear-gradient(135deg, ${colors.secondary[500]} 0%, ${colors.secondary[600]} 100%)`,
        "gradient-accent": `linear-gradient(135deg, ${colors.accent[500]} 0%, ${colors.accent[600]} 100%)`,
        "gradient-hero": `linear-gradient(135deg, ${colors.primary[500]} 0%, ${colors.secondary[500]} 50%, ${colors.accent[500]} 100%)`,
      },
      
      // Tipografia customizada
      typography: {
        DEFAULT: {
          css: {
            maxWidth: "none",
            color: colors.neutral[700],
            lineHeight: "1.7",
            h1: {
              color: colors.neutral[900],
              fontWeight: typography.fontWeight.bold,
            },
            h2: {
              color: colors.neutral[900],
              fontWeight: typography.fontWeight.semibold,
            },
            h3: {
              color: colors.neutral[800],
              fontWeight: typography.fontWeight.semibold,
            },
            strong: {
              color: colors.neutral[900],
              fontWeight: typography.fontWeight.semibold,
            },
            a: {
              color: colors.primary[600],
              textDecoration: "none",
              "&:hover": {
                color: colors.primary[700],
                textDecoration: "underline",
              },
            },
            code: {
              color: colors.primary[600],
              backgroundColor: colors.neutral[100],
              padding: "0.25rem 0.375rem",
              borderRadius: borderRadius.sm,
              fontWeight: typography.fontWeight.medium,
            },
            blockquote: {
              borderLeftColor: colors.primary[500],
              color: colors.neutral[600],
            },
          },
        },
        dark: {
          css: {
            color: colors.neutral[300],
            h1: {
              color: colors.neutral[100],
            },
            h2: {
              color: colors.neutral[100],
            },
            h3: {
              color: colors.neutral[200],
            },
            strong: {
              color: colors.neutral[100],
            },
            a: {
              color: colors.primary[400],
              "&:hover": {
                color: colors.primary[300],
              },
            },
            code: {
              color: colors.primary[400],
              backgroundColor: colors.neutral[800],
            },
            blockquote: {
              borderLeftColor: colors.primary[400],
              color: colors.neutral[400],
            },
          },
        },
      },
    },
  },
  plugins: [
    require("@tailwindcss/typography"),
  ],
};

export default config;
