{"name": "vestflex-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tailwindcss/typography": "^0.5.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.3.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@eslint/eslintrc": "^3", "@storybook/addon-a11y": "^9.0.15", "@storybook/addon-docs": "^9.0.15", "@storybook/addon-onboarding": "^9.0.15", "@storybook/addon-vitest": "^9.0.15", "@storybook/nextjs-vite": "^9.0.15", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "eslint": "^9", "eslint-config-next": "15.3.4", "eslint-plugin-storybook": "^9.0.15", "playwright": "^1.53.2", "storybook": "^9.0.15", "tailwindcss": "^4", "typescript": "^5", "vitest": "^3.2.4"}}